package com.saida.services.common.tools;

import com.saida.services.exception.BizRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.Date;

@Slf4j
public class SmsUtil {

    private static final String USER_ID = "400035";
    private static final String PWD = "700277";
    private static final String PATH = "http://api.shumi365.com:8090/sms/send.do";

    public static String sendSms(String phone, String msg) {
        log.info("发送短信: phone:{},msg:{}", phone, msg);
        String timestamp = getTimestamp();
        String sig = getMD5(PWD, timestamp).toUpperCase();

        OutputStreamWriter out;
        BufferedReader br;
        StringBuilder result = new StringBuilder();
        try {
            URL url = new URL(PATH);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setDoInput(true);// 设置是否允许数据写入
            connection.setDoOutput(true);// 设置是否允许参数数据输出
            connection.setConnectTimeout(5000);// 设置链接响应时间
            connection.setReadTimeout(10000);// 设置参数读取时间
            connection.setRequestProperty("Content-type", "application/x-www-form-urlencoded");
            // 提交请求
            out = new OutputStreamWriter(connection.getOutputStream(), StandardCharsets.UTF_8);
            String args = "userid=" + USER_ID +
                    "&pwd=" + sig +
                    "&timespan=" + timestamp +
                    "&mobile=" + phone +
                    "&msgfmt=UTF-8&content=" + getBase64Code(msg);
            out.write(args);
            out.flush();
            // 读取返回参数
            br = new BufferedReader(new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8));
            String temp = "";
            while ((temp = br.readLine()) != null) {
                result.append(temp);
            }
        } catch (Exception e) {
            throw new BizRuntimeException("发送短信失败");
        }
        return result.toString();
    }

    // 获取时间戳
    public static String getTimestamp() {
        return new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
    }

    // 创建验证码
    public static String smsCode() {
        return (int) ((Math.random() * 9 + 1) * 100000) + "";
    }

    // sing签名
    public static String getMD5(String pwd, String timestamp) {

        StringBuilder result = new StringBuilder();
        String source = pwd + timestamp;
        // 获取某个类的实例
        try {
            MessageDigest digest = MessageDigest.getInstance("MD5");
            // 要进行加密的东西
            byte[] bytes = digest.digest(source.getBytes());
            for (byte b : bytes) {
                String hex = Integer.toHexString(b & 0xff);
                if (hex.length() == 1) {
                    result.append("0").append(hex);
                } else {
                    result.append(hex);
                }
            }
        } catch (NoSuchAlgorithmException e) {
            log.error("MD5加密失败", e);
        }
        return result.toString();
    }

    public static String getBase64Code(String string) {
        Base64 base64 = new Base64();
        String base64Sign = "";
        base64Sign = base64.encodeToString(string.getBytes(StandardCharsets.UTF_8));
        return base64Sign;
    }
}