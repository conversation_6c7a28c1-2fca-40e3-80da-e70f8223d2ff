package com.saida.services.common.tools;

import lombok.extern.slf4j.Slf4j;

/**
 * 地理坐标距离计算工具类
 * 用于计算两个经纬度坐标点之间的距离
 *
 * <AUTHOR>
 */
@Slf4j
public class GeoDistanceUtil {

    /**
     * 地球半径（米）
     */
    private static final double EARTH_RADIUS_METERS = 6371000.0;

    /**
     * 角度转弧度的转换因子
     */
    private static final double DEGREES_TO_RADIANS = Math.PI / 180.0;

    /**
     * 使用Haversine公式计算两个经纬度坐标点之间的距离
     *
     * @param lat1 第一个点的纬度
     * @param lon1 第一个点的经度
     * @param lat2 第二个点的纬度
     * @param lon2 第二个点的经度
     * @return 两点之间的距离（米）
     *
     * @throws IllegalArgumentException 当输入的经纬度超出有效范围时抛出
     * 示例用法：
     * <pre>
     * // 计算北京天安门和上海外滩之间的距离
     * double distance = GeoDistanceUtil.calculateDistance(39.9042, 116.4074, 31.2304, 121.4737);
     * System.out.println("距离: " + distance + " 米");
     * </pre>
     */
    public static double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
        // 验证输入参数
        if (validateCoordinates(lat1, lon1) || validateCoordinates(lat2, lon2)) {
            return -1;
        }
        // 如果两个点相同，距离为0
        if (lat1 == lat2 && lon1 == lon2) {
            return 0.0;
        }
        // 将角度转换为弧度
        double lat1Rad = lat1 * DEGREES_TO_RADIANS;
        double lon1Rad = lon1 * DEGREES_TO_RADIANS;
        double lat2Rad = lat2 * DEGREES_TO_RADIANS;
        double lon2Rad = lon2 * DEGREES_TO_RADIANS;
        // 计算纬度和经度的差值
        double deltaLat = lat2Rad - lat1Rad;
        double deltaLon = lon2Rad - lon1Rad;
        // Haversine公式
        double a = Math.sin(deltaLat / 2) * Math.sin(deltaLat / 2) +
                Math.cos(lat1Rad) * Math.cos(lat2Rad) *
                        Math.sin(deltaLon / 2) * Math.sin(deltaLon / 2);

        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        // 计算距离
        return EARTH_RADIUS_METERS * c;
    }


    /**
     * 验证经纬度坐标是否在有效范围内
     *
     * @param latitude 纬度 (-90 到 90)
     * @param longitude 经度 (-180 到 180)
     * @throws IllegalArgumentException 当坐标超出有效范围时抛出
     */
    private static boolean validateCoordinates(double latitude, double longitude) {
        if (latitude < -90.0 || latitude > 90.0) {
            log.error("纬度必须在 -90 到 90 度之间，当前值: {}", latitude);
            return true;
        }
        if (longitude < -180.0 || longitude > 180.0) {
            log.error("经度必须在 -180 到 180 度之间，当前值: {}", longitude);
            return true;
        }
        return false;
    }
}
