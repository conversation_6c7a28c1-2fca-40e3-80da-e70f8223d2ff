package com.saida.services.common.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.DynamicTableNameInnerInterceptor;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.saida.services.common.interceptor.PaginationInnerEscapeInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@Slf4j
@EnableTransactionManagement
@MapperScan("com.saida.services.**.mapper")
@Configuration
public class VLinkerBatisPlusConfig {

    /**
     * 分页插件
     */
    public PaginationInnerEscapeInterceptor paginationInnerEscapeInterceptor() {
        PaginationInnerEscapeInterceptor paginationInnerEscapeInterceptor = new PaginationInnerEscapeInterceptor();

        paginationInnerEscapeInterceptor.setMaxLimit(1000L);
        paginationInnerEscapeInterceptor.setDbType(DbType.MYSQL);
        paginationInnerEscapeInterceptor.setOverflow(false);
        return paginationInnerEscapeInterceptor;
    }

    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();

        interceptor.addInnerInterceptor(paginationInnerEscapeInterceptor());

        // interceptor.addInnerInterceptor(paginationInterceptor());

        DynamicTableNameInnerInterceptor dynamicTableNameMonthInterceptor = new DynamicTableNameInnerInterceptor();
        dynamicTableNameMonthInterceptor.setTableNameHandler(
                // 可以传多个表名参数，指定哪些表使用MonthTableNameHandler处理表名称
                new DaysTableNameHandler("ops_device_alarm")
        );
        // 以拦截器的方式处理表名称
        interceptor.addInnerInterceptor(dynamicTableNameMonthInterceptor);
        return interceptor;
    }

    @Bean
    public Jackson2ObjectMapperBuilderCustomizer customizer() {
        return builder -> builder.featuresToEnable(SerializationFeature.WRITE_ENUMS_USING_TO_STRING);
    }
}