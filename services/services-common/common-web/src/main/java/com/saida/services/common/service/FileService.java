package com.saida.services.common.service;

import cn.hutool.core.lang.UUID;
import cn.hutool.crypto.digest.MD5;
import com.saida.services.common.base.DtoResult;
import com.saida.services.common.config.S3Config;
import com.saida.services.common.config.oss.OSSBean;
import com.saida.services.common.config.oss.impl.DefS3Impl;
import com.saida.services.entities.pojo.FileModel;
import com.saida.services.exception.BizRuntimeException;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.http.MediaTypeFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.URL;
import java.net.URLConnection;
import java.nio.file.Files;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * description:
 * author zhangjc
 * date 2022/12/12 15:54
 */
@Getter
@Slf4j
@Component
public class FileService {

    @Value("${sysconfig.uploadPath:#{null}}")
    private String uploadPath;

    @Autowired(required = false)
    private S3Config s3Config;

    public static final char SEPARATOR = '/';
    public static final String PREFIX = "file/";
    public static final String HOST = "{{HOST}}";

    @Resource
    private DefS3Impl defS3;


    public static BufferedImage downloadImage(String imgSrc) {
        if (StringUtils.isEmpty(imgSrc)) {
            log.error("图片为空：{}", imgSrc);
            return null;
        }
        try {
            URL url = new URL(imgSrc);
            return ImageIO.read(url);
        } catch (IOException e) {
            log.error("下载图片失败：{}", imgSrc, e);
            return null;
        }
    }

    public DtoResult<FileModel> uploadToS3(String fileUrl, String ossObjKey) {
        if (StringUtils.isEmpty(fileUrl)) {
            log.error("uploadToS3 -> 图片 URL 为空");
            return DtoResult.error("图片 URL 不能为空");
        }
        try {
            byte[] bytes = downloadFileAsByteArrayByCount(fileUrl, 5);
            if (bytes == null) {
                log.error("uploadToS3 -> 下载图片失败 bytes是空的 ：{}", fileUrl);
                return DtoResult.error("下载图片失败");
            }
            if (bytes.length == 0) {
                log.error("uploadToS3 -> 图片为空 bytes.length == 0：{}", fileUrl);
                return DtoResult.error("图片为空");
            }
            return uploadByte(bytes, ossObjKey);
        } catch (Exception e) {
            log.error("uploadToS3 -> 下载或上传文件失败，URL: {}", fileUrl, e);
            return DtoResult.error("下载或上传失败：" + e.getMessage());
        }
    }


    /**
     * 上传到S3
     * ossObjKey 为文件名称  也支持文件夹
     * module 为第一层文件夹
     */
    public DtoResult<FileModel> uploadToS3(InputStream inputStream, String ossObjKey, Long size, String module) {
        if (StringUtils.isEmpty(ossObjKey)) {
            return DtoResult.error("文件名为空");
        }
        if (s3Config == null) {
            return DtoResult.error("s3配置不存在！请联系运维人员");
        }
        OSSBean ossBean = OSSBean.builder()
                .region(s3Config.getRegion())
                .accessKey(s3Config.getAccessKey())
                .secretKey(s3Config.getSecretKey())
                .endPoint(s3Config.getEndPoint())
                .returnPoint(s3Config.getReturnPoint())
                .bucket(s3Config.getBucket())
                .build();
        FileModel fileModel = new FileModel();
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        try {
            // 将 InputStream 读取到内存
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                baos.write(buffer, 0, bytesRead);
            }
            byte[] fileBytes = baos.toByteArray();// 创建新的 InputStream 用于上传
            InputStream uploadStream = new ByteArrayInputStream(fileBytes);
            defS3.upload(ossBean, uploadStream, size, module + SEPARATOR + ossObjKey, ossObjKey);
            fileModel.setMd5(MD5.create().digestHex(fileBytes));
            fileModel.setUrl(ossBean.getReturnPoint() + SEPARATOR + ossBean.getBucket() + SEPARATOR + module + SEPARATOR + ossObjKey);
            fileModel.setSize(size);
            return DtoResult.ok(fileModel);
        } catch (Exception e) {
            return DtoResult.error("上传失败");
        } finally {
            try {
                baos.close();
            } catch (IOException ignored) {
            }
        }
    }

    public DtoResult<FileModel> uploadToS3(File file, String module) {
        if (StringUtils.isEmpty(file.getName())) {
            return DtoResult.error("文件名为空");
        }
        if (s3Config == null) {
            return DtoResult.error("s3配置不存在！请联系运维人员");
        }
        OSSBean ossBean = OSSBean.builder()
                .region(s3Config.getRegion())
                .accessKey(s3Config.getAccessKey())
                .secretKey(s3Config.getSecretKey())
                .endPoint(s3Config.getEndPoint())
                .returnPoint(s3Config.getReturnPoint())
                .bucket(s3Config.getBucket())
                .build();
        FileModel fileModel = new FileModel();
        String fileType = getFileExtension(file.getName());
        // 生成上传路径
        Date d = new Date();
        String datePath = module + SEPARATOR + formatDate(d, "yyyy") + SEPARATOR + formatDate(d, "MM")
                + SEPARATOR + formatDate(d, "dd");
        String fileName = UUID.fastUUID() + fileType;
        String s3FilePath = datePath + SEPARATOR + UUID.fastUUID() + fileType;
        fileModel.setMd5(MD5.create().digestHex(file));
        try (InputStream inputStream = Files.newInputStream(file.toPath())) {
            defS3.upload(ossBean, inputStream, file.length(), s3FilePath, file.getName());
            fileModel.setUrl(ossBean.getReturnPoint() + SEPARATOR + ossBean.getBucket() + SEPARATOR + s3FilePath);
            fileModel.setName(fileName);
            fileModel.setOldName(file.getName());
            fileModel.setSize((file.length()));
            return DtoResult.ok(fileModel);
        } catch (IOException e) {
            return DtoResult.error("上传失败");
        }
    }

    /**
     * 如果你要加可以上传的文件类型
     * 请写上用途防止被扫漏洞
     */
    private static final Set<String> permittedSuffixes = new HashSet<String>() {{
        add(".jpg");
        add(".jpeg");
        add(".png");
        add(".gif");
        add(".bmp");
        add(".mp4");
        add(".avi");
        add(".mov");
        add(".doc");
        add(".docx");
        add(".pdf");
        add(".mp3");
        add(".wav");
        add(".txt");
        add(".webp");
        add(".aac");
        add(".xls");
        add(".xlsx");
        add(".ppt");
        add(".pptx");
        add(".csv");
        // 汇聚版本文件
        add(".bin");
        //融合apk文件
        add(".apk");
    }};

    public DtoResult<FileModel> uploadToS3(MultipartFile file, String module) {
        if (StringUtils.isEmpty(file.getOriginalFilename())) {
            return DtoResult.error("文件名为空");
        }
        if (s3Config == null) {
            return DtoResult.error("s3配置不存在！请联系运维人员");
        }
        OSSBean ossBean = OSSBean.builder()
                .region(s3Config.getRegion())
                .accessKey(s3Config.getAccessKey())
                .secretKey(s3Config.getSecretKey())
                .endPoint(s3Config.getEndPoint())
                .returnPoint(s3Config.getReturnPoint())
                .bucket(s3Config.getBucket())
                .build();
        FileModel fileModel = new FileModel();
        String fileType = getFileExtension(file.getOriginalFilename());
        if (!permittedSuffixes.contains(fileType)) {
            return DtoResult.error("文件类型不支持");
        }
        // 生成上传路径
        Date d = new Date();
        String datePath = module + SEPARATOR + formatDate(d, "yyyy") + SEPARATOR + formatDate(d, "MM")
                + SEPARATOR + formatDate(d, "dd");
        String fileName = UUID.fastUUID() + fileType;
        String s3FilePath = datePath + SEPARATOR + UUID.fastUUID() + fileType;
        // S3上传
        InputStream inputStream = null;
        try {
            byte[] fileBytes = file.getBytes();
            fileModel.setMd5(MD5.create().digestHex(fileBytes));
            inputStream = new ByteArrayInputStream(fileBytes);
            defS3.upload(ossBean, inputStream, (long) fileBytes.length, s3FilePath, file.getOriginalFilename());
            fileModel.setUrl(ossBean.getReturnPoint() + SEPARATOR + ossBean.getBucket() + SEPARATOR + s3FilePath);
            fileModel.setName(fileName);
            fileModel.setOldName(file.getOriginalFilename());
            fileModel.setSize((file.getSize()));
            return DtoResult.ok(fileModel);
        } catch (IOException e) {
            return DtoResult.error("上传失败");
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.error("Error closing InputStream", e);
                }
            }
        }
    }

    /**
     * 上传文件 相同的ossKey可以覆盖
     */
    public DtoResult<FileModel> uploadToS3AndKey(MultipartFile file, String module, String ossKey) {
        if (StringUtils.isEmpty(file.getOriginalFilename())) {
            return DtoResult.error("文件名为空");
        }
        if (s3Config == null) {
            return DtoResult.error("s3配置不存在！请联系运维人员");
        }
        OSSBean ossBean = OSSBean.builder()
                .region(s3Config.getRegion())
                .accessKey(s3Config.getAccessKey())
                .secretKey(s3Config.getSecretKey())
                .endPoint(s3Config.getEndPoint())
                .returnPoint(s3Config.getReturnPoint())
                .bucket(s3Config.getBucket())
                .build();
        FileModel fileModel = new FileModel();
        String fileType = getFileExtension(file.getOriginalFilename());
        if (!permittedSuffixes.contains(fileType)) {
            return DtoResult.error("文件类型不支持");
        }
        String s3FilePath = module + SEPARATOR + ossKey;
        // S3上传
        InputStream inputStream = null;
        try {
            byte[] fileBytes = file.getBytes();
            fileModel.setMd5(MD5.create().digestHex(fileBytes));
            inputStream = new ByteArrayInputStream(fileBytes);
            defS3.upload(ossBean, inputStream, (long) fileBytes.length, s3FilePath, null);
            fileModel.setUrl(ossBean.getReturnPoint() + SEPARATOR + ossBean.getBucket() + SEPARATOR + s3FilePath);
            fileModel.setName(file.getOriginalFilename());
            fileModel.setOldName(file.getOriginalFilename());
            fileModel.setSize((file.getSize()));
            return DtoResult.ok(fileModel);
        } catch (IOException e) {
            return DtoResult.error("上传失败");
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.error("Error closing InputStream", e);
                }
            }
        }
    }

    // 上传Base64编码的文件
    public DtoResult<FileModel> uploadBase64(String base64, String fileType, String module) {
        if (StringUtils.isEmpty(fileType)) {
            fileType = "jpg";
        }
        if (!fileType.startsWith(".")) {
            fileType = "." + fileType;
        }
        if (s3Config == null) {
            return DtoResult.error("s3配置不存在！请联系运维人员");
        }
        OSSBean ossBean = OSSBean.builder()
                .region(s3Config.getRegion())
                .accessKey(s3Config.getAccessKey())
                .secretKey(s3Config.getSecretKey())
                .endPoint(s3Config.getEndPoint())
                .returnPoint(s3Config.getReturnPoint())
                .bucket(s3Config.getBucket())
                .build();

        FileModel fileModel = new FileModel();
        // 生成上传路径
        Date d = new Date();
        String datePath = module + SEPARATOR + formatDate(d, "yyyy") + SEPARATOR + formatDate(d, "MM")
                + SEPARATOR + formatDate(d, "dd");

        String fileName = UUID.fastUUID() + fileType;
        String s3FilePath = datePath + SEPARATOR + UUID.fastUUID() + fileType;

        // S3上传
        InputStream inputStream = null;
        try {
            byte[] fileBytes = decodeBase64(base64);
            inputStream = new ByteArrayInputStream(fileBytes);
            defS3.upload(ossBean, inputStream, (long) fileBytes.length, s3FilePath, fileName);
            fileModel.setUrl(ossBean.getReturnPoint() + SEPARATOR + ossBean.getBucket() + SEPARATOR + s3FilePath);
            fileModel.setName(fileName);
            fileModel.setSize(((long) base64.length()));
            fileModel.setMd5(MD5.create().digestHex(fileBytes));
            return DtoResult.ok(fileModel);
        } catch (Exception e) {
            log.error("上传失败 -> ", e);
            return DtoResult.error("上传失败");
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.error("Error closing InputStream", e);
                }
            }
        }
    }

    // 上传Base64编码的文件
    public DtoResult<FileModel> uploadByte(byte[] fileBytes, String filePath) {
        if (s3Config == null) {
            return DtoResult.error("s3配置不存在！请联系运维人员");
        }
        OSSBean ossBean = OSSBean.builder()
                .region(s3Config.getRegion())
                .accessKey(s3Config.getAccessKey())
                .secretKey(s3Config.getSecretKey())
                .endPoint(s3Config.getEndPoint())
                .returnPoint(s3Config.getReturnPoint())
                .bucket(s3Config.getBucket())
                .build();

        FileModel fileModel = new FileModel();
        // S3上传
        InputStream inputStream = null;
        try {
            inputStream = new ByteArrayInputStream(fileBytes);
            defS3.upload(ossBean, inputStream, (long) fileBytes.length, filePath, filePath);
            fileModel.setUrl(ossBean.getReturnPoint() + SEPARATOR + ossBean.getBucket() + SEPARATOR + filePath);
            fileModel.setName(filePath);
            fileModel.setSize(((long) fileBytes.length));
            fileModel.setMd5(MD5.create().digestHex(fileBytes));
            return DtoResult.ok(fileModel);
        } catch (Exception e) {
            log.error("上传失败 -> Error closing InputStream", e);
            return DtoResult.error("上传失败");
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.error("Error closing InputStream", e);
                }
            }
        }
    }


    // Base64解码
    private byte[] decodeBase64(String base64) {
        String baseValue = base64.replaceAll(" ", "+");
        return Base64.getDecoder().decode(baseValue.replace("data:image/", "").replace(";base64,", ""));
    }

    // 获取文件扩展名
    private String getFileExtension(String filename) {
        int beginIndex = filename.lastIndexOf(".");
        if (beginIndex == -1) {
            throw new BizRuntimeException("未知的文件格式类型！");
        }
        return filename.substring(beginIndex);
    }

    // 格式化日期
    private String formatDate(Date date, String pattern) {
        return new SimpleDateFormat(pattern).format(date);
    }


    public String formatContentType(String fileName) {
        Optional<MediaType> mediaType = MediaTypeFactory.getMediaType(fileName);
        return mediaType.orElse(MediaType.APPLICATION_OCTET_STREAM).toString();
    }


    /**
     * 重试下载文件
     */
    public byte[] downloadFileAsByteArrayByCount(String fileUrl, int count) {
        for (int i = 0; i < count; i++) {
            byte[] bytes = downloadFileAsByteArray(fileUrl);
            if (bytes != null) {
                return bytes;
            }
        }
        return null;
    }


    public byte[] downloadFileAsByteArray(String fileUrl) {
        if (fileUrl == null || fileUrl.isEmpty()) {
            return null;
        }
        ByteArrayOutputStream buffer = new ByteArrayOutputStream();
        InputStream is = null;
        BufferedInputStream bis = null;
        try {
            URL url = new URL(fileUrl);// 使用 URLConnection 并设置超时时间
            URLConnection connection = url.openConnection();
            // 设置连接超时（单位：毫秒）
            connection.setConnectTimeout(5 * 1000);
            // 设置读取超时（单位：毫秒）
            connection.setReadTimeout(5 * 1000);
            is = connection.getInputStream();
            bis = new BufferedInputStream(is);

            byte[] data = new byte[1024];
            int count;
            while ((count = bis.read(data, 0, 1024)) != -1) {
                buffer.write(data, 0, count);
            }
            byte[] byteArray = buffer.toByteArray();
            if (byteArray.length == 0) {
                log.error("下载文件失败 为什么 文件长度是0呢？ url:{}", fileUrl);
                return null;
            }
            return byteArray;
        } catch (IOException e) {
            log.error("下载文件失败 url:{},msg:{}", fileUrl, e.getMessage());
            return null;
        } finally {
            if (bis != null) {
                try {
                    bis.close();
                } catch (IOException e) {
                    log.error("Error closing BufferedInputStream", e);
                }
            }
            if (is != null) {
                try {
                    is.close();
                } catch (IOException e) {
                    log.error("Error closing InputStream", e);
                }
            }
            try {
                buffer.close();
            } catch (IOException e) {
                log.error("Error closing ByteArrayOutputStream", e);
            }
        }
    }
}