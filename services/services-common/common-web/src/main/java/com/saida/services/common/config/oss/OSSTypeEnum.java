package com.saida.services.common.config.oss;

import com.saida.services.common.tools.SDNumberUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum OSSTypeEnum {

    /**
     * 默认
     */
    DEF("通用S3", 0),

    /**
     * 天翼云
     */
    TIAN_YI_YUN("天翼云-OOS", 1),

    /**
     * 阿里云
     */
    ALIYUN("阿里云", 2),

    /**
     * 瑞驰
     */
    VCLUSTERS("瑞驰", 3),

    /**
     * 移动云
     */
    ECLOUD("移动云-EOS", 4);

    private final String OSSType;
    private final Integer typeId;

    public static OSSTypeEnum getByTypeId(Integer typeId){
        for(OSSTypeEnum t : values()){
            if(SDNumberUtil.equals(t.typeId, typeId)){
                return t;
            }
        }
        return null;
    }
}
