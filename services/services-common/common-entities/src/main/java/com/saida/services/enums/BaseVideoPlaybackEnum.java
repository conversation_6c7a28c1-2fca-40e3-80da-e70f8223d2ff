package com.saida.services.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 通用流协议枚举类
 */
@Getter
@AllArgsConstructor
public enum BaseVideoPlaybackEnum {

    CLOUD(1, "Cloud", "云端录像"),
    DEVICE(2, "Device", "设备录像");

    private final Integer code;
    private final String type;
    private final String msg;

    public static BaseVideoPlaybackEnum getEnumByCode(Integer code) {
        for (BaseVideoPlaybackEnum ele : values()) {
            if (ele.code.equals(code)) {
                return ele;
            }
        }
        return BaseVideoPlaybackEnum.CLOUD;
    }

}

