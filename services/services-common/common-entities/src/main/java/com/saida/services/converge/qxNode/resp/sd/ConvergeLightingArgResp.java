package com.saida.services.converge.qxNode.resp.sd;

import lombok.Data;

/**
 * <AUTHOR>
 * @ClassName ConvergeGimbalStatusResp
 * @Desc
 * @Date 2024/11/9 15:47
 */
@Data
public class ConvergeLightingArgResp {
    //0:自动切换（自动切换时不允许关闭补光）, 1:彩色模式（彩色模式时只支持白光补光）, 2:黑白模式（黑白模式时只支持红外补光）
    public Integer mode;
    //0:关闭, 1:白光补光, 2:红外补光
    private Integer value;
    //0:状态指示灯关闭, 1:状态指示灯开启
    private Integer statusLight;
}
