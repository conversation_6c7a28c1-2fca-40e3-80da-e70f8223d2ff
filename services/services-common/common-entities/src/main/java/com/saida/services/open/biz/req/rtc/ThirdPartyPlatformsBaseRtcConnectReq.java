package com.saida.services.open.biz.req.rtc;

import com.saida.services.open.biz.req.ThirdPartyPlatformsBaseReq;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class ThirdPartyPlatformsBaseRtcConnectReq extends ThirdPartyPlatformsBaseReq implements Serializable {
    private static final long serialVersionUID = 1L;

    /*
     * 设备编码
     */
    private String deviceCode;
    /*
     * 通道
     */
    private String channelId;
    /**
     * 播放类型
     * OpenCommonEnum.BaseNetworkingProtocol
     */
    private Integer playType;
}