package com.saida.services.system.sys.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;

@Data
@HeadRowHeight(40)
@ColumnWidth(15)
public class OrderDeviceExcel implements Serializable {
    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "设备编码", index = 0)
    private String deviceSn;
    @ExcelProperty(value = "设备验证码", index = 1)
    private String verCode;
}
