package com.saida.services.converge.dto;

import com.saida.services.converge.entity.DeviceRecordPlanEntity;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

@Getter
@Setter
public class ConvAddDeviceRecordPlanDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Set<Long> channelIdSet;

    private List<DeviceRecordPlanEntity> deviceRecordPlanEntityList;
}