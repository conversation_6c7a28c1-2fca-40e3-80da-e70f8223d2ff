package com.saida.services.system.sys.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.saida.services.common.entity.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@TableName("algorithm_subscribe")
public class AlgorithmSubscribeEntity extends BaseEntity<AlgorithmSubscribeEntity> implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long appId;

    private String deviceCode;

    private String alertTypes;
}