package com.saida.services.converge.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.saida.services.common.entity.BaseEntity;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @ClassName OpsPlatform
 * @Desc
 * @Date 2024/10/18 13:59
 */
@TableName("ops_platform")
@Data
public class OpsPlatformEntity extends BaseEntity<OpsPlatformEntity> {

    /**
     * 1:上级域  2:下级域
     */
    @NotNull(message = "平台类型不能为空")
    private Integer type;

    /**
     * 平台名称
     */
    @NotBlank(message = "平台名称不能为空")
    private String name;

    /**
     * 平台编码
     */
//    @NotBlank(message = "平台编码不能为空")
    private String code;

    /**
     * 平台厂家
     */
    @NotBlank(message = "平台厂家不能为空")
    private String manufacturer;

    @NotBlank(message = "sip不能为空")
    private String sip;

    private String sipId;

//    @NotBlank(message = "sipIp不能为空")
    private String sipIp;

//    @NotNull(message = "sip端口不能为空")
    private Integer sipPort;

    /**
     * 取流方式 TCPA TCP UDP
     */
//    @NotNull(message = "取流方式不能为空")
    private String fetchingMode;

    /**
     * 传输协议  1 TCP 2 UDP
     */
//    @NotNull(message = "传输方式不能为空")
    private String transportProtocol;

    @NotNull(message = "级联节点不能为空")
    private String nodeId;

    /**
     * 联网协议
     */
    @NotBlank(message = "联网协议不能为空")
    private String networkProtocol;

    /**
     * 组织id
     */
    private Long orgId;

    /**
     * 虚拟组织
     */
    private Long virtualOrgId;

    /**
     * 是否开启鉴权 1:是 0:否
     */
    private Integer authEnable;

    /**
     * 用户名
     */
    private String username;

    /**
     * 是否开启订阅
     */
    private Integer enabledSubscribe;

    /**
     * 是否启用 1启用 0禁用
     */
    private Integer enabled;

    /**
         * 密码
     */
    private String password;

    /**
     * 注册周期
     */
    private Integer registrationCycle;

    /**
     * 心跳
     */
    private Integer heartBeat;
}
