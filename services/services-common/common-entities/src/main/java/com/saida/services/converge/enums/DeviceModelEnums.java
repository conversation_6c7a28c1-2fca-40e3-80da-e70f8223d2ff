package com.saida.services.converge.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum DeviceModelEnums {

    IPC(1L, "IPC"),
    NVR(2L, "NVR");
    // HUMAN_GATES(3L, "人闸"),
    // BRAKE(4L, "车闸");

    private final Long code;
    private final String name;


    public static String getName(Long code) {
        if (code == null) {
            return "";
        }
        for (DeviceModelEnums c : DeviceModelEnums.values()) {
            if (c.getCode().equals(code)) {
                return c.name;
            }
        }
        return "";
    }
}
