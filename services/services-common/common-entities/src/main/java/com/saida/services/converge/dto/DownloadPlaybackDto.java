package com.saida.services.converge.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class DownloadPlaybackDto implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long deviceId;

    private String channelId;

    private String uuid;

    private Long start;

    private Long end;
}