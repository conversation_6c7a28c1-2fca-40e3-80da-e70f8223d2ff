package com.saida.services.open.enums;


import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.saida.services.open.dto.DeviceCapacityDto;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum DeviceCapacityEnum {

    PAN_TILT_CONTROL("PTZ", "pan_tilt_control", 1, 0),
    INTERCOM("语音对讲", "voice_intercom", 1, 1),
    LIGHT_CONTROL("补光灯-1.0", "light_control", 1, 2),
    FLIP_THE_SCREEN("画面翻转", "flip_the_screen", 1, 3),
    PRESET("预制位", "preset", 1, 4),
    GUARDS("看守位", "guards", 1, 5),
    TRAJECTORY_CRUISE("轨迹巡航", "trajectory_cruise", 1, 6),
    PRECISE_PTZ_CONTROL("精准PTZ控制", "precise_ptz_control", 1, 7),
    DEVIC_RELATIVE_XYZ("PTZ快速定位", "devic_relative_xyz", 1, 8),
    ZOOM("变焦", "zoom", 1, 9),
    FOCUS("变倍", "focus", 1, 10),
    VIDEO_CALLS("视频通话", "video_calls", 1, 11),
    ONE_CLICK_PATROL("一键巡视", "one_click_patrol", 1, 12),
    WHITE_LIGHT("白光灯", "white_light", 1, 13),
    SOUND_AND_LIGHT_SHOCK("声光震慑", "sound_and_light_shock", 1, 14),
    DUAL_CAMERA_LINKAGE("双摄联动", "dual_camera_linkage", 1, 15),
    HUMANOID_MARKERS("人形标记", "humanoid_markers", 1, 16),
    WIRED_DISTRIBUTION_NETWORK_V1("有线配网1.0", "wired_distribution_network_v1", 1, 17),
    wireless_distribution_network_V1("无线配网1.0", "wireless_distribution_network_v1", 1, 18),


    FACE_RECOGNITION("人脸识别", "face_recognition", 2, 0),
    HUMANOID_RECOGNITION("人形识别", "humanoid_recognition", 2, 1),
    MOVEMENT_DETECTION("移动检测", "movement_detection", 2, 2),
    VIDEO_OCCLUSION("视频遮挡", "video_occlusion", 2, 3),
    ELECTRIC_VEHICLE_IDENTIFICATION("电动车识别", "electric_vehicle_identification", 2, 4),
    LICENSE_PLATE_RECOGNITION("车牌识别", "license_plate_recognition", 2, 5),
    REGIONAL_INVASION("区域入侵", "regional_invasion", 2, 6),
    MOTION_TRACKING("移动追踪", "motion_tracking", 2, 7),
    PEDESTRIAN_TRACKING("人行追踪", "pedestrian_tracking", 2, 8),
    SOUND_AND_LIGHT_ALARM_V1("声光报警", "sound_and_light_alarm_v1", 2, 9),
    /**
     *
     * 1 赛达  人形检测
     * 2 赛达  移动检测
     * 3 赛达  视频遮挡
     * 4 赛达  区域入侵
     */
//    TIME_LAPSE("时光缩影", "time_lapse")
    ;


    private final String name;
    private final String field;
    //1 通用 2AI
    private final Integer type;
    private final int bitIndex; // 新增字段


    private static final Map<String, DeviceCapacityDto> capacityDtoMap = new HashMap<>();

    public static Map<String, DeviceCapacityDto> getCapacityDtoMap() {
        return ObjectUtil.clone(capacityDtoMap);
    }

    static {
        for (DeviceCapacityEnum capacityEnum : values()) {
            capacityDtoMap.put(capacityEnum.getField(), new DeviceCapacityDto(capacityEnum, 0));
        }
    }

    public static DeviceCapacityEnum getByField(String deviceCapacityField) {
        DeviceCapacityEnum[] values = DeviceCapacityEnum.values();
        for (DeviceCapacityEnum capacityEnum : values) {
            if (capacityEnum.getField().equals(deviceCapacityField)) {
                return capacityEnum;
            }
        }
        return null;
    }

    public JSONObject getJSONObject() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("name", name);
        jsonObject.put("field", field);
        jsonObject.put("type", type);
        return jsonObject;
    }
}