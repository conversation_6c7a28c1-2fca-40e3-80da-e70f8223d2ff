package com.saida.services.converge.qxNode.req;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * 视频汇聚平台-车辆信息同步到闸机
 */
@Data
public class AddCarInfoGateReq implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * add：添加，edit：修改
     */
    private String action;

    /**
     * 设备id
     */
    @JSONField(name = "device_id")
    private String deviceId;

    /**
     * 车牌号码
     */
    @JSONField(name = "license_plate")
    private String licensePlate;

    /**
     * 卡号
     */
    @JSONField(name = "card_no")
    private String cardNo;

    /**
     * 授权/非授权 ,blockList :非授权名单, allowList : 授权名单
     */
    @JSONField(name = "list_type")
    private String listType;

    /**
     * 车牌颜色，取值：[black#黑色,blue#蓝色,civilAviationBlack#民航黑色,civilAviationGreen#民航绿色,
     * golden#金色,green#绿色,mixedColor#花底,newEnergyGreen#新能源绿色,newEnergyYellowGreen#新能源黄绿色,
     * orange#橙色,other#其他颜色,red#红色,unknown#未知,white#白色,yellow#黄色]
     */
    @JSONField(name = "plate_color")
    private String plateColor;

    /**
     *车牌类型，取值：[02TypePersonalized#02式个性化车,04NewMilitay#04式新军车,92FarmVehicle#民用车双行尾牌（补录）,
     * 92TypeArm#92式武警车,92TypeCivil#92式民用车,arm#警车,civilAviation#民航车牌,coach#教练车,consulate#领馆汽车,
     * embassy#使馆车,emergency#应急车牌,green1325FarmVehicle#绿色1325农用车,hongKongMacao#港澳入出车,leftRightMilitay#左右军车,
     * motorola#摩托车,newEnergy#新能源车牌,oneLineArm#一行结构的新武警车,oneLineArmHeadquarters#一行结构武警总部车牌,tempEntry#临时入境车,
     * tempTravl#临时行驶车,trailer#挂车,twoLineArm#两行结构的新武警车,twoLineArmHeadquarters#两行结构武警总部车牌,unknown#未知,upDownMilitay#上下军车,
     * yellow1225FarmVehicle#黄色1225农用车,yellow1325FarmVehicle#黄色1325结构农用车,yellowTwoLine#黄色双行尾牌]
     */
    @JSONField(name = "plate_type")
    private String plateType;

    /**
     * 有效期开始时间，action=edit有效  ，时间格式：yyyy-MM-dd HH:mm:ss
     */
    @JSONField(name = "create_time")
    private String createTime;

    /**
     * 有效期截止时间  action=edit有效  ，时间格式：yyyy-MM-dd HH:mm:ss
     */
    @JSONField(name = "effective_time")
    private String effectiveTime;
}