package com.saida.services.open.req;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 视频汇聚平台-录像时间轴
 */
@Getter
@Setter
public class VlinkerDelPreSetReq implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long deviceId;
    /*
     * 设备SN码
     */
    private String deviceCode;
    /*
     * 通道
     */
    private String channelId;
    /**
     * 预置点位
     */
    private Integer index;
}