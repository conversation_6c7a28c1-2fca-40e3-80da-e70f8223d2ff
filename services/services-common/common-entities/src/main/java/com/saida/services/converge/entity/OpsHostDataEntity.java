package com.saida.services.converge.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.saida.services.entities.base.BaseRequest;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 主机信息
 *
 * <AUTHOR>
 * email ${email}
 * date 2023-10-31 16:43:41
 */
@Data
@TableName("ops_host_data")
public class OpsHostDataEntity extends BaseRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 创建人
     */
    private Long createUser;
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * 修改人
     */
    private Long updateUser;
    /**
     * 修改时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    /**
     * 节点id
     */
    private String nodeId;
    /**
     * 主机名称
     */
    private String name;
    /**
     * 操作系统
     */
    private String operatingSystem;
    /**
     * 核心
     */
    private String core;
    /**
     * 内存
     */
    private String memory;
    /**
     * 硬盘
     */
    private String hardDisk;
    /**
     * 内网ip
     */
    private String intranetIpAddress;
    /**
     * 外网ip
     */
    private String publicIpAddress;
    /**
     * 上线带宽
     */
    private String bandwidthOnTheLine;
    /**
     * 下行带宽
     */
    private String downstreamBandwidth;
    /**
     *
     */
    private String mac;
    /**
     * 普罗米修斯api
     */
    private String prometheusApi;

    //1正常 0异常
    private Integer status;

}
