package com.saida.services.converge.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;

@Data
@HeadRowHeight(40)
@ColumnWidth(15)
public class DeviceChannelInfoExcelOut implements Serializable {
    private static final long serialVersionUID = 1L;


    @ExcelProperty(value = "通道名称")
    private String channelName;

    @ExcelProperty(value = "通道ID")
    private String channelId;
    /**
     * 状态，1：在线，0：离线
     */
    @ExcelProperty(value = "状态")
    private String status;

    @ExcelProperty(value = "上报时间")
    private String createTime;

}