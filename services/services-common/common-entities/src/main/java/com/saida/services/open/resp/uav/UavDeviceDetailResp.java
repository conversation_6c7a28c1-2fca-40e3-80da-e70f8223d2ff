package com.saida.services.open.resp.uav;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @ClassName UavDetailResp
 * @Desc
 * @Date 2025/2/11 09:46
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UavDeviceDetailResp {
    public String deviceId;

    private String deviceCode;

    // 无人机名称
    private String deviceName;

    // 在线状态 0 离线 1 在线
    private Integer onlineStatus;

    // 0:飞机类 1:负载类 2:遥控器类 3:机场类
    private Integer domain;

    // 固件版本
    private String firmwareVersion;

    // "0":"未升级","1":"升级中"
    private Integer firmwareUpgradeStatus;
}
