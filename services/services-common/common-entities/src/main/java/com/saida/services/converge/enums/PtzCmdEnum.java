package com.saida.services.converge.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @ClassName PtzCmdEnum
 * @Desc
 * @Date 2024/11/13 16:53
 */
@Getter
@AllArgsConstructor
public enum PtzCmdEnum {

    //TOP: 上，BOTTOM: 下，LEFT: 左，RIGHT: 右，TOP_LEFT: 左上，TOP_RIGHT: 右上，BOTTOM_LEFT: 左下，BOTTOM_RIGHT: 右下，ZOOM_IN: 放大，ZOOM_OUT: 缩小，FOCUS_IN: 近焦距，FOCUS_OUT: 远焦距

    UP("TOP", "SD_PTZ_UP",  "上"),
    DOWN("BOTTOM", "SD_PTZ_DOWN",  "下"),
    LEFT("LEFT", "SD_PTZ_LEFT", "左"),
    RIGHT("RIGHT", "SD_PTZ_RIGHT", "右"),
    LEFT_UP("TOP_LEFT", "SD_PTZ_LEFT_UP", "左上"),
    RIGHT_UP("TOP_RIGHT", "SD_PTZ_RIGHT_UP",  "右上"),
    LEFT_DOWN("BOTTOM_LEFT", "SD_PTZ_LEFT_DOWN", "左下"),
    RIGHT_DOWN("BOTTOM_RIGHT", "SD_PTZ_RIGHT_DOWN", "右下"),
    NEAR("ZOOM_IN", "SD_PTZ_NEAR", "放大"),
    FAR("ZOOM_OUT", "SD_PTZ_FAR", "缩小"),
    FOCUS_IN("FOCUS_IN", "SD_PTZ_FOCUS_IN", "近焦距"),
    FOCUS_OUT("FOCUS_OUT", "SD_PTZ_FOCUS_IN", "远焦距"),
    APERTURE_BIG("APERTURE_BIG", "SD_PTZ_APERTURE_BIG", "光圈+"),
    APERTURE_SMALL("APERTURE_SMALL", "SD_PTZ_APERTURE_SMALL", "光圈-"),
    STOP("stop", "SD_PTZ_STOP", "停止"),

   ;

    private final String type;
    private final String sdCode;
    private final String name;

    public static PtzCmdEnum getType(String type) {
        for (PtzCmdEnum t : values()) {
            if (Objects.equals(type, t.getType())) {
                return t;
            }
        }
        return null;
    }
}
