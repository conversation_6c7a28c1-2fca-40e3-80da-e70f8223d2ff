package com.saida.services.converge.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @ClassName DeviceCapacityVo
 * @Desc
 * @Date 2024/10/16 09:36
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DeviceModelVersionVo {
    /**
     * 设备sn
     */
    private String deviceCodeCode;

    /**
     * 设备型号
     */
    private String model;

    /**
     * 当前固件版本
     */
    private String versionNow;


    /**
     * 最新的固件版本
     */
    private String versionLatest;

    private String macAddress;

}