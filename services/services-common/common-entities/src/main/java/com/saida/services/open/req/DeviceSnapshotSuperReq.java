package com.saida.services.open.req;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class DeviceSnapshotSuperReq implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 设备ID，用于唯一标识一个设备
     */
    private Long deviceId;

    /**
     * 设备编码，用于设备的外部标识和追踪
     */
    private String deviceCode;

    /**
     * 通道ID，标识设备所属的通道，用于消息的路由和管理
     */
    private String channelId;

    /**
     * 是否强制生成
     */
    private Boolean generateFlag;

    private Integer times;

    /**
     * 图像宽度
     */
    private Integer width;

    /**
     * 图像高度
     */
    private Integer height;
}
