package com.saida.services.open.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class AppListPageByAdminDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /*
     * 企业名称
     */
    private String enterpriseName;

    /*
     * 订购日期：开始时间（yyyy-MM-dd）
     */
    private String begOrderDate;

    /*
     * 订购日期：结束时间（yyyy-MM-dd）
     */
    private String endOrderDate;

    /*
     * 创建时间：开始时间（yyyy-MM-dd HH:mm:ss）
     */
    private String begOrderTime;

    /*
     * 创建时间：结束时间（yyyy-MM-dd HH:mm:ss）
     */
    private String endOrderTime;

    /*
     * 订单类型：字典，1-初始订单；2-开通能力；3-流量订单；4-有效期；5-订购路数
     */
    private Long orderType;

    /*
     * 订单编号
     */
    private String orderNumber;

    private Long orderId;
}
