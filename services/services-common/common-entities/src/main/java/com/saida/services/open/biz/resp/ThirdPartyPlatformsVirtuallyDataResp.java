package com.saida.services.open.biz.resp;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 */
@Getter
@Setter
public class ThirdPartyPlatformsVirtuallyDataResp implements Serializable {
    private static final long serialVersionUID = 1L;



    /**
     * 推流地址
     */
    private String rtmpUrl;
    /**
     * 推流地址
     */
    private String rtspUrl;
    /**
     * 拉流地址
     */
    private String pullUrl;

    /**
     * 1 推流  2拉流
     */
    private Integer mode;
}