package com.saida.services.open.req;

import com.saida.services.open.enums.DeviceManufacturerEnum;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Getter
@Setter
public class IotDeviceRelayControlReq implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 设备imei
     */
    @NotBlank(message = "设备imei不能为空")
    private String imei;
    /**
     * 继电器状态0-断开 1-闭合
     */
    @NotNull(message = "继电器状态不能为空")
    private Integer relayStatus;
    /**
     * 继电器索引
     */
    @NotNull(message = "继电器索引不能为空")
    private Integer relayIndex;
    /**
     * 设备厂商
     * @see DeviceManufacturerEnum
     */
    @NotNull(message = "设备厂商不能为空")
    private Integer manufacturer;
}