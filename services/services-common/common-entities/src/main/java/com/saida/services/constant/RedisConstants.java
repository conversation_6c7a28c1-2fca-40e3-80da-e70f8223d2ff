package com.saida.services.constant;

public interface RedisConstants {

    /**
     * 用户的token
     */
    String USER_JWT = "USER_JWT:";

    // 用户APP登录token
    String APP_USER_JWT = "APP_USER_JWT:";

    String USER_REFRESH_JWT = "USER_REFRESH_JWT:";

    String APP_USER_REFRESH_JWT = "APP_USER_REFRESH_JWT:";

    /**
     * 流程用的编号
     */
    String PROCESS_NO = "PROCESS_NO:";
    /**
     * 请求锁
     */
    String DELAY = "Delay:";
    /**
     * 缓存请求参数
     */
    String CACHE_PARAMETER = "CACHE:PARAMETER:";
    /**
     * 缓存请求返回
     */
    String CACHE_RES = "CACHE:RES:";

    /**
     * 短信验证码
     */
    String SMS_V_CODE = "SMS_V_CODE:";

    /**
     * 短信验证码,下发频率
     */
    String SMS_V_CODE_SEND_FREQUENCY = "SMS_V_CODE:SEND:FREQUENCY:";

    /**
     * 短信验证码（密码修改）
     */
    String SMS_MOD_PASSWORD_CODE = "SMS_MOD_PASSWORD_CODE:";

    String SMS_BIND_TOC_USER_CODE = "SMS_BIND_TOC_USER_CODE:";

    /**
     * 短信验证码（账号注销）
     */
    String SMS_LOGOFF_PASSWORD_CODE = "SMS_LOGOFF_PASSWORD_CODE:";

    /**
     * 短信验证码（通过手机号修改密码）
     */
    String SMS_CODE_BY_PHONE = "SMS_CODE_BY_PHONE:";

    /**
     * 短信验证码（新增管理用户绑定个人用户）
     */
    String SMS_CODE_BIND_TOC_USER = "SMS_CODE_BIND_TOC_USER:";

    /**
     * 短信验证码 有效期 秒
     */
    int SMS_V_CODE_TIME = 60 * 5;

    /**
     * 图片验证码
     */
    String IMG_V_CODE = "IMG_V_CODE:";
    /**
     * 图片验证码 有效期 秒
     */
    int IMG_V_CODE_TIME = 60;

    /**
     * websocket用户
     */
    String WEBSOCKET_USER = "WEBSOCKET_USER:";

    /**
     * 手机推送服务token
     */
    String PHONE_PUSH_SERVICE_TOKNE = "PHONE_PUSH_SERVICE_TOKEN:";

    /**
     * 注册回话id， REGISTER_SESSION_ID:phone
     */
    String REGISTER_SESSION_ID = "REGISTER_SESSION_ID:";

    /**
     * CLIENT_APP
     */
    String CLIENT_APP = "CLIENT_APP:";

    /**
     * CLIENT_APP token
     */
    String CLIENT_APP_JWT = "CLIENT_APP_JWT:";

    /**
     * 能力信息 CAPABILITY_INFO:URL
     */
    String CAPABILITY_INFO = "CAPABILITY_INFO:";

    /**
     * 参数跟踪
     */
    String TRACE_PARAM = "TRACE_PARAM:";

    /**
     * 订单接口使用次数， key: OrderUseCount:订单id:能力ID:日期， value: 次数
     */
    String OrderUseCount = "OrderUseCount:%s:%s:%s";

    /**
     * 套餐订单号生成key
     */
    String PACKAGE_ORDER_CREATE_KEY = "PACKAGE_ORDER_CREATE_KEY_";

    /**
     * c端用户设备是否配置过ai设置标识
     */
    String SRV_DEVICE_AI_SETTING_RECORD = "SRV:DEVICE_AI_SETTING_RECORD:";

    /**
     * c端用户设备配置相机静音标识
     */
    String SRV_DEVICE_CAMERA_MUTE_RECORD = "SRV:DEVICE_CAMERA_MUTE_RECORD:";

    /**
     * 算法平台-基础信息-用户工号key
     */
    String CONV_BASIC_INFO_PEOPLE_NUMBER = "CONV:BASIC_INFO:PEOPLE:NUMBER";
    /**
     * 算法平台-人员布控-同人告警间隔
     */
    String CONV_PEOPLE_DEPLOY_CONTROL_SAME_PERSON_ALARM_INTERVAL = "CONV:PEOPLE_DEPLOY_CONTROL:ALARM_INTERVAL:";
    /**
     * 算法平台-人员布控数据缓存
     */
    String CONV_PEOPLE_DEPLOY_CONTROL_RECORD = "CONV:PEOPLE_DEPLOY_CONTROL:RECORD";
}
