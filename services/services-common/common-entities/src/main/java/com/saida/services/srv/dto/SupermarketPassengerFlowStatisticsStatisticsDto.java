package com.saida.services.srv.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@JsonInclude
public class SupermarketPassengerFlowStatisticsStatisticsDto implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long orgId;

    private Long appScene;

    /*
     * 排序规则：1-升序；2-降序
     */
    private Integer sort;

    /*
     * 统计类型：1-天；2-周；3-月；4-自定义时间
     */
    private Integer type;

    private String begTime;

    private String endTime;
}