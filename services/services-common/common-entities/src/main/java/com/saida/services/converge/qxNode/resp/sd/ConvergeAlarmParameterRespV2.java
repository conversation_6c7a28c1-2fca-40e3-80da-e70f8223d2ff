package com.saida.services.converge.qxNode.resp.sd;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName ConvergeAlarmParameterResp
 * @Desc
 * @Date 2024/11/11 13:54
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ConvergeAlarmParameterRespV2 {
    /**
     * {
     * 	"detect": [{
     * 			"channel_id": 1,
     * 			"detect_enable": 1,
     * 			"detect_type": 1,
     * 			"detect_sens": 50,
     * 			"detect_points": [{
     * 					"x": 23,
     * 					"y": 43
     * 				                },
     *                {
     * 					"x": 23,
     * 					"y": 43
     *                },
     *                {
     * 					"x": 23,
     * 					"y": 43
     *                },
     *                {
     * 					"x": 23,
     * 					"y": 43
     *                },
     *                {
     * 					"x": 23,
     * 					"y": 43
     *                }
     * 			]
     * 		},
     * 		{
     * 			"channel_id": 1,
     * 			"detect_enable": 1,
     * 			"detect_type": 1,
     * 			"detect_sens": 50,
     * 			"detect_points": [{
     * 					"x": 23,
     * 					"y": 43
     *                },
     *                {
     * 					"x": 23,
     * 					"y": 43
     *                },
     *                {
     * 					"x": 23,
     * 					"y": 43
     *                },
     *                {
     * 					"x": 23,
     * 					"y": 43
     *                },
     *                {
     * 					"x": 23,
     * 					"y": 43
     *                }
     * 			]
     * 		}
     * 	],
     * 	"plan_time": ["7:00 - 10:00", "11:00 - 18:00"],
     * 	"alarm_type": 1,
     * 	"alarm_interval": 10,
     * 	"alarm_trace": 0
     * }
     */
    private List<Detect> detect;
    private List<String> planTime;
    //0x1 声音报警，0x2 灯光报警   ，0x1 | 0x2 同时报警
    private Integer alarmType;
    //报警间隔： 单位秒
    private Integer alarmInterval;
    //0：跟踪， 1不跟踪
    private Integer alarmTrace;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Detect {
        private Integer channelId;
        //报警区域是否启用,1: 启用, 0: 禁用
        private Integer detectEnable;
        //报警类型, 1,人型侦测, 2,移动侦测 3,视频遮挡 4,区域入侵 ， 7 人脸抓拍，8车牌抓拍
        private Integer detectType;
        //报警区域侦测灵敏度,范围1-100
        private Integer detectSens;
        //报警区域坐标数组，最多10
        private List<DetectPoint> detectPoints;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DetectPoint {
        private Integer x;
        private Integer y;
    }
}
