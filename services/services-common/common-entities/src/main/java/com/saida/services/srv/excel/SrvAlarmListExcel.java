package com.saida.services.srv.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;

@Data
@HeadRowHeight(40)
@ColumnWidth(25)
public class SrvAlarmListExcel implements Serializable {
    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "预警类型", index = 0)
    private String alarmTypeName;

    @ExcelProperty(value = "设备名称", index = 3)
    private String channelName;

    @ExcelProperty(value = "预警来源", index = 1)
    private String alarmSourceName;

    @ExcelProperty(value = "原始图片", index = 2)
    private String originalImageUrl;

    @ExcelProperty(value = "预警图片", index = 3)
    private String alarmImageUrl;

    @ExcelProperty(value = "状态", index = 3)
    private String statusName;

    @ExcelProperty(value = "预警时间", index = 3)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String alarmTime;
}