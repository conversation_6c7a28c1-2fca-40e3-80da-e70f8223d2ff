package com.saida.services.converge.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.saida.services.common.entity.BaseEntity;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("ops_device_screen_info")
public class OpsDeviceScreenInfoEntity extends BaseEntity<OpsDeviceScreenInfoEntity> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * CREATE TABLE `ops_device_screen_info` (
     *   `id` bigint NOT NULL COMMENT '其实就是设备id',
     *   `create_user` bigint DEFAULT NULL COMMENT '创建人',
     *   `create_time` datetime DEFAULT NULL COMMENT '创建时间',
     *   `update_user` bigint DEFAULT NULL COMMENT '修改人',
     *   `update_time` datetime DEFAULT NULL COMMENT '修改时间',
     *   `screen_size` double DEFAULT '0' COMMENT '屏幕尺寸',
     *   `ui_width` int DEFAULT NULL COMMENT '屏幕分辨率宽',
     *   `ui_height` int DEFAULT NULL COMMENT '屏幕分辨率高',
     *   `ui_dpi` int DEFAULT NULL COMMENT '每英寸像素点数 如 100、200、240、800、1200',
     *   `ui_support_touch_flag` int DEFAULT NULL COMMENT '是否支持触屏 0.不支持、1.支持',
     *   `ui_screen_brightness` int DEFAULT NULL COMMENT '亮度0-100',
     *   `ui_video_enc_ability` int DEFAULT NULL COMMENT ' 0x01.JPEG 图片编码模式, 0x02.H264 编 码 ，0x04.H265 编 码 , 若 支 持 H264 、 H265 编 码 ， 则ui_video_enc_ability=0x02|0x04',
     *   `ui_max_bit_rate` int DEFAULT NULL COMMENT '整型，最大支持的码率',
     *   `off_screen_time` int DEFAULT NULL COMMENT '息屏时间 30s',
     *   PRIMARY KEY (`id`)
     * ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='设备屏幕参数信息';
     */
    /**
     * 屏幕尺寸
     */
    private Double screenSize;

    /**
     * 屏幕分辨率宽
     */
    private Integer uiWidth;

    /**
     * 屏幕分辨率高
     */
    private Integer uiHeight;

    /**
     * 每英寸像素点数 如 100、200、240、800、1200
     */
    private Integer uiDpi;

    /**
     * 是否支持触屏 0.不支持、1.支持
     */
    private Integer uiSupportTouchFlag;

    /**
     * 亮度0-100
     */
    private Integer uiScreenBrightness;

    /**
     * 0x01.JPEG 图片编码模式, 0x02.H264 编 码 ，0x04.H265 编 码 , 若 支 持 H264 、 H265 编 码 ， 则ui_video_enc_ability=0x02|0x04
     */
    private Integer uiVideoEncAbility;

    /**
     * 最大支持的码率
     */
    private Integer uiMaxBitRate;

    /**
     * 息屏时间 30s
     */
    private Integer offScreenTime;
}