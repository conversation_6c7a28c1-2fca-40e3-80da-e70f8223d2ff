package com.saida.services.converge.entity.dto;

import com.saida.services.converge.entity.VirtualOrganTreeEntity;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * 虚拟组织树
 *
 * <AUTHOR>
 * email ${email}
 * date 2023-10-10 15:31:54
 */
@Getter
@Setter
public class VirtualOrganTreeDto extends VirtualOrganTreeEntity {
    private static final long serialVersionUID = 1L;


    private Integer onlineCount;

    private Integer offlineCount;

    public Integer getOnlineCount() {
        return onlineCount == null ? 0 : onlineCount;
    }

    public Integer getOfflineCount() {
        return offlineCount == null ? 0 : offlineCount;
    }

    public Integer getAllCount() {
        return (onlineCount == null ? 0 : onlineCount) + (offlineCount == null ? 0 : offlineCount);
    }

    private List<VirtualOrganTreeDto> childList = new ArrayList<>();

}
