package com.saida.services.open.req;

import lombok.*;

import java.io.Serializable;
import java.util.List;

/**
 */
@Getter
@Setter
public class VlinkerSetSideAlgorithmReq implements Serializable {
    private static final long serialVersionUID = 1L;
    private Long deviceId;

    /*
     * 设备SN码
     */
    private String deviceCode;
    /*
     * 通道
     */
    private String channelId;
    /**
     * 1 赛达  人形检测
     * 2 赛达  移动检测
     * 3 赛达  视频遮挡
     * 4 赛达  区域入侵
     */
    private Integer type;

    //报警区域编号
    private Integer detectId;
    //报警区域是否启用,1: 启用, 0: 禁用
    private Integer detectEnable;
    //报警区域侦测类型, 1,人型侦测, 2,移动侦测 3,视频遮挡 4,区域入侵
//    private Integer detectType;
    //报警区域侦测灵敏度,范围1-100
    private Integer detectSens;
    //报警区域坐标数组，最多10
    private List<PointVo> detectPoints;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class PointVo {
        //0 - 100的百分比
        private Integer x;
        //0 - 100的百分比
        private Integer y;
    }

}