package com.saida.services.common.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * 归一化的告警结构体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlarmNormalizationExt {

    // 车辆信息
    private List<CarInfoDto> carInfoDto = new ArrayList<>();
    // 人员信息
    private List<PeopleInfoDto> peopleInfoDto = new ArrayList<>();
    // ptz信息
    private PtzDto ptzDto;
    // box
    private List<BoxDto> boxInfoDto = new ArrayList<>();
    // 进出统计信息
    private InOutCountDto inOutCountDto;

    // 进出统计信息
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class InOutCountDto {
        private Integer inNum;
        private Integer outNum;
    }

    /**
     * 封装一个简易方法
     */
    public void addBoxInfoDto(BoxDto boxDto) {
        if (this.boxInfoDto == null) {
            this.boxInfoDto = new ArrayList<>();
        }
        this.boxInfoDto.add(boxDto);
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BoxDto {
        // box的id
        private Integer boxId;
        // box的坐标
        private Integer x;
        private Integer y;
        private Integer width;
        private Integer height;
        //-1：未知状态；0：正常状态；1：奔跑状态；2：徘徊状态；3：逗留状态
        private Integer abd_evt_type;
        //0：未知事件；1：进入事件；2：离开事件
        private Integer cpc_evt_type;
    }

    /**
     * 封装一个简易方法
     */
    public void addCarInfoDto(CarInfoDto carInfoDto) {
        if (this.carInfoDto == null) {
            this.carInfoDto = new ArrayList<>();
        }
        this.carInfoDto.add(carInfoDto);
    }

    public void addPeopleInfoDto(PeopleInfoDto peopleInfoDto) {
        if (this.peopleInfoDto == null) {
            this.peopleInfoDto = new ArrayList<>();
        }
        this.peopleInfoDto.add(peopleInfoDto);
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CarInfoDto {
        // 车牌号
        private String plateNumber;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PeopleInfoDto {
        // 人员编号
        private String peopleNumber;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PtzDto {
        // 水平角度
        private Double zoom;
        // 垂直角度
        private Double tilt;
        // 旋转角度
        private Double pan;
    }
}