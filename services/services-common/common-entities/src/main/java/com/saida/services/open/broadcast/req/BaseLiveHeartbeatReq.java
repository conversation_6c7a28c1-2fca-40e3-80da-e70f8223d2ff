package com.saida.services.open.broadcast.req;

import com.saida.services.open.biz.req.ThirdPartyPlatformsBaseReq;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class BaseLiveHeartbeatReq extends ThirdPartyPlatformsBaseReq implements Serializable {
    private static final long serialVersionUID = 1L;

    private String sessionId;

    private String deviceId;
}