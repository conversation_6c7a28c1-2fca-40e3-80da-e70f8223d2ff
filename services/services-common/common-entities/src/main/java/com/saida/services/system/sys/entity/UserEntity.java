package com.saida.services.system.sys.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.saida.services.entities.base.BaseRequest;
import com.saida.services.tools.attr.DisplayField;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 用户
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-09-23 14:03:54
 */
@Data
@TableName("sys_user")
public class UserEntity extends BaseRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 账号
     */
    @NotEmpty(message = "账号必填")
    private String account;

    /**
     * 姓名
     */
    @NotEmpty(message = "姓名必填")
    private String name;

    /**
     * 手机
     */
    private String phone;

    /**
     * 密码
     */
    @JsonIgnore
    private String password;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 用户类型，1：管理用户，2：注册客户
     */
    private Integer type;

    /**
     * 创建人
     */
    private Long createUser;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 修改人
     */
    private Long updateUser;

    /**
     * 修改时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 组织
     */
    @DisplayField(field = "orgName")
    private Long orgId;

    /**
     * 角色ID
     */
    @TableField(exist = false)
    private String rids;

    /**
     * 查询条件是否包含下级
     */
    @TableField(exist = false)
    private Boolean subLevel = false;

    @TableField(exist = false)
    private List<RoleEntity> roles;

    /**
     * 头像
     */
    private String headPortrait;

    /**
     * 最后修改密码时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime lastModifyPasswordTime;

    /**
     * 区域id
     */
    private Long regionId;

    @TableField(exist = false)
    private String regionIdChain;

    /**
     * 查询条件，法人
     */
    @TableField(exist = false)
    private String legalPerson;
    /**
     * 1:冻结 2：激活
     */
    @TableField(exist = false)
    private Integer freeze;


    @TableField(exist = false)
    private String orgName;
    /**
     * 用户类型，1：普通用户 2超管
     */
    private Integer isSuper;
    /**
     * 1汇聚 2算法 3三方（GIS） 4 能开
     */
    private Integer pid;
}
