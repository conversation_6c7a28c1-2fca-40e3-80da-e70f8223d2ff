package com.saida.services.converge.qxNode.req.device;

import com.alibaba.fastjson.annotation.JSONField;
import com.saida.services.converge.qxNode.req.QxNodeBaseReq;
import lombok.Data;

import java.util.List;

/**
 */
@Data
public class DeviceSubscribeReq extends QxNodeBaseReq {

    @JSONField(name = "device_ids")
    private List<String> deviceIds;

    /**
     * 目录订阅 1订阅 0未订阅
     */
    private Boolean catalog;

    /**
     * 告警订阅 1订阅 0未订阅
     */
    private Boolean alarm;

    /**
     * 位置订阅 1订阅 0未订阅
     */
    private Boolean position;

}