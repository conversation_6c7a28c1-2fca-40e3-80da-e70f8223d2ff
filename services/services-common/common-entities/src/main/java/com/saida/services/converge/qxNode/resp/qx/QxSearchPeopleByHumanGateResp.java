package com.saida.services.converge.qxNode.resp.qx;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 信令接口-人闸接口，查询人员
 */
@Data
public class QxSearchPeopleByHumanGateResp implements Serializable {
    private static final long serialVersionUID = 1L;

    @JSONField(name = "total")
    private Integer total;

    @JSONField(name = "total_page")
    private Integer totalPage;

    @JSONField(name = "list")
    private List<PeopleInfo> list;


    @Data
    public static class PeopleInfo implements Serializable {
        private static final long serialVersionUID = 1L;

        @JSONField(name = "user_id")
        private String userId;

        @JSONField(name = "name")
        private String name;

        @JSONField(name = "begin_time")
        private String beginTime;

        @J<PERSON>NField(name = "end_time")
        private String endTime;
    }
}