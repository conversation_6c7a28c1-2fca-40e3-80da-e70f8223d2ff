package com.saida.services.open.resp.api;

import com.saida.services.open.dto.DeviceCapacityDto;
import lombok.Data;

import java.util.Map;

@Data
public class DeviceListResp {

    /**
     * 如果这个设备是通道的话 这个编码是他们设备的编码而且是一样的
     */
    private String deviceId;

    /**
     * 设备请求唯一编码
     */
    private String deviceCode;

    private String deviceSn;

    private String deviceName;

    private String channelCode;

    private String channelName;

    /**
     * 在线状态 0离线 1在线
     */
    private Integer onlineStatus;

    /*
     * 闸机类型：0-默认，不是闸机；1-人闸；2-车闸
     */
    private Integer gateType;

    /*
     * 1 ipc
     * 2 nvr
     */
    private Integer ipcType;
    /**
     * 设备能力信息
     */
    private Map<String, DeviceCapacityDto> deviceCapacity;
}