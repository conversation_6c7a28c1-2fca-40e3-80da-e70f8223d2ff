package com.saida.services.converge.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.saida.services.common.entity.BaseEntity;
import lombok.Data;

import java.io.Serializable;

/**
 * 南向绑定
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-10-27 14:29:27
 */
@Data
@TableName("ops_device_vcp")
public class OpsDeviceVcpEntity extends BaseEntity<OpsDeviceVcpEntity> implements Serializable {
	private static final long serialVersionUID = 1L;

	private Long accountId;

	private String deviceCode;

	private String username;

	private String password;

	private String serverUrl;

	private String sipGbDomain;

	private String sipGbCode;

	private String channelList;

	/**
	 * 0默认 1已创建
	 */
	private Integer isCreate;

	/**
	 *  0默认 1已绑定
	 */
	private Integer isBind;

	private Long deviceId;

}
