package com.saida.services.log;

import java.lang.annotation.*;

@Retention(RetentionPolicy.RUNTIME)
@Inherited
@Documented
@Target(ElementType.METHOD)
public @interface LogOperation {
    /**
     * 操作类型
     */
    LogOperationEnum type();

    /**
     * 操作功能
     */
    String func();

    /**
     * 模块
     */
    ModuleEnum module() default ModuleEnum.UNKNOWN;

    /**
     * 是否记录参数
     */
    boolean recordParam() default true;

    /**
     * 是否是能力开放接口
     */
    boolean openApi() default false;
}
