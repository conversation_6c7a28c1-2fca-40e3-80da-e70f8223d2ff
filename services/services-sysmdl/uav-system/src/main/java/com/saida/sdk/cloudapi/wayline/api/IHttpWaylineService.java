package com.saida.sdk.cloudapi.wayline.api;

import com.saida.sdk.cloudapi.wayline.GetWaylineListRequest;
import com.saida.sdk.cloudapi.wayline.GetWaylineListResponse;
import com.saida.sdk.cloudapi.wayline.WaylineUploadCallbackRequest;
import com.saida.sdk.common.HttpResultResponse;
import com.saida.sdk.common.PaginationData;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @version 0.3
 * @date 2021/12/22
 */
public interface IHttpWaylineService {

    String PREFIX = "wayline/api/v1";

    /**
     * Query the basic data of the wayline file according to the query conditions.
     * The query condition field in pilot is fixed.
     * @param workspaceId workspace id
     * @param request   get waylines params
     * @param req
     * @param rsp
     * @return  wayline list
     */
    @GetMapping(PREFIX + "/workspaces/{workspace_id}/waylines")
    HttpResultResponse<PaginationData<GetWaylineListResponse>> getWaylineList(
            @Valid GetWaylineListRequest request,
            @PathVariable(name = "workspace_id") String workspaceId,
            HttpServletRequest req, HttpServletResponse rsp);

    /**
     * Query the download address of the file according to the wayline file id,
     * and redirect to this address directly for download.
     * @param workspaceId   workspace id
     * @param waylineId     wayline file id
     * @param req
     * @param rsp
     */
    @GetMapping(PREFIX + "/workspaces/{workspace_id}/waylines/{wayline_id}/url")
    void getWaylineFileDownloadAddress(
            @PathVariable(name = "workspace_id") String workspaceId,
            @PathVariable(name = "wayline_id") String waylineId,
            HttpServletRequest req, HttpServletResponse rsp);

    /**
     * Checking whether the name already exists according to the wayline name must ensure the uniqueness of the wayline name.
     * This interface will be called when uploading waylines and must be available.
     * @param workspaceId workspace id
     * @param names  wayline file name collection
     * @param req
     * @param rsp
     * @return  already existing wayline name
     */
    @GetMapping(PREFIX + "/workspaces/{workspace_id}/waylines/duplicate-names")
    HttpResultResponse<List<String>> getDuplicatedWaylineName(
            @PathVariable(name = "workspace_id") String workspaceId,
            @NotNull @Size(min = 1) @RequestParam(name = "name") List<String> names,
            HttpServletRequest req, HttpServletResponse rsp);

    /**
     * When the wayline file is uploaded to the storage server by pilot,
     * the basic information of the file is reported through this interface.
     * @param workspaceId   workspace id
     * @param request   upload callback params
     * @param req
     * @param rsp
     * @return  success
     */
    @PostMapping(PREFIX + "/workspaces/{workspace_id}/upload-callback")
    HttpResultResponse fileUploadResultReport(
            @PathVariable(name = "workspace_id") String workspaceId,
            @Valid @RequestBody WaylineUploadCallbackRequest request,
            HttpServletRequest req, HttpServletResponse rsp);

    /**
     * Favorite the wayline file according to the wayline file id.
     * @param workspaceId   workspace id
     * @param ids   wayline file id
     * @param req
     * @param rsp
     * @return  success
     */
    @PostMapping(PREFIX + "/workspaces/{workspace_id}/favorites")
    HttpResultResponse batchFavoritesWayline(
            @PathVariable(name = "workspace_id") String workspaceId,
            @NotNull @Size(min = 1) @RequestParam(name = "id") List<String> ids,
            HttpServletRequest req, HttpServletResponse rsp);

    /**
     * Delete the favorites of this wayline file based on the wayline file id.
     * @param workspaceId   workspace id
     * @param ids   wayline file id
     * @param req
     * @param rsp
     * @return  success
     */
    @DeleteMapping(PREFIX + "/workspaces/{workspace_id}/favorites")
    HttpResultResponse batchUnfavoritesWayline(
            @PathVariable(name = "workspace_id") String workspaceId,
            @NotNull @Size(min = 1) @RequestParam(name = "id") List<String> ids,
            HttpServletRequest req, HttpServletResponse rsp);
}
