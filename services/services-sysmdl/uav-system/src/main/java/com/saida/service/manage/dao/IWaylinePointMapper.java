package com.saida.service.manage.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.saida.service.manage.model.entity.WaylinePointEntity;
import com.saida.services.open.req.uav.GetWayLinePointReq;
import com.saida.services.open.resp.uav.GetWayLinePointResp;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName IWaylinePointMapper
 * @Desc
 * @Date 2025/2/22 14:53
 */
@Mapper
public interface IWaylinePointMapper extends BaseMapper<WaylinePointEntity> {

    List<GetWayLinePointResp> getWayLinePoint(GetWayLinePointReq req);
}
