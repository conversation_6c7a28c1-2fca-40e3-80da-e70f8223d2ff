package com.saida.service.map.service;

import com.saida.service.map.model.dto.DeviceDataStatusDTO;
import com.saida.service.map.model.dto.DeviceFlightAreaDTO;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.9
 * @date 2023/11/24
 */
public interface IDeviceDataService {

    List<DeviceDataStatusDTO> getDevicesDataStatus(String workspaceId);

    Optional<DeviceFlightAreaDTO> getDeviceStatus(String workspaceId, String deviceSn);
}
