package com.saida.sdk.cloudapi.tsa;

import com.saida.sdk.common.BaseModel;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.7
 * @date 2023/6/16
 */
public class TopologyResponse extends BaseModel {

    @NotNull
    private List<@Valid TopologyList> list;

    public TopologyResponse() {
    }

    @Override
    public String toString() {
        return "TopologyResponse{" +
                "list=" + list +
                '}';
    }

    public List<TopologyList> getList() {
        return list;
    }

    public TopologyResponse setList(List<TopologyList> list) {
        this.list = list;
        return this;
    }
}
