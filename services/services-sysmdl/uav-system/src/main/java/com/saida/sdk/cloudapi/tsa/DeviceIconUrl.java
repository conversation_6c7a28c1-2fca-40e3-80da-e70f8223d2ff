package com.saida.sdk.cloudapi.tsa;

import com.fasterxml.jackson.annotation.JsonProperty;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 0.3
 * @date 2022/1/5
 */
public class DeviceIconUrl {

     @JsonProperty("normal_icon_url")
    @NotNull
    private String normalIconUrl;

     @JsonProperty("selected_icon_url")
    @NotNull
    private String selectIconUrl;

    public DeviceIconUrl() {
    }

    @Override
    public String toString() {
        return "DeviceIconUrl{" +
                "normalIconUrl='" + normalIconUrl + '\'' +
                ", selectIconUrl='" + selectIconUrl + '\'' +
                '}';
    }

    public String getNormalIconUrl() {
        return normalIconUrl;
    }

    public DeviceIconUrl setNormalIconUrl(String normalIconUrl) {
        this.normalIconUrl = normalIconUrl;
        return this;
    }

    public String getSelectIconUrl() {
        return selectIconUrl;
    }

    public DeviceIconUrl setSelectIconUrl(String selectIconUrl) {
        this.selectIconUrl = selectIconUrl;
        return this;
    }
}
