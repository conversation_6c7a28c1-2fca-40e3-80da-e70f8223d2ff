package com.saida.sdk.cloudapi.media;


import com.fasterxml.jackson.annotation.JsonProperty;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.7
 * @date 2023/6/16
 */
public class GetFileFingerprintRequest {

    @NotNull
     @JsonProperty("tiny_fingerprints")
    private List<String> tinyFingerprints;

    public GetFileFingerprintRequest() {
    }

    @Override
    public String toString() {
        return "GetFileFingerprintRequest{" +
                "tinyFingerprints=" + tinyFingerprints +
                '}';
    }

    public List<String> getTinyFingerprints() {
        return tinyFingerprints;
    }

    public GetFileFingerprintRequest setTinyFingerprints(List<String> tinyFingerprints) {
        this.tinyFingerprints = tinyFingerprints;
        return this;
    }
}
