package com.saida.service.manage.service.impl;

import com.saida.sdk.annotations.CloudSDKVersion;
import com.saida.sdk.cloudapi.property.DockDroneCommanderFlightHeight;
import com.saida.sdk.cloudapi.property.DockDroneCommanderModeLostAction;
import com.saida.sdk.cloudapi.property.DockDroneRthMode;
import com.saida.sdk.cloudapi.wayline.FlighttaskProgress;
import com.saida.sdk.config.version.CloudSDKVersionEnum;
import com.saida.sdk.config.version.GatewayTypeEnum;
import com.saida.sdk.mqtt.ChannelName;
import com.saida.sdk.mqtt.state.TopicStateResponse;
import com.saida.service.component.mq.RocketMQTopicConst;
import com.saida.service.component.mqtt.model.EventsReceiver;
import com.saida.service.component.redis.RedisConst;
import com.saida.service.component.redis.RedisOpsUtils;
import com.saida.service.component.websocket.model.BizCodeEnum;
import com.saida.service.component.websocket.service.IWebSocketMessageService;
import com.saida.service.manage.model.dto.DeviceDTO;
import com.saida.service.manage.model.dto.DevicePayloadReceiver;
import com.saida.service.manage.model.entity.WaylinePointEntity;
import com.saida.service.manage.model.enums.DeviceFirmwareStatusEnum;
import com.saida.service.manage.model.param.DeviceQueryParam;
import com.saida.service.manage.service.*;
import com.saida.sdk.cloudapi.device.*;
import com.saida.sdk.cloudapi.device.api.AbstractDeviceService;
import com.saida.sdk.cloudapi.tsa.DeviceIconUrl;
import com.saida.sdk.cloudapi.tsa.IconUrlEnum;
import com.saida.sdk.config.version.GatewayManager;
import com.saida.sdk.common.SDKManager;
import com.saida.sdk.mqtt.MqttReply;
import com.saida.sdk.mqtt.osd.TopicOsdRequest;
import com.saida.sdk.mqtt.state.TopicStateRequest;
import com.saida.sdk.mqtt.status.TopicStatusRequest;
import com.saida.sdk.mqtt.status.TopicStatusResponse;
import com.saida.service.wayline.service.IWaylineRedisService;
import com.saida.services.common.rocketMq.RocketMQEnhanceTemplate;
import com.saida.services.common.rocketMq.message.OsdMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageHeaders;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.7
 * @date 2023/7/4
 */
@Service
@Slf4j
public class SDKDeviceService extends AbstractDeviceService {

    @Autowired
    private IDeviceRedisService deviceRedisService;

    @Autowired
    private IWaylineRedisService waylineRedisService;

    @Autowired
    private IWaylinePointService waylinePointService;

    @Autowired
    private IDeviceService deviceService;

    @Autowired
    private IDeviceDictionaryService dictionaryService;

    @Autowired
    private IWebSocketMessageService webSocketMessageService;

    @Autowired
    private IDevicePayloadService devicePayloadService;

    @Autowired
    private RocketMQEnhanceTemplate rocketMQEnhanceTemplate;

    @Override
    public TopicStatusResponse<MqttReply> updateTopoOnline(TopicStatusRequest<UpdateTopo> request, MessageHeaders headers) {
        UpdateTopoSubDevice updateTopoSubDevice = request.getData().getSubDevices().get(0);
        String deviceSn = updateTopoSubDevice.getSn();

        Optional<DeviceDTO> deviceOpt = deviceRedisService.getDeviceOnline(deviceSn);
        Optional<DeviceDTO> gatewayOpt = deviceRedisService.getDeviceOnline(request.getFrom());
        GatewayManager gatewayManager = SDKManager.registerDevice(request.getFrom(), deviceSn,
                request.getData().getDomain(), request.getData().getType(),
                request.getData().getSubType(), request.getData().getThingVersion(), updateTopoSubDevice.getThingVersion());

        if (deviceOpt.isPresent() && gatewayOpt.isPresent()) {
            deviceOnlineAgain(deviceOpt.get().getWorkspaceId(), request.getFrom(), deviceSn);
            return new TopicStatusResponse<MqttReply>().setData(MqttReply.success());
        }

        changeSubDeviceParent(deviceSn, request.getFrom());

        DeviceDTO gateway = deviceGatewayConvertToDevice(request.getFrom(), request.getData());
        Optional<DeviceDTO> gatewayEntityOpt = onlineSaveDevice(gateway, deviceSn, null);
        if (!gatewayEntityOpt.isPresent()) {
            log.error("Failed to go online, please check the status data or code logic.");
            return null;
        }
        DeviceDTO subDevice = subDeviceConvertToDevice(updateTopoSubDevice);
        Optional<DeviceDTO> subDeviceEntityOpt = onlineSaveDevice(subDevice, null, gateway.getDeviceSn());
        if (!subDeviceEntityOpt.isPresent()) {
            log.error("Failed to go online, please check the status data or code logic.");
            return null;
        }
        subDevice = subDeviceEntityOpt.get();
        gateway = gatewayEntityOpt.get();
        dockGoOnline(gateway, subDevice);
        deviceService.gatewayOnlineSubscribeTopic(gatewayManager);

        if (!StringUtils.hasText(subDevice.getWorkspaceId())) {
            return new TopicStatusResponse<MqttReply>().setData(MqttReply.success());
        }

        // Subscribe to topic related to drone devices.
        deviceService.subDeviceOnlineSubscribeTopic(gatewayManager);
        deviceService.pushDeviceOnlineTopo(gateway.getWorkspaceId(), gateway.getDeviceSn(), subDevice.getDeviceSn());

        log.debug("{} online.", subDevice.getDeviceSn());
        return new TopicStatusResponse<MqttReply>().setData(MqttReply.success());
    }

    @Override
    public TopicStatusResponse<MqttReply> updateTopoOffline(TopicStatusRequest<UpdateTopo> request, MessageHeaders headers) {
        GatewayManager gatewayManager = SDKManager.registerDevice(request.getFrom(), null,
                request.getData().getDomain(), request.getData().getType(),
                request.getData().getSubType(), request.getData().getThingVersion(), null);
        deviceService.gatewayOnlineSubscribeTopic(gatewayManager);
        // Only the remote controller is logged in and the aircraft is not connected.
        Optional<DeviceDTO> deviceOpt = deviceRedisService.getDeviceOnline(request.getFrom());
        if (!deviceOpt.isPresent()) {
            // When connecting for the first time
            DeviceDTO gatewayDevice = deviceGatewayConvertToDevice(request.getFrom(), request.getData());
            Optional<DeviceDTO> gatewayDeviceOpt = onlineSaveDevice(gatewayDevice, null, null);
            if (!gatewayDeviceOpt.isPresent()) {
                return null;
            }
            deviceService.pushDeviceOnlineTopo(gatewayDeviceOpt.get().getWorkspaceId(), request.getFrom(), null);
            return new TopicStatusResponse<MqttReply>().setData(MqttReply.success());
        }

        String deviceSn = deviceOpt.get().getChildDeviceSn();
        if (!StringUtils.hasText(deviceSn)) {
            return new TopicStatusResponse<MqttReply>().setData(MqttReply.success());
        }

        deviceService.subDeviceOffline(deviceSn);
        return new TopicStatusResponse<MqttReply>().setData(MqttReply.success());
    }

    @Override
    public void osdDock(TopicOsdRequest<OsdDock> request, MessageHeaders headers) {
        String from = request.getFrom();
        Optional<DeviceDTO> deviceOpt = deviceRedisService.getDeviceOnline(from);
        if (!deviceOpt.isPresent() || !StringUtils.hasText(deviceOpt.get().getWorkspaceId())) {
            deviceOpt = deviceService.getDeviceBySn(from);
            if (!deviceOpt.isPresent()) {
                log.error("Please restart the drone.");
                return;
            }
        }

        DeviceDTO device = deviceOpt.get();
        if (!StringUtils.hasText(device.getWorkspaceId())) {
            log.error("Please bind the dock first.");
        }
        if (StringUtils.hasText(device.getChildDeviceSn())) {
            deviceService.getDeviceBySn(device.getChildDeviceSn()).ifPresent(device::setChildren);
        }

        deviceRedisService.setDeviceOnline(device);
        fillDockOsd(from, request.getData());

        // 发送mq消息
        if (Objects.isNull(RedisOpsUtils.get(RedisConst.OSD_DOCK_MESSAGE_LOCK + from))) {
            // 发送osd消息
            rocketMQEnhanceTemplate.send(RocketMQTopicConst.OSD_MESSAGE, buildOsdMessage(request.getData(), deviceOpt.get()));
            RedisOpsUtils.setWithExpire(RedisConst.OSD_DOCK_MESSAGE_LOCK + from, from, 5);
        }

        deviceService.pushOsdDataToWeb(device.getWorkspaceId(), BizCodeEnum.DOCK_OSD, from, request.getData());
    }

    private OsdMessage buildOsdMessage(OsdDock osdDock, DeviceDTO device) {
        return OsdMessage.builder()
                .sn(device.getDeviceSn())
                .name(device.getDeviceName())
                .model(DeviceTypeNameEnum.find(device.getType().getType()).getName())
                .domain(device.getDomain().getDomain())
                .lon(Objects.nonNull(osdDock.getLongitude()) ? osdDock.getLongitude().toString() : null)
                .lat(Objects.nonNull(osdDock.getLatitude()) ? osdDock.getLatitude().toString() : null)
//                .firmwareVersion(osdDock.getFirmwareVersion())
//                .firmwareStatus(osdDock.getFirmwareStatus())
                .mainControlSn(device.getDeviceSn())
                .dockModeCode(Objects.nonNull(osdDock.getModeCode()) ? osdDock.getModeCode().getCode() : null)
                .airConditionerState(Objects.nonNull(osdDock.getAirConditioner()) ? osdDock.getAirConditioner().getAirConditionerState().getState() : null)
                .jobNumber(osdDock.getJobNumber())
                .workingCurrent(osdDock.getWorkingCurrent())
                .workingVoltage(osdDock.getWorkingVoltage())
                .landPointIsConfigured(Objects.nonNull(osdDock.getAlternateLandPoint()) && osdDock.getAlternateLandPoint().getConfigured() ? 1 : 0)
                .backupBatterySwitch(Objects.nonNull(osdDock.getBackupBattery()) && osdDock.getBackupBattery().getBatterySwitch() ? 1 : 0)
                .backupBatteryVoltage(Objects.nonNull(osdDock.getBackupBattery()) ? osdDock.getBackupBattery().getVoltage() : 0)
                .backupBatteryTemperature(Objects.nonNull(osdDock.getBackupBattery()) ? BigDecimal.valueOf(osdDock.getBackupBattery().getTemperature()) : null)
                .humidity(osdDock.getHumidity())
                .temperature(Objects.nonNull(osdDock.getTemperature()) ? BigDecimal.valueOf(osdDock.getTemperature()) : null)
                .environmentTemperature(Objects.nonNull(osdDock.getEnvironmentTemperature()) ? BigDecimal.valueOf(osdDock.getEnvironmentTemperature()) : null)
                .batteryCapacityPercent(Objects.nonNull(osdDock.getDroneChargeState()) ? osdDock.getDroneChargeState().getCapacityPercent() : null)
                .build();
    }

    @Override
    public void osdDockDrone(TopicOsdRequest<OsdDockDrone> request, MessageHeaders headers) {
        String from = request.getFrom();
        Optional<DeviceDTO> deviceOpt = deviceRedisService.getDeviceOnline(from);
        if (!deviceOpt.isPresent()) {
            deviceOpt = deviceService.getDeviceBySn(from);
            if (!deviceOpt.isPresent()) {
                log.error("Please restart the drone.");
                return;
            }
        }

        if (!StringUtils.hasText(deviceOpt.get().getWorkspaceId())) {
            log.error("Please restart the drone.");
        }

        DeviceDTO device = deviceOpt.get();
        deviceRedisService.setDeviceOnline(device);
        deviceRedisService.setDeviceOsd(from, request.getData());

        // 判断无人机是否在执行任务,在执行任务新增点位信息
        Optional<EventsReceiver<FlighttaskProgress>> runningWaylineJob = waylineRedisService.getRunningWaylineJob(request.getGateway());
        if (runningWaylineJob.isPresent()) {
            if (Objects.isNull(RedisOpsUtils.get(RedisConst.WAYLINE_POINT_LOCK + request.getGateway()))) {
                waylinePointService.save(buildWaylinePointEntity(request.getData(), runningWaylineJob.get().getOutput().getExt().getFlightId()));
                RedisOpsUtils.setWithExpire(RedisConst.WAYLINE_POINT_LOCK + request.getGateway(), request.getGateway(), 2);
            }
        }

        // 发送mq消息
        if (Objects.isNull(RedisOpsUtils.get(RedisConst.OSD_DOCK_DRONE_MESSAGE_LOCK + request.getGateway()))) {
            // 发送osd消息
            rocketMQEnhanceTemplate.send(RocketMQTopicConst.OSD_MESSAGE, buildOsdMessage(request.getData(), deviceOpt.get()));
            RedisOpsUtils.setWithExpire(RedisConst.OSD_DOCK_DRONE_MESSAGE_LOCK + request.getGateway(), request.getGateway(), 5);
        }

        deviceService.pushOsdDataToWeb(device.getWorkspaceId(), BizCodeEnum.DEVICE_OSD, from, request.getData());
    }

    private WaylinePointEntity buildWaylinePointEntity(OsdDockDrone drone, String jobId) {
        WaylinePointEntity entity = new  WaylinePointEntity();
        entity.setJobId(jobId);
        entity.setLongitude(drone.getLongitude().toString());
        entity.setLatitude(drone.getLatitude().toString());
        return entity;
    }

    private OsdMessage buildOsdMessage(OsdDockDrone osdDockDrone, DeviceDTO device) {
        return OsdMessage.builder()
                .sn(device.getDeviceSn())
                .name(device.getDeviceName())
                .model(DeviceTypeNameEnum.find(device.getType().getType()).getName())
                .domain(device.getDomain().getDomain())
                .lon(Objects.nonNull(osdDockDrone.getLongitude()) ? osdDockDrone.getLongitude().toString() : null)
                .lat(Objects.nonNull(osdDockDrone.getLatitude()) ? osdDockDrone.getLatitude().toString() : null)
                .firmwareVersion(osdDockDrone.getFirmwareVersion())
//                .firmwareStatus(osdDockDrone.getFirmwareStatus())
                .mainControlSn(device.getDeviceSn())
                .droneModeCode(Objects.nonNull(osdDockDrone.getModeCode()) ? osdDockDrone.getModeCode().getCode() : null)
//                .jobNumber(osdDockDrone.getJobNumber())
//                .workingCurrent(osdDockDrone.getWorkingCurrent())
//                .workingVoltage(osdDockDrone.getWorkingVoltage())
//                .landPointIsConfigured(osdDock.getAlternateLandPoint().getConfigured() ? 1 : 0)
//                .backupBatterySwitch(osdDock.getBackupBattery().getBatterySwitch() ? 1 : 0)
//                .backupBatteryVoltage(osdDock.getBackupBattery().getVoltage())
//                .backupBatteryTemperature(BigDecimal.valueOf(osdDock.getTemperature()))
//                .humidity(osdDock.getHumidity())
//                .temperature(BigDecimal.valueOf(osdDockDrone.getTemperature()))
//                .environmentTemperature(BigDecimal.valueOf(osdDock.getEnvironmentTemperature()))
//                .batteryCapacityPercent(osdDock.getDroneChargeState().getCapacityPercent())
                .heightLimit(osdDockDrone.getHeightLimit())
                .distanceLimit(osdDockDrone.getDistanceLimitStatus().getDistanceLimit())
                .build();
    }

    @Override
    public void osdRemoteControl(TopicOsdRequest<OsdRemoteControl> request, MessageHeaders headers) {
        String from = request.getFrom();
        Optional<DeviceDTO> deviceOpt = deviceRedisService.getDeviceOnline(from);
        if (!deviceOpt.isPresent()) {
            deviceOpt = deviceService.getDeviceBySn(from);
            if (!deviceOpt.isPresent()) {
                log.error("Please restart the drone.");
                return;
            }
        }
        DeviceDTO device = deviceOpt.get();
        if (StringUtils.hasText(device.getChildDeviceSn())) {
            deviceService.getDeviceBySn(device.getChildDeviceSn()).ifPresent(device::setChildren);
        }
        deviceRedisService.setDeviceOnline(device);

        OsdRemoteControl data = request.getData();
        deviceService.pushOsdDataToPilot(device.getWorkspaceId(), from,
                new DeviceOsdHost()
                        .setLatitude(data.getLatitude())
                        .setLongitude(data.getLongitude())
                        .setHeight(data.getHeight()));
        deviceService.pushOsdDataToWeb(device.getWorkspaceId(), BizCodeEnum.RC_OSD, from, data);

    }

    @Override
    public void osdRcDrone(TopicOsdRequest<OsdRcDrone> request, MessageHeaders headers) {
        String from = request.getFrom();
        Optional<DeviceDTO> deviceOpt = deviceRedisService.getDeviceOnline(from);
        if (!deviceOpt.isPresent()) {
            deviceOpt = deviceService.getDeviceBySn(from);
            if (!deviceOpt.isPresent()) {
                log.error("Please restart the drone.");
                return;
            }
        }
        DeviceDTO device = deviceOpt.get();
        if (!StringUtils.hasText(device.getWorkspaceId())) {
            log.error("Please bind the drone first.");
        }

        deviceRedisService.setDeviceOnline(device);

        OsdRcDrone data = request.getData();
        deviceService.pushOsdDataToPilot(device.getWorkspaceId(), from,
                new DeviceOsdHost()
                        .setLatitude(data.getLatitude())
                        .setLongitude(data.getLongitude())
                        .setElevation(data.getElevation())
                        .setHeight(data.getHeight())
                        .setAttitudeHead(data.getAttitudeHead())
                        .setElevation(data.getElevation())
                        .setHorizontalSpeed(data.getHorizontalSpeed())
                        .setVerticalSpeed(data.getVerticalSpeed()));
        deviceService.pushOsdDataToWeb(device.getWorkspaceId(), BizCodeEnum.DEVICE_OSD, from, data);
    }

    @Override
    public void dockFirmwareVersionUpdate(TopicStateRequest<DockFirmwareVersion> request, MessageHeaders headers) {
        // If the reported version is empty, it will not be processed to prevent misleading page.
        if (!StringUtils.hasText(request.getData().getFirmwareVersion())) {
            return;
        }

        DeviceDTO device = DeviceDTO.builder()
                .deviceSn(request.getFrom())
                .firmwareVersion(request.getData().getFirmwareVersion())
                .firmwareStatus(request.getData().getNeedCompatibleStatus() ?
                        DeviceFirmwareStatusEnum.UNKNOWN : DeviceFirmwareStatusEnum.CONSISTENT_UPGRADE)
                .build();
        boolean isUpd = deviceService.updateDevice(device);
        if (!isUpd) {
            log.error("Data update of firmware version failed. SN: {}", request.getFrom());
        }
    }

    @Override
    public void rcAndDroneFirmwareVersionUpdate(TopicStateRequest<FirmwareVersion> request, MessageHeaders headers) {
        // If the reported version is empty, it will not be processed to prevent misleading page.
        if (!StringUtils.hasText(request.getData().getFirmwareVersion())) {
            return;
        }

        DeviceDTO device = DeviceDTO.builder()
                .deviceSn(request.getFrom())
                .firmwareVersion(request.getData().getFirmwareVersion())
                .build();
        boolean isUpd = deviceService.updateDevice(device);
        if (!isUpd) {
            log.error("Data update of firmware version failed. SN: {}", request.getFrom());
        }
    }

    @Override
    public void rcPayloadFirmwareVersionUpdate(TopicStateRequest<PayloadFirmwareVersion> request, MessageHeaders headers) {
        // If the reported version is empty, it will not be processed to prevent misleading page.
        if (!StringUtils.hasText(request.getData().getFirmwareVersion())) {
            return;
        }

        boolean isUpd = devicePayloadService.updateFirmwareVersion(request.getFrom(), request.getData());
        if (!isUpd) {
            log.error("Data update of payload firmware version failed. SN: {}", request.getFrom());
        }
    }

    @Override
    public void dockControlSourceUpdate(TopicStateRequest<DockDroneControlSource> request, MessageHeaders headers) {
        // If the control source is empty, it will not be processed.
        if (ControlSourceEnum.UNKNOWN == request.getData().getControlSource()) {
            return;
        }
        Optional<DeviceDTO> deviceOpt = deviceRedisService.getDeviceOnline(request.getFrom());
        if (!deviceOpt.isPresent()) {
            return;
        }
        Optional<DeviceDTO> dockOpt = deviceRedisService.getDeviceOnline(request.getGateway());
        if (!dockOpt.isPresent()) {
            return;
        }

        deviceService.updateFlightControl(dockOpt.get(), request.getData().getControlSource());
        devicePayloadService.updatePayloadControl(deviceOpt.get(),
                request.getData().getPayloads().stream()
                        .map(p -> DevicePayloadReceiver.builder()
                                .controlSource(p.getControlSource())
                                .payloadIndex(p.getPayloadIndex())
                                .sn(p.getSn())
                                .deviceSn(request.getFrom())
                                .build()).collect(Collectors.toList()));
    }

    @Override
    public void rcControlSourceUpdate(TopicStateRequest<RcDroneControlSource> request, MessageHeaders headers) {
        // If the control source is empty, it will not be processed.
        if (ControlSourceEnum.UNKNOWN == request.getData().getControlSource()) {
            return;
        }
        Optional<DeviceDTO> deviceOpt = deviceRedisService.getDeviceOnline(request.getFrom());
        if (!deviceOpt.isPresent()) {
            return;
        }
        Optional<DeviceDTO> dockOpt = deviceRedisService.getDeviceOnline(request.getGateway());
        if (!dockOpt.isPresent()) {
            return;
        }

        deviceService.updateFlightControl(dockOpt.get(), request.getData().getControlSource());
        devicePayloadService.updatePayloadControl(deviceOpt.get(),
                request.getData().getPayloads().stream()
                        .map(p -> DevicePayloadReceiver.builder()
                                .controlSource(p.getControlSource())
                                .payloadIndex(p.getPayloadIndex())
                                .sn(p.getSn())
                                .deviceSn(request.getFrom())
                                .build()).collect(Collectors.toList()));
    }

    private void dockGoOnline(DeviceDTO gateway, DeviceDTO subDevice) {
        if (DeviceDomainEnum.DOCK != gateway.getDomain()) {
            return;
        }
        if (!StringUtils.hasText(gateway.getWorkspaceId())) {
            log.error("The dock is not bound, please bind it first and then go online.");
            return;
        }
        if (!Optional.ofNullable(subDevice.getBoundStatus()).orElse(false)) {
            // Directly bind the drone of the dock to the same workspace as the dock.
            deviceService.bindDevice(DeviceDTO.builder().deviceSn(subDevice.getDeviceSn()).workspaceId(gateway.getWorkspaceId()).build());
            subDevice.setWorkspaceId(gateway.getWorkspaceId());
        }
        deviceRedisService.setDeviceOnline(subDevice);
    }

    private void changeSubDeviceParent(String deviceSn, String gatewaySn) {
        List<DeviceDTO> gatewaysList = deviceService.getDevicesByParams(
                DeviceQueryParam.builder()
                        .childSn(deviceSn)
                        .build());
        gatewaysList.stream()
                .filter(gateway -> !gateway.getDeviceSn().equals(gatewaySn))
                .forEach(gateway -> {
                    gateway.setChildDeviceSn("");
                    deviceService.updateDevice(gateway);
                    deviceRedisService.getDeviceOnline(gateway.getDeviceSn())
                            .ifPresent(device -> {
                                device.setChildDeviceSn(null);
                                deviceRedisService.setDeviceOnline(device);
                            });
                });
    }


    public void deviceOnlineAgain(String workspaceId, String gatewaySn, String deviceSn) {
        DeviceDTO device = DeviceDTO.builder().loginTime(LocalDateTime.now()).deviceSn(deviceSn).build();
        DeviceDTO gateway = DeviceDTO.builder()
                .loginTime(LocalDateTime.now())
                .deviceSn(gatewaySn)
                .childDeviceSn(deviceSn).build();
        deviceService.updateDevice(gateway);
        deviceService.updateDevice(device);
        gateway = deviceRedisService.getDeviceOnline(gatewaySn).map(g -> {
            g.setChildDeviceSn(deviceSn);
            return g;
        }).get();
        device = deviceRedisService.getDeviceOnline(deviceSn).map(d -> {
            d.setParentSn(gatewaySn);
            return d;
        }).get();
        deviceRedisService.setDeviceOnline(gateway);
        deviceRedisService.setDeviceOnline(device);
        if (StringUtils.hasText(workspaceId)) {
            deviceService.subDeviceOnlineSubscribeTopic(SDKManager.getDeviceSDK(gatewaySn));
        }

        log.warn("{} is already online.", deviceSn);
    }

    /**
     * Convert the received gateway device object into a database entity object.
     * @param gateway
     * @return
     */
    private DeviceDTO deviceGatewayConvertToDevice(String gatewaySn, UpdateTopo gateway) {
        if (null == gateway) {
            throw new IllegalArgumentException();
        }
        return DeviceDTO.builder()
                .deviceSn(gatewaySn)
                .subType(gateway.getSubType())
                .type(gateway.getType())
                .thingVersion(gateway.getThingVersion())
                .domain(gateway.getDomain())
                .controlSource(gateway.getSubDevices().isEmpty() ? null :
                        ControlSourceEnum.find(gateway.getSubDevices().get(0).getIndex().getControlSource()))
                .build();
    }

    /**
     * Convert the received drone device object into a database entity object.
     * @param device
     * @return
     */
    private DeviceDTO subDeviceConvertToDevice(UpdateTopoSubDevice device) {
        if (null == device) {
            throw new IllegalArgumentException();
        }
        return DeviceDTO.builder()
                .deviceSn(device.getSn())
                .type(device.getType())
                .subType(device.getSubType())
                .thingVersion(device.getThingVersion())
                .domain(device.getDomain())
                .build();
    }

    private Optional<DeviceDTO> onlineSaveDevice(DeviceDTO device, String childSn, String parentSn) {

        device.setChildDeviceSn(childSn);
        device.setLoginTime(LocalDateTime.now());

        Optional<DeviceDTO> deviceOpt = deviceService.getDeviceBySn(device.getDeviceSn());

        if (!deviceOpt.isPresent()) {
            device.setIconUrl(new DeviceIconUrl());
            // Set the icon of the gateway device displayed in the pilot's map, required in the TSA module.
            device.getIconUrl().setNormalIconUrl(IconUrlEnum.NORMAL_PERSON.getUrl());
            // Set the icon of the gateway device displayed in the pilot's map when it is selected, required in the TSA module.
            device.getIconUrl().setSelectIconUrl(IconUrlEnum.SELECT_PERSON.getUrl());
            device.setBoundStatus(false);

            // Query the model information of this gateway device.
            dictionaryService.getOneDictionaryInfoByTypeSubType(
                    device.getDomain().getDomain(), device.getType().getType(), device.getSubType().getSubType())
                    .ifPresent(entity -> {
                        device.setDeviceName(entity.getDeviceName());
                        device.setNickname(entity.getDeviceName());
                        device.setDeviceDesc(entity.getDeviceDesc());
                    });
        }
        boolean success = deviceService.saveOrUpdateDevice(device);
        if (!success) {
            return Optional.empty();
        }

        deviceOpt = deviceService.getDeviceBySn(device.getDeviceSn());
        DeviceDTO redisDevice = deviceOpt.get();
        redisDevice.setStatus(true);
        redisDevice.setParentSn(parentSn);

        deviceRedisService.setDeviceOnline(redisDevice);
        return deviceOpt;
    }

    private void fillDockOsd(String dockSn, OsdDock dock) {
        Optional<OsdDock> oldDockOpt = deviceRedisService.getDeviceOsd(dockSn, OsdDock.class);
        if (Objects.nonNull(dock.getJobNumber())) {
            return;
        }
        if (!oldDockOpt.isPresent()) {
            deviceRedisService.setDeviceOsd(dockSn, dock);
            return;
        }
        OsdDock oldDock = oldDockOpt.get();
        if (Objects.nonNull(dock.getModeCode())) {
            dock.setDrcState(oldDock.getDrcState());
            deviceRedisService.setDeviceOsd(dockSn, dock);
            return;
        }
        if (Objects.nonNull(dock.getDrcState()) ) {
            oldDock.setDrcState(dock.getDrcState());
            deviceRedisService.setDeviceOsd(dockSn, oldDock);
        }
    }

    @Override
    public void dockLiveStatusUpdate(TopicStateRequest<DockLiveStatus> request, MessageHeaders headers) {
    }

    @Override
    public void rcLiveStatusUpdate(TopicStateRequest<RcLiveStatus> request, MessageHeaders headers) {
    }

    @Override
    public void dockWpmzVersionUpdate(TopicStateRequest<DockDroneWpmzVersion> request, MessageHeaders headers) {
    }

    @Override
    public void dockThermalSupportedPaletteStyle(TopicStateRequest<DockDroneThermalSupportedPaletteStyle> request, MessageHeaders headers) {
    }

    @Override
    public TopicStateResponse<MqttReply> dockDroneRthMode(TopicStateRequest<DockDroneRthMode> request, MessageHeaders headers) {
        return new TopicStateResponse();
    }

    @Override
    public TopicStateResponse<MqttReply> dockDroneCurrentRthMode(TopicStateRequest<DockDroneCurrentRthMode> request, MessageHeaders headers) {
        return new TopicStateResponse();
    }

    @Override
    public TopicStateResponse<MqttReply> dockDroneCommanderModeLostAction(TopicStateRequest<DockDroneCommanderModeLostAction> request, MessageHeaders headers) {
        return new TopicStateResponse();
    }

    @Override
    public TopicStateResponse<MqttReply> dockDroneCurrentCommanderFlightMode(TopicStateRequest<DockDroneCurrentCommanderFlightMode> request, MessageHeaders headers) {
        return new TopicStateResponse();
    }

    @Override
    public TopicStateResponse<MqttReply> dockDroneCommanderFlightHeight(TopicStateRequest<DockDroneCommanderFlightHeight> request, MessageHeaders headers) {
        return new TopicStateResponse();
    }

    @Override
    public TopicStateResponse<MqttReply> dockDroneModeCodeReason(TopicStateRequest<DockDroneModeCodeReason> request, MessageHeaders headers) {
        return new TopicStateResponse();
    }

    @Override
    public TopicStateResponse<MqttReply> dongleInfos(TopicStateRequest<DongleInfos> request, MessageHeaders headers) {
        return new TopicStateResponse();
    }

    @Override
    public TopicStateResponse<MqttReply> offlineMapEnable(TopicStateRequest<DongleInfos> request, MessageHeaders headers) {
        return new TopicStateResponse();
    }

    @Override
    public TopicStateResponse<MqttReply> dockSilentMode(TopicStateRequest<DockSilentMode> request, MessageHeaders headers) {
        return new TopicStateResponse();
    }

}
