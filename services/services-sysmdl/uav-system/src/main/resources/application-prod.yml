server:
  port: 6789
spring:
#  profiles:
#    active: dev
  main:
    allow-bean-definition-overriding: true
  datasource:
    druid:
      type: com.alibaba.druid.pool.DruidDataSource
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: *************************************************************************************************************************************
      username: sanhe_wetland
      password: ZPgWv4kYsDVW2pnRxqq
      initial-size: 10
      min-idle: 10
      max-active: 20
      max-wait: 60000

  # MyBatis Plus配置
  #mybatis-plus:
  # 开启mybatisPlus日志（输出到控制台）
  #  configuration:
  #    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  rabbitmq:
    host: 127.0.0.1
    port: 5672
    username: saida
    password: SdpwxyyhnXwB9hnM
    enable: true
  redis:
    host: 127.0.0.1
    port: 6379
    database: 0
    username:
    password: xXU5SBWUbYD8ReBi.s
    lettuce:
      pool:
        max-active: -1
        max-idle: 50
        min-idle: 100
    connect-timeout: 5000

  servlet:
    multipart:
      max-file-size: 2GB
      max-request-size: 2GB

jwt:
  issuer: DJI
  subject: CloudApiSample
  secret: CloudApiSample
  age: 14400

mqtt:
  # @see com.dji.sample.component.mqtt.model.MqttUseEnum
  # BASIC parameters are required.
  BASIC:
    clientId:
    protocol: MQTT # @see com.dji.sample.component.mqtt.model.MqttProtocolEnum
    host: 127.0.0.1
    port: 1883
    username: sdadmin
    password: 9FDgV2a3zza4sQFwpRK
#    host: **************
#    port: 1883
#    username: sdadmin
#    password: 9FDgV2a3zza4sQFwpRK8

    #    client-id: zhsd
    #    host: 127.0.0.1
    #    port: 1883
    #    username:
    #    password:
    # If the protocol is ws/wss, this value is required.
    path:
  DRC:
    protocol: WSS # @see com.dji.sample.component.mqtt.model.MqttProtocolEnum
#    host: test-sanhe-wetland-uav-man.sdccx.cn
    host: sanhe-wetland-uav-man.sdccx.cn
    port: 8801
    path: /mqtt
    username: sdadmin
    password: 9FDgV2a3zza4sQFwpRK

cloud-sdk:
  mqtt:
    # Topics that need to be subscribed when initially connecting to mqtt, multiple topics are divided by ",".
    inbound-topic: sys/product/+/status,thing/product/+/requests

oss:
  enable: true
  provider: minio
#  endpoint: https://dev-sd-wetland-minio-api.sdccx.cn:58801
  endpoint: https://sanhe-wetland-minio-api.sdccx.cn:8801
  access-key: Sdmin
  secret-key: PW2KDUQyUCrQU2x5_WN
  bucket: sanhe-wetland
  expire: 3600
  region: us-east-1
  object-dir-prefix: wayline

logging:
  level:
    com.dji: info
  file:
    name: logs/cloud-api-sample.log

ntp:
  server:
    host: Google.mzr.me

# To create a license for an application: https://developer.dji.com/user/apps/#all
cloud-api:
  app:
    id: 141227
    key: 2600b3c4b8ab8588d952068dd90fd28
    license: P30CyTw2bNqA1fqGcDAOl80gJm6SuuePy8dzCD91hQXTFmgvCREi6HewliVBLaGJsBns6EbOmqhCtAUO/bxze3xQN7b4MwM0V50iL9I1EqttEYvtuAvADpipplqXxoG/BXSfcB65myyqf/exrXY7RXyYqL7zbWIYmdEMs7tl/Jk=

livestream:
  url:
    # It is recommended to use a program to create Token. https://github.com/AgoraIO/Tools/blob/master/DynamicKey/AgoraDynamicKey/java/src/main/java/io/agora/media/RtcTokenBuilder2.java
    agora:
      channel: test
      token: 007eJxTYDjy4uu085WpLzpbAm+1Pvl26iaDeUNYwJz3LVHGW+xY8tsVGBINjCwSLZNTzI2S0kwsLQyTkpINkixTDQzNEhONUyxTE+Y8TmsIZGRwe8HJysgAgSA+C0NJanEJAwMASwsiYQ==
      uid:  1178207

    # RTMP  Note: This IP is the address of the streaming server. If you want to see livestream on web page, you need to convert the RTMP stream to WebRTC stream.
    rtmp:
      url: rtmp://**************:11935/live/virtual_002

    gb28181:
      serverIP: *************
      serverPort: 5061
      serverID: 34020000002000000001
      agentID: 34020000001180000220
      agentPassword: admin123
      localPort: 7060
      channel: 34020000001180000220

url:
  manage:
    prefix: manage
    version: /api/v1
  map:
    prefix: map
    version: /api/v1
  media:
    prefix: media
    version: /api/v1
  wayline:
    prefix: wayline
    version: /api/v1
  storage:
    prefix: storage
    version: /api/v1
  control:
    prefix: control
    version: /api/v1