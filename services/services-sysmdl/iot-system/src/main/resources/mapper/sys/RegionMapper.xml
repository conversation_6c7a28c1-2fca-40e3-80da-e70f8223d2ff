<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.saida.services.system.sys.mapper.RegionMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.saida.services.system.sys.entity.RegionEntity" id="regionMap">
        <result property="id" column="id"/>
        <result property="parentId" column="parent_id"/>
        <result property="name" column="name"/>
        <result property="code" column="code"/>
        <result property="idChain" column="id_chain"/>
    </resultMap>

    <select id="getList" resultType="com.saida.services.system.sys.entity.RegionEntity">
        select t.*, IF(COUNT(sr.id) <![CDATA[ > ]]> 0, TRUE, FALSE) hasChild from sys_region t
        left join sys_region sr on sr.parent_id=t.id
        where t.parent_id=#{parentId}
        GROUP BY t.id
    </select>

    <select id="getRegion" resultType="com.saida.services.system.sys.entity.RegionEntity">
        select t.*, IF(COUNT(sr.id) > 0, TRUE, FALSE) hasChild,IF(COUNT(scd.id) > 0, TRUE, FALSE) hasDevice from sys_region t
        left join sys_region sr on sr.parent_id=t.id
        left join device_info di on di.region_id=t.id
        left join sys_client_device scd on scd.device_id=di.id and scd.client_id=#{appId}
        where di.deleted = 0 and t.parent_id=#{regionId}
        group by t.id
    </select>

    <select id="getRegionIdChainListByAppId" resultType="java.lang.String">
        SELECT
            t3.id_chain
        FROM
            sys_client_device t1
            LEFT JOIN device_info t2 ON t1.device_id = t2.id
            LEFT JOIN sys_region t3 ON t2.region_id = t3.id
        WHERE
            t2.deleted = 0
        AND
            t2.region_id IS NOT NULL
        AND
            t1.client_id = #{appId}
        GROUP BY
            t3.id_chain
    </select>
</mapper>