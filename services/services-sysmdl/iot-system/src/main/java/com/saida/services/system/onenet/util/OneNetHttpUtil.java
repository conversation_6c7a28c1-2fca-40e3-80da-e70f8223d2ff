package com.saida.services.system.onenet.util;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.saida.services.common.tools.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;

@Component
public class OneNetHttpUtil {

    @Autowired
    private RedisUtil redisUtil;

    @Value("${onenet.host:https://open.iot.10086.cn/fuse/http}")
    private String  URL;

    private static String  USER_TOKEN_KEY = "ONE_NET_USER_TOKEN";


    /**
     * 认证
     * @return
     */
    public String getToken(String productId, String deviceId, String accessKey) {
        String key = USER_TOKEN_KEY+":"+productId+":"+deviceId;
        Object o = redisUtil.get(key);
        if(o != null){
            return  o.toString();
        }
        String version = "2018-10-31";
        String resourceName = "products/"+productId+"/devices/"+deviceId;
        String expirationTime = System.currentTimeMillis() / 1000 + 100 * 24 * 60 * 60 + "";
        String signatureMethod = TokenUtil.SignatureMethod.SHA1.name().toLowerCase();
        String token = null;
        try {
            token = TokenUtil.assembleToken(version, resourceName, expirationTime, signatureMethod, accessKey);
            Long currentTime = System.currentTimeMillis() / 1000L;
            Long time = Long.valueOf(expirationTime) - currentTime;
            redisUtil.set(key, token,time-1200);
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        } catch (InvalidKeyException e) {
            throw new RuntimeException(e);
        }
        return token;
    }

    /***
     * 设备属性上报
     * @return
     */
    public String propertyPost(String productId, String deviceId, String accessKey, JSONObject params) {
        String topic = "$sys/"+productId+"/"+deviceId+"/thing/property/post";
        String url = URL+"/device/thing/property/post?topic="+topic+"&protocol=http";

        String response = HttpRequest.post(url)
                .header("token", this.getToken(productId, deviceId, accessKey))
                .body(JSONObject.toJSONString(params))
                .execute().body();
        JSONObject returnObject = JSON.parseObject(response);
        if (returnObject == null || !"succ".equals(returnObject.getString("error"))){
            return null;
        }

        return "succ";
    }



}
