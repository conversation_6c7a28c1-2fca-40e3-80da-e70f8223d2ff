package com.saida.services.system.manufacturer.runtu.util;

import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

public class RunTuDecoderUtil {

    private int p = 0;

    private int length = 0;

    public final Map<String, Object> map = new LinkedHashMap<>();

//    public static void main(String[] args) {
//        String str = "7E7E0121051700421234320056020006220210140000F1F121051700425AF0F02202101400091B001234452000000481FF021B027600FF03080038121152FF9C230064D20000001E000000000000020003030384000000000000000000000007000000090348EF";
//
//        RunTuProtocolUtil runTuProtocolUtil = new RunTuProtocolUtil();
//        Map<String, Object> decoder = runTuProtocolUtil.decoder(str);
//        System.out.println(JSON.toJSONString(decoder));
//    }

    public Map<String, Object> decoder(String msg){

        // 数据上报 头解码
        upHeaderDecode(msg);

        String funcCode = (String) map.get("funcCode");
        switch (funcCode) {
            // 查询遥测终端软件版本
//            case "45":
//                upVersion(msg, map);
//                break;
            // 链路维持报
            case "2F":
                linkDataDecode(msg);
                break;
            // 遥测站定时报
            case "32":
                realDataDecode(msg);
                break;
            // 遥测站加报报
//            case "33":
//                response(msg, map);
//                break;
//            // 中心站查询遥测站实时数据
//            case "37":
//                response(msg, map);
//                break;
            // 闸门控制命令上报
            case "EB":
                controlDecode(msg);
                break;
            default:
                break;
        }

        // 数据上报 尾解码
        upFootDecode(msg);

        return map;
    }


    /**
     * 数据上报 头解码
     * @param msg
     */
    public void upHeaderDecode(String msg){
        // 帧起始符
        String startFrame = msg.substring(p, p + 2 * 2);
        map.put("startFrame", startFrame);
        p += 2 * 2;

        // 中心站地址
        String centerAddress = msg.substring(p, p + 2 * 1);
        map.put("centerAddress", centerAddress);
        p += 2 * 1;

        // 遥测站地址
        String remoteAddress = msg.substring(p, p + 2 * 5);
        map.put("remoteAddress", remoteAddress);
        p += 2 * 5;


        // 密码
        String password = msg.substring(p, p + 2 * 2);
        map.put("password", password);
        p += 2 * 2;

        // 功能码
        String funcCode = msg.substring(p, p + 2 * 1);
        map.put("funcCode", funcCode);
        p += 2 * 1;

        // 上下行标识及长度
        String signLength = msg.substring(p, p + 2 * 2);
        map.put("signLength", signLength);
        p += 2 * 2;

        // 报文起始符
        String msgStart = msg.substring(p, p + 2 * 1);
        map.put("msgStart", msgStart);
        p += 2 * 1;

        // 流水号
        String no = msg.substring(p, p + 2 * 2);
        map.put("no", no);
        p += 2 * 2;

        // 发报时间
        String sendTime = msg.substring(p, p + 2 * 6);
        map.put("sendTime", sendTime);
        p += 2 * 6;
    }

    /**
     * 数据上报 尾解码
     * @param msg
     */
    public void upFootDecode(String msg){
        // 报文结束符
        String msgEnd = msg.substring(p, p + 2 * 1);
        map.put("msgEnd", msgEnd);
        p += 2 * 1;

        // 校验位
        String checkBit = msg.substring(p, p + 2 * 2);
        map.put("checkBit", checkBit);
        p += 2 * 2;
    }

    private void realDataDecode(String msg){
        // 设备地址
        String deviceAddress = msg.substring(p, p + 2 * 7);
        map.put("deviceAddress", RunTuSubUtil.deviceAddressDecode(deviceAddress));
        p += 2 * 7;

        // 观测站分类
        String siteCategory = msg.substring(p, p + 2 * 1);
        map.put("siteCategory", siteCategory);
        p += 2 * 1;

        // 观测时间
        String observeTime = msg.substring(p, p + 2 * 7);
        map.put("observeTime", RunTuSubUtil.observeTimeDecode(observeTime));
        p += 2 * 7;

        // 闸门开启高度
        String gateHeight = msg.substring(p, p + 2 * 5);
        map.put("gateHeight", RunTuSubUtil.gateHeightDecode(gateHeight));
        p += 2 * 5;

        // 遥测站状态及报警信息
        String siteAlarm = msg.substring(p, p + 2 * 6);
        map.put("siteAlarm", RunTuSubUtil.siteAlarmDecode(siteAlarm));
        p += 2 * 6;

        // 温度
        String temperature = msg.substring(p, p + 2 * 6);
        map.put("temperature", RunTuSubUtil.temperatureDecode(temperature));
        p += 2 * 6;

        // 信号强度
        String signalStrength = msg.substring(p, p + 2 * 4);
        map.put("signalStrength", RunTuSubUtil.signalStrengthDecode(signalStrength));
        p += 2 * 4;

        // 电压
        String voltage = msg.substring(p, p + 2 * 4);
        map.put("voltage", RunTuSubUtil.voltageDecode(voltage));
        p += 2 * 4;


        // 闸门数据
        // FF9C 标识符
        String signStr = msg.substring(p, p + 2 * 2);
        p += 2 * 2;

        // 字节数
        String byteLengthHex = msg.substring(p, p + 2 * 1);
        p += 2 * 1;
        int byteLength = Integer.parseInt(byteLengthHex, 16);

        String gateData = msg.substring(p, p + 2 * byteLength);

        Map<String, String> gateDataMap = getGateData(gateData);
        gateDataMap.put("signStr", signStr);
        gateDataMap.put("byteLength", String.valueOf(byteLength));

        map.put("gateData", gateDataMap);
        p += 2 * byteLength;

    }

    private Map<String, String> getGateData(String gateData) {
        Map<String, String> gateDataMap = new HashMap<>();
        int gp = 0;

        // 闸门标号,从0开始
        String gateNo = gateData.substring(gp, gp + 2 * 1);
        gateDataMap.put("gateNo", String.valueOf(Integer.parseInt(gateNo, 16)));
        gp += 2 * 1;

        // 电压 25810mV
        String voltage = gateData.substring(gp, gp + 2 * 2);
        gateDataMap.put("voltage", String.valueOf(Integer.parseInt(voltage, 16)));
        gp += 2 * 2;

        // 电流 0mA
        String current = gateData.substring(gp, gp + 2 * 2);
        gateDataMap.put("current", String.valueOf(Integer.parseInt(current, 16)));
        gp += 2 * 2;

        // 设备温度:3.0℃
        String deviceTemperature = gateData.substring(gp, gp + 2 * 2);
        gateDataMap.put("deviceTemperature", RunTuSubUtil.divideAndRound(Integer.parseInt(deviceTemperature, 16), 10, 3));
        gp += 2 * 2;

        // 闸位:0mm
        String gatePosition = gateData.substring(gp, gp + 2 * 2);
        gateDataMap.put("gatePosition", String.valueOf(Integer.parseInt(gatePosition, 16)));
        gp += 2 * 2;

        // 荷重:0.0kN
        String weight = gateData.substring(gp, gp + 2 * 2);
        gateDataMap.put("weight", RunTuSubUtil.divideAndRound(Integer.parseInt(weight, 16), 10, 3));
        gp += 2 * 2;

        // 报警状态:无
        String alarmState = gateData.substring(gp, gp + 2 * 2);
        gateDataMap.put("alarmState", String.valueOf(Integer.parseInt(alarmState, 16)));
        gp += 2 * 2;

        // 限位状态:下限
        String limitState = gateData.substring(gp, gp + 2 * 1);
        gateDataMap.put("limitState", String.valueOf(Integer.parseInt(limitState, 16)));
        gp += 2 * 1;

        // 控制命令:无
        String controlCmd = gateData.substring(gp, gp + 2 * 1);
        gateDataMap.put("controlCmd", String.valueOf(Integer.parseInt(controlCmd, 16)));
        gp += 2 * 1;

        // 闸门状态:急停
        String gateState = gateData.substring(gp, gp + 2 * 1);
        gateDataMap.put("gateState", String.valueOf(Integer.parseInt(gateState, 16)));
        gp += 2 * 1;

        // 运行状态:急停
        String runState = gateData.substring(gp, gp + 2 * 1);
        gateDataMap.put("runState", String.valueOf(Integer.parseInt(runState, 16)));
        gp += 2 * 1;

        // 闸门最大开度:900mm
        String gateMax = gateData.substring(gp, gp + 2 * 2);
        gateDataMap.put("gateMax", String.valueOf(Integer.parseInt(gateMax, 16)));
        gp += 2 * 2;

        // 瞬时流量:0.000m³/s
        String momentFlow = gateData.substring(gp, gp + 2 * 4);
        gateDataMap.put("momentFlow", RunTuSubUtil.divideAndRound(Integer.parseInt(momentFlow, 16), 1000, 3));
        gp += 2 * 4;

        // 河道水位:0.000m
        String riverCourseWaterLevel = gateData.substring(gp, gp + 2 * 4);
        gateDataMap.put("riverCourseWaterLevel", RunTuSubUtil.divideAndRound(Integer.parseInt(riverCourseWaterLevel, 16), 1000, 3));
        gp += 2 * 4;

        // 闸后水位:0.007m
        String gateAfterWaterLevel = gateData.substring(gp, gp + 2 * 4);
        gateDataMap.put("gateAfterWaterLevel", RunTuSubUtil.divideAndRound(Integer.parseInt(gateAfterWaterLevel, 16), 1000, 3));
        gp += 2 * 4;

        // 闸前水位:0.009m
        String gateFrontWaterLevel = gateData.substring(gp, gp + 2 * 4);
        gateDataMap.put("gateFrontWaterLevel", RunTuSubUtil.divideAndRound(Integer.parseInt(gateFrontWaterLevel, 16), 1000, 3));
        gp += 2 * 4;

        return gateDataMap;
    }

    /**
     * 闸门控制解码
     * @param msg
     */
    private void controlDecode(String msg){
        // 闸门状态
        String gateStatusHex = msg.substring(p, p + 2 * 1);
        map.put("gateStatus", Integer.parseInt(gateStatusHex, 16));
        p += 2 * 1;

        // 命令状态
        String cmdStatusHex = msg.substring(p, p + 2 * 1);
        map.put("cmdStatus", Integer.parseInt(cmdStatusHex, 16));
        p += 2 * 1;

    }

    /**
     * 链路维持报
     * @param msg
     */
    private void linkDataDecode(String msg){
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        Map<String, String> data = new HashMap<>();
        data.put("funcCode", "2F");
        data.put("no", "0199");
        RunTuEncoderUtil runTuEncoderUtil = new RunTuEncoderUtil();
        runTuEncoderUtil.encoder(data);
    }

}
