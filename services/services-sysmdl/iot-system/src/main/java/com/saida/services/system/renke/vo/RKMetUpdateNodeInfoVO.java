package com.saida.services.system.renke.vo;

import lombok.Data;

/**
 * 仁科气象-更新节点信息
 */
@Data
public class RKMetUpdateNodeInfoVO {

    /**
     * 设备地址
     */
    private String deviceAddr;

    /**
     * 节点编号
     */
    private Integer nodeId;
    /**
     * 1: 模拟量1使能模拟量2使能 ；
     * 2: 模拟量1使能模拟量2禁用 ；
     * 3: 模拟量1禁用模拟量2使能 ；
     * 4: 浮点型设备 ；
     * 5: 开关量型设备；
     * 6: 32位有符号整形 ；
     * 7: 32位无符号整形；
     * 8: 遥调设备
     */
    private Integer nodeType;
    /**
     * 0 常规 ；1 雨量 ；2 蒸发量
     */
    private Integer nodeMold;
    /**
     * 小数位数
     */
    private Integer digits;
    /**
     * 节点是否开启 0：关闭 1：开启
     */
    private Integer enable;
    /**
     * 模拟量2下限
     */
    private Number humLowerLimit;
    /**
     * 模拟量2名称
     */
    private String humName;
    /**
     * 模拟量2偏差
     */
    private Number humOffset;
    /**
     * 模拟量2系数
     */
    private Number humRatio;
    /**
     * 模拟量2单位
     */
    private String humUnit;
    /**
     * 模拟量2上限
     */
    private Number humUpperLimit;
    /**
     * 节点名称
     */
    private String nodeName;
    /**
     * 优先级
     */
    private Integer priority;
    /**
     * 开关量报警类型（0 不报警 1闭合报警 2断开报警）
     */
    private Integer switchAlarmType;
    /**
     * 开关量断开显示内容
     */
    private String switchOffContent;
    /**
     * 开关量闭合显示内容
     */
    private String switchOnContent;
    /**
     * 模拟量1下限
     */
    private Number temLowerLimit;
    /**
     * 模拟量1名称
     */
    private String temName;
    /**
     * 模拟量1偏差
     */
    private Number temOffset;
    /**
     * 模拟量1系数
     */
    private Number temRatio;
    /**
     * 模拟量1单位
     */
    private String temUnit;
    /**
     * 模拟量1上限
     */
    private Number temUpperLimit;

}
