package com.saida.services.system.mqtt;

import cn.hutool.cron.CronUtil;
import com.alibaba.fastjson.JSONObject;
import com.saida.services.common.tools.IdGenerateUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.MqttClient;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Data
@Slf4j
@Configuration
@ConfigurationProperties(prefix = "mqtt")
@ConditionalOnProperty(prefix = "mqtt", name = "enable", havingValue = "true")
public class MqttConfig {

    private String broker;

    private String subTopic;

    private String subTopicDeviceToPlatform;

    private String username;

    private String password;

    private String enable;

    @Autowired
    private OnMessageCallback callback;

    @Bean
    public MqttClient mqttClient() {
        MemoryPersistence persistence = new MemoryPersistence();
        MqttClient client = null;

        try {
            client = new MqttClient(broker, username + "_" + IdGenerateUtil.getUUID(), persistence);

            // MQTT 连接选项
            MqttConnectOptions connOpts = new MqttConnectOptions();
            connOpts.setUserName(username);
            connOpts.setPassword(password.toCharArray());
            // 保留会话
            connOpts.setCleanSession(true);

            // 设置回调
            client.setCallback(callback);

            MqttClient finalClient = client;
            new Thread(() -> {
                if (!"true".equals(enable)) {
                    return;
                }
                // 建立连接
                try {
                    try {
                        Thread.sleep(15000);
                    } catch (InterruptedException e) {
                        log.info(e.getMessage());
                    }
                    log.info("Connecting to broker「正在链接到broker」: " + broker);
                    finalClient.connect(connOpts);
                    log.info("Connected");
                    log.info("subTopic =======================> " + subTopic);
//                    finalClient.subscribe(subTopic);

                    CronUtil.schedule("*/5 * * * * ?", new KeepConnMqttTask(finalClient, subTopic, username));
                    CronUtil.schedule("*/5 * * * * ?", new KeepConnMqttTask(finalClient, subTopicDeviceToPlatform, username));
                    CronUtil.setMatchSecond(true);
                    CronUtil.start();
                } catch (MqttException e) {
                    log.error("mqtt链接异常----->", e);
                }
            }).start();
        } catch (MqttException me) {
            log.info("reason " + me.getReasonCode());
            log.info("msg " + me.getMessage());
            log.info("loc " + me.getLocalizedMessage());
            log.info("cause " + me.getCause());
            log.info("excep " + me);
            log.info(JSONObject.toJSONString(me.getStackTrace()));
        }

        return client;
    }

}
