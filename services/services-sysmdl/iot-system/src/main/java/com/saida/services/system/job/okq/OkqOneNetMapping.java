package com.saida.services.system.job.okq;

import java.util.HashMap;
import java.util.Map;

public class OkqOneNetMapping {
    public static final Map<String, Map<String, String>> mapping = new HashMap<String, Map<String, String>>() {{
//        //气象
//        put(AccessKeyProductEnum.KEY_WEATHER.getProductSn(), new HashMap<String, String>() {{
//            put("e1", "WindSpeed"); //风速
//            put("e2", "Rainfall");  //降雨量
//            put("e3", "Temperature");   //温度
//            put("e5", "Atmosphere");   //大气压
//            put("e7", "WindDirection");   //风向
//            put("e9", "RelativeHumidity");   //相对湿度
//            put("e11", "LightLux");   //光照度
//        }});
//
//        //土壤墒情
//        put(AccessKeyProductEnum.KEY_SOIL.getProductSn(), new HashMap<String, String>() {{
////            put("e2", "soilMoisture"); //土壤含水量
////            put("e3", "soilTemperature");  //土壤温度
////            put("e4", "soilMoistureOne");   //土壤含水量 1
////            put("e5", "soilTemperatureOne");   //土壤温度 1
////            put("e6", "soilMoistureTwo");   //土壤含水量 2
////            put("e7", "soilTemperatureTwo");  //土壤温度 2
////            put("e8", "soilMoistureThree");  //土壤含水量 3
////            put("e9", "soilTemperatureThree");  //土壤温度 3
//
//            put("e1", "soilTemperature");  //土壤温度
//            put("e2", "soilMoisture");  //土壤湿度
//            put("e3", "electricConductivity");  //电导率
//            put("e4", "ph");  //PH值
//            put("e5", "NIon");  //氮离子
//            put("e6", "PIon");  //磷离子
//            put("e7", "KIon");  //钾离子
//            put("e8", "salinity");  //盐分
//            put("e14", "voltage");  //电压
//        }});
//
//        //水质
//        put(AccessKeyProductEnum.KEY_WATER.getProductSn(), new HashMap<String, String>() {{
//            put("e1", "waterPH");   //PH值
//            put("e2", "waterTemperature");   //水温
//            put("e3", "dissolvedOxygen");   //溶解氧
//            put("e4", "conductivity");   //电导率
//            put("e5", "TDS");   //TDS
//            put("e6", "turbidity");   //浊度
//            put("e7", "ammoniaNitrogen");   //氨氮
//            put("e8", "nitrateNitrogen");   //硝氮
//        }});
//
//        //虫情
//        put(AccessKeyProductEnum.KEY_PEST.getProductSn(), new HashMap<String, String>() {{
//            put("image", "picPath");  //图片地址
//        }});
//
//        //多光谱
//        put(AccessKeyProductEnum.KEY_MULTISPECTRAL.getProductSn(), new HashMap<String, String>() {{
//            put("deviceName", "deviceName");  //设备名称
//            put("scene", "scene");  //场景
//            put("plantIndex", "plantIndex");  //植被指数
//            put("plantValue", "averValue");  //指数值
//            put("checkStatus", "checkStatus");  //生长状态
//            put("time", "time");  //检测时间
//            put("picUrls", "picUrls");  //检测图片
//            put("averValue", "averValue");  //平均值
//        }});
//
//        //物联网智能虫情测报灯
//        put(AccessKeyProductEnum.KEY_AI_PEST_LIGHT.getProductSn(), new HashMap<String, String>() {{
//            put("e1", "supplyVoltage");   //电源电压
//            put("e2", "pipelineTemperature");   //管道温度
//            put("e3", "illumination");   //照度
//            put("e4", "operationMode");   //运行模式
//            put("e5", "insectWarehouseNumber");   //虫仓号
//            put("e7", "currentMotor");   //当前电机
//            put("e8", "motorDirection");   //电机方向
//            put("e9", "longitude");   //经度
//            put("e10", "latitude");   //纬度
//            put("e13", "outdoorTemperature");   //室外温度
//            put("e15", "enterLowTemperatureSleepMode");   //进入低温休眠
//            put("e16", "lowTemperatureSleepSwitch");   //低温休眠开关
//            put("e17", "heating");   //加热
//            put("e18", "lureInsects");   //诱虫
//            put("e19", "supplementLight");   //补光
//            put("e20", "screenPowerSupply");   //屏幕电源
//            put("e21", "vibration");   //振动
//            put("e22", "upWarehouse");   //上虫仓
//            put("e23", "downWarehouse");   //下虫仓
//            put("e24", "conveyorBelt");   //传送带
//            put("image", "image");   //原始图片地址
//            put("resultImage", "resultImage");   //识别图片地址
//            put("result", "result");   //识别结果
//            put("pestImageRecognitions", "pestImageRecognitions");   //图片识别结果
//        }});
//
//        //物联网太阳能杀虫灯
//        put(AccessKeyProductEnum.KEY_SUNLIGHT_PEST_LIGHT.getProductSn(), new HashMap<String, String>() {{
//            put("e1", "insectTrapLampVoltage");   //捕虫灯电压
//            put("e2", "insectTrapLampElectricCurrent");   //捕虫灯电流
//            put("e3", "insectTrapLampPower");   //捕虫灯功率
//            put("e4", "storageBatteryVoltage");   //蓄电池电压
//            put("e5", "storageBatteryElectricCurrent");   //蓄电池电流
//            put("e6", "storageBatteryPower");   //蓄电池功率
//            put("e7", "solarPanelsVoltage");   //太阳能板电压
//            put("e8", "solarPanelsElectricCurrent");   //太阳能板电流
//            put("e9", "solarPanelsPower");   //太阳能板功率
//            put("e10", "insecticidalNum");   //杀虫数量
//            put("e11", "longitude");   //经度
//            put("e12", "latitude");   //纬度
//            put("image", "image");   //原始图片地址
//            put("resultImage", "resultImage");   //识别图片地址
//            put("result", "result");   //识别结果
//            put("pestImageRecognitions", "pestImageRecognitions");   //图片识别结果
//        }});
//
//        //农田气象站-云南咖啡
//        put(AccessKeyProductEnum.KEY_WEATHER_YUNNAN_COFFEE.getProductSn(), new HashMap<String, String>() {{
//            put("e1", "windSpeed"); //风速
//            put("e2", "rainfall");  //雨量累计
//            put("e3", "atmosphericTemperature");   //大气温度
//            put("e4", "atmosphericHumidity");   //大气湿度
//            put("e5", "digitalAirPressure");   //数字气压
//            put("e6", "simplifiedTotalRadiation");   //简易总辐射
//            put("e7", "windDirection");   //风向
//            put("e8", "cumulativeRadiation");   //辐射累计
//            put("e9", "lightLux");   //光照度
//        }});
//
//        //智享版土壤墒情监测站-云南咖啡
//        put(AccessKeyProductEnum.KEY_SOIL_YUNNAN_COFFEE.getProductSn(), new HashMap<String, String>() {{
//            put("e1", "soilTemperature");  //土壤温度
//            put("e2", "soilMoisture");  //土壤湿度
//            put("e3", "salinity");  //	盐分
//            put("e4", "NIon");  //氮离子
//            put("e5", "PIon");  //磷离子
//            put("e6", "KIon");  //钾离子
//            put("e7", "ph");  //PH值
//        }});
    }};

}