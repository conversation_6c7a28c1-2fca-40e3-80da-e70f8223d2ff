package com.saida.services.system.sequence.enums;

import lombok.Getter;

public enum SequenceTypeEnum {

    PRODUCT_CMD(1, "product_cmd", "CD", 24 * 3600), // 产品命令标识
    PRODUCT_PROPERTY(2, "product_property", "PR", 24 * 3600), //产品属性标识
    PRODUCT_SN(3, "product_sn", "HGGMR", 24 * 3600), // 产品sn
    DEVICE_SN(4, "device_sn", "DE", 24 * 3600), // 设备sn
    TOPIC_ID(5, "topic_id", "TP", 24 * 3600), // 设备sn
    ;

    @Getter
    int code;
    @Getter
    String key;
    @Getter
    String prefix;
    @Getter
    long lockTime;

    SequenceTypeEnum(int code, String key, String prefix, long lockTime) {
        this.code = code;
        this.key = key;
        this.prefix = prefix;
        this.lockTime = lockTime;
    }

    public static SequenceTypeEnum typeOf(int code) {
        SequenceTypeEnum[] values = values();
        for (SequenceTypeEnum value : values) {
            if (value.getCode() == code) {
                return value;
            }
        }
        return null;
    }
}
