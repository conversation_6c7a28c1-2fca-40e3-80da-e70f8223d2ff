package com.saida.services.system.paAlarm;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.crypto.digest.MD5;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.protobuf.ByteString;
import com.google.protobuf.GeneratedMessageV3;
import com.google.protobuf.InvalidProtocolBufferException;
import com.saida.services.iot.bully.dto.resp.FileResp;
import com.saida.services.iot.bully.dto.resp.FolderResp;
import com.saida.services.iot.entity.DeviceEntity;
import com.saida.services.iot.entity.ProductCategoryEntity;
import com.saida.services.iot.entity.ProductEntity;
import com.saida.services.open.req.bully.IotAddTaskReq;
import com.saida.services.open.req.bully.IotSetVolumeReq;
import com.saida.services.system.device.service.DeviceService;
import com.saida.services.system.event.PushAppEvent;
import com.saida.services.system.event.SaveAndUpdateDataEvent;
import com.saida.services.system.paAlarm.demo.KeyAlarmTimeSettingTest;
import com.saida.services.system.paAlarm.dto.AlarmBackDto;
import com.saida.services.system.paAlarm.pager.demo.utils.Log;
import com.saida.services.system.paAlarm.pager.pa.APIHelper;
import com.saida.services.system.paAlarm.pager.pa.PbExtParamUtils;
import com.saida.services.system.paAlarm.pager.pa.Protocol;
import com.saida.services.system.paAlarm.pager.pa.ProtocolUtils;
import com.saida.services.system.paAlarm.protobuf.InterfacePaging;
import com.saida.services.system.paAlarm.protobuf.Public;
import com.saida.services.system.product.service.ProductCategoryService;
import com.saida.services.system.product.service.ProductService;
import com.saida.services.system.sequence.enums.SequenceTypeEnum;
import com.saida.services.system.sequence.service.SequenceService;
import com.sun.jna.Platform;
import com.xxl.job.core.handler.annotation.VlinkerXxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.saida.services.system.paAlarm.pager.pa.APIHelper.if_pub_getallmediainfo;

@Component
@Slf4j
public class PaApi {


    @VlinkerXxlJob(
            value = "initDeviceBW",
            cron = "0 0/5 * * * ?",
            desc = "北望-设备列表更新"
    )
//    @Scheduled(cron = "0 0/5 * * * ?")   //每5分钟执行一次
    public void execute() {
        if (Platform.isMac()) {
            return;
        }
        if (PaAlarmPushService.getLoginId() == 0) {
            return;
        }
        initDevice();
    }

    @Resource
    private DeviceService deviceService;

    public void initBack(Void unused) {
        initDevice();
    }

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;

    public void alarmBack(AlarmBackDto alarmBackDto) {
        log.info("收到报警信息：" + JSON.toJSONString(alarmBackDto));
        DeviceEntity device = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>().eq(DeviceEntity::getImei, alarmBackDto.getDeviceId()));
        if (device == null) {
            return;
        }
        ProductEntity product = productService.getById(device.getProductId());
        if (product == null) {
            return;
        }
        JSONObject data = new JSONObject();
        data.put("imei", alarmBackDto.getDeviceId());
        data.put("type", 2);    //类型 1-普通消息 2-告警消息
        data.put("alarmType", alarmBackDto.getAlarmType());
        data.put("keyWordId", alarmBackDto.getKeyWordId());
        data.put("rowId", alarmBackDto.getRowId());
        data.put("alarmDate", alarmBackDto.getAlarmDate());
        data.put("deviceId", alarmBackDto.getDeviceId());

        applicationEventPublisher.publishEvent(new SaveAndUpdateDataEvent(this, String.valueOf(alarmBackDto.getDeviceId()), data, product));
        applicationEventPublisher.publishEvent(new PushAppEvent(this, device, data));
    }

    @Resource
    private SequenceService sequenceService;
    @Resource
    private ProductService productService;
    @Resource
    private ProductCategoryService productCategoryService;

    public void initDevice() {
        List<Public.pb_pub_address> address = new ArrayList<>();
        InterfacePaging.pbret_main_getzoneinfo ret1 = APIHelper.if_main_getzoneinfo(0, address);
        if (ret1 == null) {
            return;
        }

        ProductCategoryEntity categoryEntity = productCategoryService.getOne(new LambdaQueryWrapper<ProductCategoryEntity>().last(" limit 1"));

        ProductEntity serviceOne = productService.getOne(new LambdaQueryWrapper<ProductEntity>()
                .eq(ProductEntity::getRegular, 1));
        if (serviceOne == null) {
            serviceOne = new ProductEntity();
            serviceOne.setRegular(1);
            serviceOne.setName("北望防霸凌终端");
            serviceOne.setDecoder("defaultDecoder");
            serviceOne.setEncoder("defaultEncoder");
            serviceOne.setCateId(categoryEntity.getId());
            serviceOne.setSn(sequenceService.generate(SequenceTypeEnum.PRODUCT_SN));
            serviceOne.setProductModel("1.0.0");
            serviceOne.setProtocol("设备SDK");
            serviceOne.setManufacturer("北望");
            serviceOne.setDeviceType("SDK直连");
            serviceOne.setAccessType("设备通讯协议");
            serviceOne.setEnabled("1");
            serviceOne.setCreateTime(LocalDateTime.now());
            serviceOne.setCreateBy(1L);
            serviceOne.setUpdateBy(1L);
            productService.save(serviceOne);
        }

        ProductEntity serviceTwo = productService.getOne(new LambdaQueryWrapper<ProductEntity>()
                .eq(ProductEntity::getRegular, 2));
        if (serviceTwo == null) {
            serviceTwo = new ProductEntity();
            serviceTwo.setRegular(2);
            serviceTwo.setName("北望7寸屏寻呼器");
            serviceTwo.setCateId(categoryEntity.getId());
            serviceTwo.setDecoder("defaultDecoder");
            serviceTwo.setEncoder("defaultEncoder");
            serviceTwo.setSn(sequenceService.generate(SequenceTypeEnum.PRODUCT_SN));
            serviceTwo.setProductModel("1.0.0");
            serviceTwo.setProtocol("设备SDK");
            serviceTwo.setManufacturer("北望");
            serviceTwo.setDeviceType("SDK直连");
            serviceTwo.setAccessType("设备通讯协议");
            serviceTwo.setEnabled("1");
            serviceTwo.setCreateTime(LocalDateTime.now());
            serviceTwo.setCreateBy(1L);
            serviceTwo.setUpdateBy(1L);
            productService.save(serviceTwo);
        }

        ProductEntity serviceThree = productService.getOne(new LambdaQueryWrapper<ProductEntity>()
                .eq(ProductEntity::getRegular, 3));
        if (serviceThree == null) {
            serviceThree = new ProductEntity();
            serviceThree.setRegular(3);
            serviceThree.setName("北望10寸屏寻呼器");
            serviceThree.setCateId(categoryEntity.getId());
            serviceThree.setDecoder("defaultDecoder");
            serviceThree.setEncoder("defaultEncoder");
            serviceThree.setSn(sequenceService.generate(SequenceTypeEnum.PRODUCT_SN));
            serviceThree.setProductModel("1.0.0");
            serviceThree.setProtocol("设备SDK");
            serviceThree.setManufacturer("北望");
            serviceThree.setDeviceType("SDK直连");
            serviceThree.setAccessType("设备通讯协议");
            serviceThree.setEnabled("1");
            serviceThree.setCreateTime(LocalDateTime.now());
            serviceThree.setCreateBy(1L);
            serviceThree.setUpdateBy(1L);
            productService.save(serviceThree);
        }

        List<InterfacePaging.pbret_main_getzoneinfo_single> singleList = ret1.getSingleList();
        ProductEntity finalServiceTwo = serviceTwo;
        ProductEntity finalServiceThree = serviceThree;
        ProductEntity finalServiceOne = serviceOne;
        singleList.forEach(zoneInfo -> {
            int mask = 0xfffc0000;
            int maskId = zoneInfo.getDeviceId() & mask;
            Long productId = null;
            if (maskId == 0x4c0000) {
                productId = finalServiceOne.getId(); // 防欺凌终端 4980736
            } else if (maskId == 0x200000) {
                productId = finalServiceThree.getId(); // 10寸屏寻呼器 2097152
            } else if (maskId == 0x580000) {
                productId = finalServiceTwo.getId(); // 7寸屏寻呼器 5767168
            }
            if (productId != null) {
                DeviceEntity one = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                        .eq(DeviceEntity::getImei, zoneInfo.getDeviceId())
                        .eq(DeviceEntity::getProductId, productId));
                if (one == null) {
                    one = new DeviceEntity();
                    one.setImei(String.valueOf(zoneInfo.getDeviceId()));
                    one.setSn(sequenceService.generate(SequenceTypeEnum.DEVICE_SN));
                    one.setProductId(productId);
                    one.setName(zoneInfo.getZonename().toStringUtf8());
                    one.setCreateTime(LocalDateTime.now());
                    one.setStatus(1);
                    one.setCreateBy(1L);
                    one.setUpdateBy(1L);
                    deviceService.save(one);
                } else {
                    one.setStatus(zoneInfo.getIsOnline());
                    deviceService.edit(one);
                }
            }
        });
    }


    public boolean setVolume(IotSetVolumeReq setVolumeReq) {
        InterfacePaging.pbif_set_setDeviceParam.Builder setDeviceParamBuilder = InterfacePaging.pbif_set_setDeviceParam.newBuilder();

        setDeviceParamBuilder.setDeviceId(setVolumeReq.getImei());
        InterfacePaging.pbif_deviceParam_volume.Builder volumeBuilder = InterfacePaging.pbif_deviceParam_volume.newBuilder();
        volumeBuilder.setPlay(setVolumeReq.getPlay());
        volumeBuilder.setCall(setVolumeReq.getCall());
        volumeBuilder.setAlarm(setVolumeReq.getAlarm());
        volumeBuilder.setStreamOut(setVolumeReq.getStreamOut());
        volumeBuilder.setStreamIn(setVolumeReq.getStreamIn());
        volumeBuilder.setTalkOut(setVolumeReq.getTalkOut());
        volumeBuilder.setTalkIn(setVolumeReq.getTalkIn());
        setDeviceParamBuilder.setMaxVol(volumeBuilder);
        try {
            Public.pbret_common ret1 = ProtocolUtils.call(setDeviceParamBuilder.build());
            return (ret1 != null ? ret1.getErr() : 0) == 0;
        } catch (InvalidProtocolBufferException e) {
            log.error("setVolume", e);
        }
        return false;

    }


    public IotSetVolumeReq getVolume(Integer deviceId) {
        try {
            InterfacePaging.pbif_set_getDeviceParam pbif = InterfacePaging.pbif_set_getDeviceParam.newBuilder()
                    .setType(0)
                    .setDeviceId(deviceId)
                    .build();
            InterfacePaging.pbret_set_getDeviceParam ret = ProtocolUtils.call(pbif);
            InterfacePaging.pbif_deviceParam_volume maxVol = ret.getMaxVol();
            IotSetVolumeReq iotSetVolumeReq = new IotSetVolumeReq();
            iotSetVolumeReq.setAlarm(maxVol.getAlarm());
            iotSetVolumeReq.setCall(maxVol.getCall());
            iotSetVolumeReq.setPlay(maxVol.getPlay());
            iotSetVolumeReq.setStreamIn(maxVol.getStreamIn());
            iotSetVolumeReq.setStreamOut(maxVol.getStreamOut());
            iotSetVolumeReq.setTalkIn(maxVol.getTalkIn());
            iotSetVolumeReq.setTalkOut(maxVol.getTalkOut());
            return iotSetVolumeReq;
        } catch (InvalidProtocolBufferException e) {
            log.error("getVolume", e);
        }
        return null;

    }


    /**
     * 获取文件夹列表。
     * 这个方法主要用于获取包括私有库在内的所有文件夹列表。首先，它创建一个表示私有库的FolderResp对象，
     * 然后通过调用API获取所有文件类型的列表，并将这些文件夹信息添加到列表中返回。
     *
     * @return List<FolderResp> 返回包含所有文件夹信息的列表，第一个元素是私有库。
     */
    public List<FolderResp> getFolderList() {
        // 创建一个FolderResp对象表示私有库，并设置其ID和名称
        FolderResp kind = new FolderResp();
        kind.setFolderId(0);
        kind.setFolderName("私有库");

        // 调用API获取所有文件类型的列表
        Public.pbret_pub_filetype ret = APIHelper.getAllFileType();

        // 创建一个列表用于存储所有文件夹信息
        List<FolderResp> list = new ArrayList<>();
        // 将私有库添加到列表中
        list.add(kind);

        // 遍历API返回的文件类型列表，为每个文件类型创建一个FolderResp对象，并添加到列表中
        for (Public.pb_pub_filetype_single s : ret.getSingleList()) {
            int Id = s.getAttribId(); // 目录ID
            String attribName = s.getAttribName().toStringUtf8(); // 目录名称
            FolderResp resp = new FolderResp();
            resp.setFolderId(Id);
            resp.setFolderName(attribName);
            list.add(resp);
        }
        // 返回包含所有文件夹信息的列表
        return list;
    }


    /**
     * 创建一个新的文件夹。
     *
     * @param attribName 新文件夹的属性名称。
     * @return 如果文件夹创建成功，则返回true；否则返回false。
     */
    public boolean createFolder(String attribName) {
        // 调用APIHelper获取所有文件类型的列表。
        Public.pbret_pub_filetype ret = APIHelper.getAllFileType();

        // 从列表中找出属性ID最大的文件类型，用于确定新文件夹的属性ID。
        Public.pb_pub_filetype_single pbPubFiletypeSingle = ret.getSingleList().stream()
                .max(Comparator.comparing(Public.pb_pub_filetype_single::getAttribId))
                .orElseGet(() -> Public.pb_pub_filetype_single.newBuilder().setAttribId(0).build());

        // 计算新文件夹的属性ID，即最大属性ID加一。
        int attribId = pbPubFiletypeSingle.getAttribId() + 1;

        // 构建新文件夹的属性信息。
        // 创建目录
        Public.pb_pub_filetype_single fileType = Public.pb_pub_filetype_single.newBuilder()
                .setTypeId(0)
                .setAttribId(attribId)
                .setAttribName(ByteString.copyFromUtf8(attribName)).build();

        // 构建设置新文件类型的请求。
        Public.pbif_pub_setfiletype add_pbif = Public.pbif_pub_setfiletype.newBuilder()
                .setSingle(fileType).build();

        try {
            // 调用协议工具类，发送设置新文件类型的请求。
            Public.pbret_common ret1 = ProtocolUtils.call(add_pbif);
            return (ret1 != null ? ret1.getErr() : 0) == 0;
        } catch (InvalidProtocolBufferException e) {
            log.error("创建目录失败", e); // 记录创建文件夹失败的日志。
        }

        return false; // 文件夹创建失败。
    }


    /**
     * 重命名文件夹。
     *
     * @param attribId   文件类型ID，用于标识要重命名的文件夹。
     * @param attribName 新的文件夹名称，以字符串形式提供。
     * @return 如果重命名操作成功，则返回true；如果失败，则返回false。
     * <p>
     * 此方法通过构建一个包含新文件夹名称的请求消息，并尝试调用协议缓冲区（Protocol Buffer）的远程服务调用来执行重命名操作。
     * 如果重命名操作因异常而失败，则会记录错误信息并返回false。
     */
    public boolean renameFolder(Integer attribId, String attribName) {
        // 构建文件类型对象，设置类型ID、属性ID和属性名称。
        Public.pb_pub_filetype_single fileType = Public.pb_pub_filetype_single.newBuilder()
                .setTypeId(0)
                .setAttribId(attribId)
                .setAttribName(ByteString.copyFromUtf8(attribName)).build();

        // 构建设置文件类型的请求消息，包含上述构建的文件类型对象。
        Public.pbif_pub_setfiletype add_pbif = Public.pbif_pub_setfiletype.newBuilder()
                .setSingle(fileType).build();

        try {
            // 调用远程服务，发送设置文件类型的请求。
            Public.pbret_common ret = ProtocolUtils.call(add_pbif);
            return (ret != null ? ret.getErr() : 0) == 0;
        } catch (InvalidProtocolBufferException e) {
            // 如果出现协议缓冲区异常，则记录错误信息。
            log.error("renameFolder失败", e);
        }
        return false; // 如果调用失败，则返回false。
    }


    /**
     * 删除指定属性ID的文件夹。
     *
     * @param attribId 文件夹的属性ID，用于唯一标识文件夹。
     * @return 如果删除成功，则返回true；如果删除过程中出现异常，则返回false。
     */
    public boolean delFolder(Integer attribId) {

        // 构建删除文件类型的请求对象，设置属性ID。
        Public.pb_pub_filetype_single delFileType = Public.pb_pub_filetype_single.newBuilder()
                .setAttribId(attribId).build();

        // 构建删除请求，将上述文件类型请求对象添加到列表中。
        Public.pbif_pub_delfiletype del_pbif = Public.pbif_pub_delfiletype.newBuilder().addSingle(delFileType).build();

        try {
            // 调用协议工具类的方法，发送删除请求。
            // 如果删除成功，方法执行完毕，返回true。
            Public.pbret_common ret = ProtocolUtils.call(del_pbif);
            return (ret != null ? ret.getErr() : 0) == 0;
        } catch (InvalidProtocolBufferException e) {
            // 如果删除过程中出现InvalidProtocolBufferException异常，记录错误日志。
            log.error("创建目录失败", e);
        }

        // 出现异常时，返回false，表示删除操作失败。
        return false;
    }

    public static void main(String[] args) {
        String url = "/Users/<USER>/Downloads/迷人的危险.mp3";
        File tempFile = new File(url);
        String s = MD5.create().digestHex16(tempFile);
        System.out.println(s);

    }

    @Value("${sysconfig.uploadPath:#{null}}")
    private String fileBasePath;

    /**
     * 上传文件到服务器。
     *
     * @param fileUrl  文件的URL地址，用于下载文件。
     * @param attribId 文件的属性ID，用于标识文件的归属或类型。0表示私有文件，非0表示公共文件。
     * @param fileName 文件的名称，上传时使用的文件名。
     * @return 返回上传结果，true表示上传成功，false表示上传失败。
     */
    public boolean uploadFile(String fileUrl, Integer attribId, String fileName) {
        String suffix = FileUtil.getSuffix(fileUrl);
        // 创建一个临时文件，用于存储下载的文件内容。
        File tempFile = FileUtil.createTempFile("paApiFile", "." + suffix, new File(fileBasePath), true);
        // 从文件URL下载文件到临时文件。
        HttpUtil.downloadFile(fileUrl, tempFile);
        log.info("下载文件成功 tempFile.length:{},md5:{}", tempFile.length(), MD5.create().digestHex16(tempFile));
        // 根据attribId的值确定文件的类型，0表示公共文件，非0表示私有文件。
        //attribId 为0说明是私有库
        int kind = attribId == 0 ? 1 : 0;

        // 调用API接口，上传文件到服务器。传入文件路径、文件类型、文件属性ID、空字符串作为文件描述、文件名、登录ID和文件类型（公共或私有）。
        long l = System.currentTimeMillis();
        Public.pbret_common common = APIHelper.pbif_pub_uploadfile(tempFile.getPath(), Protocol.FILE_FORMAL,
                attribId, "", fileName, PaAlarmPushService.getLoginId(), kind);
        log.info("上传文件耗时:{} , common:{}", System.currentTimeMillis() - l, JSON.toJSONString(common));
        // 判断上传结果，如果common不为空且错误码为0，则上传成功；否则，上传失败。
        boolean b = common != null && common.getErr() == 0;
        FileUtil.del(tempFile);
        return b;
    }

    /**
     * 删除文件的方法。
     * 根据属性ID和媒体ID删除相应的文件。如果属性ID为0，则kind字段设置为1，否则设置为0。
     * 通过构建一个pbif_pub_delmedia消息并调用ProtocolUtils.call方法来执行删除操作。
     * 如果删除操作成功执行，则返回true；如果出现InvalidProtocolBufferException异常，则记录错误日志并返回false。
     *
     * @param attribId 属性ID，用于决定删除操作的类型。
     * @param mediaId  媒体ID，指定要删除的文件。
     * @return 如果文件删除成功，则返回true；否则返回false。
     */
    public boolean delFile(Integer attribId, Integer mediaId) {
        // 根据attribId的值决定kind的取值，用于区分不同的删除类型。
        int kind = attribId == 0 ? 1 : 0;
        // 构建删除媒体文件的消息对象，设置媒体ID和类型。
        Public.pbif_pub_delmedia build = Public.pbif_pub_delmedia.newBuilder().addMediaId(mediaId).setKind(kind).build();
        try {
            Public.pbret_common ret = ProtocolUtils.call(build);
            return (ret != null ? ret.getErr() : 0) == 0;
        } catch (InvalidProtocolBufferException e) {
            // 如果出现异常，记录错误日志，并返回false。
            log.error("删除文件失败", e);
        }
        // 如果执行到此处，说明删除操作失败，返回false。
        return false;
    }

    /**
     * 根据属性ID获取文件列表。
     *
     * @param attribId 属性ID，用于筛选文件类型。
     * @return 返回符合条件的文件响应列表。
     */
    public List<FileResp> getFileList(Integer attribId) {
        // 根据attribId的值决定获取哪种类型的文件信息，0表示获取类型2，非0表示获取类型3
        int kind = attribId == 0 ? 3 : 2;
        // 构建请求消息，设置属性ID、不需要获取推荐和更新信息，以及获取的文件类型
        Public.pbif_pub_getallmediainfo pbif = Public.pbif_pub_getallmediainfo.newBuilder()
                .setAttrib(attribId)
                .setNeedGetRec(0)
                .setNeedGetUpdate(0)
                .setGetKind(kind)
                .build();
        // 初始化文件响应列表
        List<FileResp> fileRespList = new ArrayList<>();
        try {
            // 调用远程服务获取文件信息
            Public.pbret_pub_getmediainfo info = ProtocolUtils.call(pbif);
            // 如果获取信息为空，则直接返回空列表
            if (info == null) {
                return fileRespList;
            }
            // 遍历每个文件信息，构建FileResp对象，并添加到列表中
            for (Public.pb_pub_getmediainfo_single single : info.getSingleList()) {
                FileResp resp = new FileResp();
                // 设置文件的媒体ID、长度、时长和文件名
                resp.setMediaId(single.getMediaid());
                resp.setLen(single.getLen());
                resp.setMsec(single.getMsec());
                resp.setFileName(single.getFilename().toStringUtf8());
                fileRespList.add(resp);
            }
        } catch (InvalidProtocolBufferException e) {
            // 日志记录获取文件列表失败的异常信息
            log.error("获取文件列表失败", e);
        }
        // 返回文件响应列表
        return fileRespList;
    }


    /**
     * 更新媒体文件信息。
     * <p>
     * 通过调用底层库函数，更新指定媒体ID的文件名和属性。
     * 主要用于媒体管理模块中对已上传媒体文件的元数据进行修改。
     *
     * @param mediaId  媒体文件的唯一标识ID。
     * @param attrib   媒体文件的属性，用于设置或修改文件的某些特性。
     * @param fileName 新的文件名，用于更新媒体文件的名称。
     * @return 更新操作是否成功的布尔值。成功返回true，失败返回false。
     */
    public boolean updateFile(Integer mediaId, Integer attrib, String fileName) {
        // 创建更新媒体信息请求的Builder对象
        Public.pb_pub_updatemediainfo.Builder builder = Public.pb_pub_updatemediainfo.newBuilder();

        // 构建更新媒体信息的请求消息，包括媒体ID、文件名和属性
        Public.pb_pub_updatemediainfo pbif = builder.addSingle(Public.pb_pub_getmediainfo_single.newBuilder()
                .setMediaid(mediaId)
                .setFilename(ByteString.copyFromUtf8(fileName))
                .setAttrib(attrib)
                .build()).build();

        // 调用底层库函数，发送更新媒体信息的请求，并获取响应数据
        byte[] ret_byte = ProtocolUtils.pacLibrary.call(61009, pbif.toByteArray());

        try {
            // 解析响应数据，获取操作结果
            Public.pbret_common ret = Public.pbret_common.parseFrom(ret_byte);
            // 判断操作是否成功，成功则返回true，否则返回false
            return ret.getErr() == 0;
        } catch (InvalidProtocolBufferException e) {
            // 记录解析响应数据失败的日志
            log.error("重命名文件失败", e);
        }
        // 如果出现异常或操作失败，返回false
        return false;
    }


    public boolean setKeyAlarm(Integer deviceId, Integer[] alarmKeyIdList) {
        InterfacePaging.pbif_set_setDeviceParam.Builder pbif = InterfacePaging.pbif_set_setDeviceParam.newBuilder();
        pbif.setDeviceId(deviceId);

        Public.pb_extparam_packet.Builder packet_builder = Public.pb_extparam_packet.newBuilder();
        long key = 0b00000000000000;  // //每一个位表示对应的关键词是否启用： 1 启用 0 不启用
        for (Integer index : alarmKeyIdList) {
            int bitIndex = index - 1;
            // 设置对应位为1，启用关键词
            key |= (1L << bitIndex);
        }
        byte[] keys1 = PbExtParamUtils.longTo64bit(key);
        packet_builder.addExt(KeyAlarmTimeSettingTest.setAlarmKeyIdList(keys1, null));
        pbif.setExtparam(packet_builder);
        try {
            Public.pbret_common ret = ProtocolUtils.call(pbif.build());
            InterfacePaging.pbif_set_operDeviceParam operDeviceParam = null;
            operDeviceParam = InterfacePaging.pbif_set_operDeviceParam.newBuilder()
                    .setDeviceId(deviceId)
                    .setOper(0)
                    .build();
            Public.pbret_common ret2 = ProtocolUtils.call(operDeviceParam);
            return (ret != null ? ret.getErr() : 0) == 0 && (ret2 != null ? ret2.getErr() : 0) == 0;
        } catch (InvalidProtocolBufferException e) {
            log.error("设置设备报警按键失败", e);
            return false;
        }
    }

    public Integer[] getKeyAlarm(Integer deviceId) {
        InterfacePaging.pbif_set_getDeviceParam pbif = InterfacePaging.pbif_set_getDeviceParam.newBuilder()
                .setType(0)
                .setDeviceId(deviceId)
                .build();
        try {
            InterfacePaging.pbret_set_getDeviceParam ret = ProtocolUtils.call(pbif);
            if (ret != null) {
                Public.pb_extparam_packet extparam = ret.getExtparam();
                List<Public.pb_extparam> extList = extparam.getExtList();
                for (Public.pb_extparam pb_extparam : extList) {
                    if (pb_extparam.getId() == 640) {
                        List<Public.pb_extparam> extList1 = pb_extparam.getExtList();
                        if (!extList1.isEmpty()) {
                            byte[] byteArray = extList1.get(0).getData().toByteArray();
                            long l = PbExtParamUtils.byteToNumber(byteArray);
                            String binaryString = Long.toBinaryString(l);
                            Integer[] alarmKeyIdList = new Integer[binaryString.length()];
                            for (int i = 0; i < binaryString.length(); i++) {
                                char c = binaryString.charAt(i);
                                if (c == '1') {
                                    alarmKeyIdList[i] = 1;
                                } else {
                                    alarmKeyIdList[i] = 0;
                                }
                            }
                            return alarmKeyIdList;
                        }
                    }
                }
            }
        } catch (InvalidProtocolBufferException e) {
            log.error("获取设备报警按键失败", e);
        }
        Integer[] alarmKeyIdList = new Integer[14];
        for (int i = 0; i < alarmKeyIdList.length; i++) {
            alarmKeyIdList[0] = 0;
        }
        return alarmKeyIdList;
    }


    public Integer getAlarmTerminalMedia(Integer deviceId, Integer rowId, String path) {
        Public.pbif_pub_readlog.Builder readLogBuilder = Public.pbif_pub_readlog.newBuilder();
        readLogBuilder.setRowid(rowId);
        try {
            GeneratedMessageV3 ret = ProtocolUtils.call(readLogBuilder.build());
            Public.pbret_pub_readlog readlog = (Public.pbret_pub_readlog) ret;
            Public.pbret_pub_readlog_single log_single = readlog.getSingle(0);
            if ((log_single.getDeviceId() & 0xfffc0000) != 0x4c0000) {
                // 非关键词报警终端
                return null;
            }
            if (log_single.getType() != 4) {
                // 非报警日志
                return null;
            }
            Date start = new Date();
            start.setYear(log_single.getYear() - 1900);
            start.setMonth(log_single.getMonth() - 1);
            start.setDate(log_single.getDay());
            start.setHours(log_single.getHour());
            start.setMinutes(log_single.getMinute());
            start.setSeconds(log_single.getSec());
            long start_time = start.getTime();
            long end_time = start.getTime();

            Public.pbret_pub_getmediainfo allMediaInfo = if_pub_getallmediainfo(
                    0, 1, 0, 0, log_single.getDeviceId(), start_time / 1000 - 20 * 60, end_time / 1000 + 1);
            if (allMediaInfo == null || allMediaInfo.getSingleCount() == 0) {
                return null;
            }

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date alarmLogDate = null;
            try {
                alarmLogDate = sdf.parse(getLogTime(log_single));
            } catch (ParseException e) {
                e.printStackTrace();
            }
            if (alarmLogDate == null) {
                return null;
            }

            int index = -1;
            for (int i = allMediaInfo.getSingleList().size() - 1; i >= 0; i--) {
                Public.pb_pub_getmediainfo_single media = allMediaInfo.getSingle(i);
                Matcher m = Pattern.compile("[0-9]{12}").matcher(media.getFilename().toStringUtf8());
                if (m.find()) {
                    try {
                        sdf = new SimpleDateFormat("yyyyMMddHHmmss");
                        Date fileDate = sdf.parse("20" + m.group(0));
                        if (fileDate == null) {
                            continue;
                        }

                        if (fileDate.getTime() + media.getMsec() > alarmLogDate.getTime()) {
                            if (fileDate.getTime() <= alarmLogDate.getTime()) {
                                index = i;
                            }
                        }
                    } catch (ParseException e) {
                        e.printStackTrace();
                    }
                }
            }
            if (index == -1) {
                return null;
            }
            Public.pb_pub_getmediainfo_single mediaInfo = allMediaInfo.getSingle(index);
            // 报警录音文件 ID
            int mediaId = mediaInfo.getMediaid();
            // 本地存放目录, 绝对路径
            Log.i("mediaId", String.valueOf(mediaId));
            Log.i("getFilename", mediaInfo.getFilename().toStringUtf8());
            Public.pbif_pub_dowloadfile.Builder downloadFileBuilder = Public.pbif_pub_dowloadfile.newBuilder();
            downloadFileBuilder.setFileId(mediaId);
            downloadFileBuilder.setLocalpath(ByteString.copyFromUtf8(path));
            Public.pbret_common downloadFileRet = ProtocolUtils.call(downloadFileBuilder.build());
            if (downloadFileRet != null) {
                Log.i("downloadFileRet", String.valueOf(downloadFileRet.getErr()));
                if (downloadFileRet.getErr() == 0) {
                    // 下载成功
                    return mediaId;
                }
            }
        } catch (InvalidProtocolBufferException e) {
            e.printStackTrace();
        }
        return null;
    }


    public static String getLogTime(Public.pbret_pub_readlog_single log_single) {
        if (log_single == null) {
            return "";
        }

        return log_single.getYear()
                + "-" + formantTime(log_single.getMonth())
                + "-" + formantTime(log_single.getDay())
                + " " + formantTime(log_single.getHour())
                + ":" + formantTime(log_single.getMinute())
                + ":" + formantTime(log_single.getSec());
    }

    public static String formantTime(int t) {
        return t < 10 ? "0" + t : String.valueOf(t);
    }

    public Integer setTimerPlan(IotAddTaskReq iotAddTaskReq) {
        try {
            Integer planId = null;
            InterfacePaging.pbif_set_getTimerPlan.Builder getTimerPlanBuilder = InterfacePaging.pbif_set_getTimerPlan.newBuilder();
            getTimerPlanBuilder.setUserId(PaAlarmPushService.getLoginId());
            InterfacePaging.pbret_set_timerPlan ret = ProtocolUtils.call(getTimerPlanBuilder.build());
            if (ret == null) {
                return null;
            }
            List<InterfacePaging.pbret_set_timerPlan_single> singleList = ret.getSingleList();
            for (InterfacePaging.pbret_set_timerPlan_single pbretSetTimerPlanSingle : singleList) {
                if (pbretSetTimerPlanSingle.getPlanId() == 1) {
                    planId = pbretSetTimerPlanSingle.getPlanId();
                }
            }
            if (planId == null) {
                //创建一个默认的
                InterfacePaging.pbif_set_setTimerPlan.Builder setTimerPlanBuilder = InterfacePaging.pbif_set_setTimerPlan.newBuilder();
                setTimerPlanBuilder.setUserId(PaAlarmPushService.getLoginId());
//                setTimerPlanBuilder.setPlanId(1);
                setTimerPlanBuilder.setName(ByteString.copyFromUtf8("V-LINKER默认计划"));
                Public.pbret_common call = ProtocolUtils.call(setTimerPlanBuilder.build());
                if (call != null && call.getErr() == 0) {
                    planId = 1;
                }
            }
            if (planId == null) {
                Log.e("setTimerPlan", "创建默认计划失败");
                return null;
            }
            InterfacePaging.pbif_set_setTimer.Builder setTimerBuilder = InterfacePaging.pbif_set_setTimer.newBuilder();
            InterfacePaging.pbif_timer_single.Builder timerBuilder = InterfacePaging.pbif_timer_single.newBuilder();
            timerBuilder.setName(ByteString.copyFromUtf8(iotAddTaskReq.getTaskName()));
            timerBuilder.setType(1);
            timerBuilder.setVolume(100);
            timerBuilder.setPlanId(planId);
            timerBuilder.setTimerId(iotAddTaskReq.getTimerId());
            for (Integer deviceId : iotAddTaskReq.getImeiIds()) {
                Public.pb_pub_address.Builder builderForValue = Public.pb_pub_address.newBuilder();
                builderForValue.setId(deviceId);
                timerBuilder.addAddress(builderForValue);
            }
            for (Integer week : iotAddTaskReq.getWeeks()) {
                timerBuilder.addWeek(week);
            }
            timerBuilder.setHour(iotAddTaskReq.getHour());
            timerBuilder.setMinute(iotAddTaskReq.getMinute());
            timerBuilder.setSec(iotAddTaskReq.getSec());
            timerBuilder.setOnoff(iotAddTaskReq.getStatus());
            timerBuilder.setContinued(iotAddTaskReq.getContinued());
            for (Integer mediaId : iotAddTaskReq.getMediaIds()) {
                timerBuilder.addMediaId(mediaId);
            }
            InterfacePaging.pbif_timer_datelimit.Builder builder = InterfacePaging.pbif_timer_datelimit.newBuilder();
            builder.setBeginyear(DateUtil.year(iotAddTaskReq.getStartTime()));
            builder.setBeginmonth(DateUtil.month(iotAddTaskReq.getStartTime()) + 1);
            builder.setBeginday(DateUtil.dayOfMonth(iotAddTaskReq.getStartTime()));
            builder.setEndyear(DateUtil.year(iotAddTaskReq.getEndTime()));
            builder.setEndmonth(DateUtil.month(iotAddTaskReq.getEndTime()) + 1);
            builder.setEndday(DateUtil.dayOfMonth(iotAddTaskReq.getEndTime()));
            timerBuilder.addWhite(builder.build());

            InterfacePaging.pbif_set_setTimer pbif = setTimerBuilder.setTimer(timerBuilder.build()).build();
            Public.pbret_common setTimerRet = ProtocolUtils.call(pbif);
            if (setTimerRet == null || setTimerRet.getErr() != 0) {
                Log.e("setTimerPlan", "创建默认计划失败");
                return null;
            }
            return setTimerRet.getVal1();
        } catch (InvalidProtocolBufferException e) {
            log.error("setTimerPlan", e);
        }
        return null;
    }


    public boolean delTimerPlan(IotAddTaskReq iotAddTaskReq) {
        InterfacePaging.pbif_set_delTimer.Builder delTimerBuilder = InterfacePaging.pbif_set_delTimer.newBuilder();
        delTimerBuilder.addTimerId(iotAddTaskReq.getTimerId());
        try {
            Public.pbret_common setTimerRet = ProtocolUtils.call(delTimerBuilder.build());
            if (setTimerRet != null && setTimerRet.getErr() == 0) {
                return true;
            }
        } catch (InvalidProtocolBufferException e) {
            log.error("delTimerPlan", e);
        }
        return false;
    }

}
