package com.saida.services.system.renke.util;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.saida.services.system.renke.vo.RKQueryVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/5/15 15:08
 */
@Component
public class SeedlingConditionUtil {

    @Autowired
    private UserUtil userUtil;

    private static String  URL = "http://api.farm.0531yun.cn";


    /***
     * 仁科苗情抓拍记录
     * @return
     */
    public  String getDevicePhotographList(RKQueryVO RKQueryVO) {
        String token = userUtil.getToken();
        String url = URL+"/api/v2.0/entrance/device/getDevicePhotographList";
        HttpResponse execute = HttpRequest.get(url)
                .form(ParamUtil.getParam(RKQueryVO))
                .header("token",token).execute();
        JSONObject returnObject = JSON.parseObject(execute.body());
        if (returnObject == null || !returnObject.getString("code").equals("1000")){
            return null;
        }
        JSONObject data = JSON.parseObject(returnObject.getString("data"));
        return data.toString();
    }


    /***
     * 仁科苗情手动抓拍
     * @return
     */
    public  String photograph(RKQueryVO RKQueryVO) {
        String token = userUtil.getToken();
        String url = URL+"/api/v2.0/entrance/device/photograph";
        HttpResponse execute = HttpRequest.get(url)
                .form(ParamUtil.getParam(RKQueryVO))
                .header("token",token).execute();
        JSONObject returnObject = JSON.parseObject(execute.body());
        if (returnObject == null || !returnObject.getString("code").equals("1000")){
            return null;
        }
        return returnObject.toString();
    }


}
