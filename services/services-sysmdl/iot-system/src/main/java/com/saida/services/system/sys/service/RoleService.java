package com.saida.services.system.sys.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.saida.services.system.sys.entity.RoleEntity;

import java.util.List;

/**
 * 角色
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-09-26 17:09:30
 */
public interface RoleService extends IService<RoleEntity> {

    void addOrUpdate(RoleEntity entity);

    IPage<RoleEntity> listPage(RoleEntity entity);

    RoleEntity getInfo(Long id);

    void delete(Long id);

    List<RoleEntity> getList(RoleEntity entity);
}

