package com.saida.services.system.paAlarm.pager.test;

import com.google.protobuf.InvalidProtocolBufferException;
import com.saida.services.system.paAlarm.pager.demo.utils.Log;
import com.saida.services.system.paAlarm.pager.pa.Protocol;
import com.saida.services.system.paAlarm.pager.pa.ProtocolUtils;
import com.saida.services.system.paAlarm.protobuf.InterfacePaging.*;
import com.saida.services.system.paAlarm.protobuf.Public;
import com.saida.services.system.paAlarm.protobuf.Public.*;


public class PAGetVerTest {

    private static final String TAG = PAGetVerTest.class.getName();

    public static void test() {

        try {
//            byte[] ret = ProtocolUtils.pacLibrary.call(Protocol.IF_CALL_PUB_GETVERSION, new byte[0]);
            byte[] ret = ProtocolUtils.pacLibrary.call(61014, new byte[0]);
            pbret_pub_getver getVer = pbret_pub_getver.parseFrom(ret);
            Log.w(TAG, "LibPaging库-编译时间: " + getVer.getBuildtime().toStringUtf8());

        } catch (InvalidProtocolBufferException e) {
            e.printStackTrace();
        }


    }
}
