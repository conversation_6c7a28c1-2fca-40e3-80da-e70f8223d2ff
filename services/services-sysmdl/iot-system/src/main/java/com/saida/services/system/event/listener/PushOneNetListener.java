package com.saida.services.system.event.listener;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.saida.services.common.tools.RedisUtil;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.iot.entity.DeviceEntity;
import com.saida.services.iot.entity.ProductEntity;
import com.saida.services.system.aspects.MsgOperation;
import com.saida.services.system.event.PushOneNetEvent;
import com.saida.services.system.event.listener.oneNet.SendToOneNetRequest;
import com.saida.services.system.event.listener.oneNet.enums.AccessKeyProductEnum;
import com.saida.services.system.product.service.ProductService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Objects;

@Slf4j
@Component
public class PushOneNetListener {

    @Resource
    private ProductService productService;

    @Resource
    private SendToOneNetRequest sendToOneNetRequest;

    /**
     * 处理推送OneNet事件的监听器方法。
     * 当发生需要向OneNet推送数据的事件时，此方法被调用。
     *
     * @param event 包含设备信息和要推送的数据的事件对象。
     */
    @MsgOperation
    @EventListener(classes = {PushOneNetEvent.class})
    public void pushOneNetEvent(PushOneNetEvent event) {
        DeviceEntity device = event.getDevice();
        if (Objects.isNull(device) || StringUtil.isEmpty(device.getAppId())) {
            log.info("当前设备不存在 或者没有appid ,deviceId:{}", device.getId());
            return;
        }
        log.info("设备{}:准备推送数据", device.getImei());
        String data = event.getData().toString();
        if (StringUtil.isEmpty(data)) {
            log.info("PushHttpEvent =========== 数据为空");
        }

        ProductEntity product = productService.findById(device.getProductId());
        if (Objects.isNull(product)) {
            log.info("当前产品不存在,productId:{}", device.getProductId());
            return;
        }
        AccessKeyProductEnum accessKeyProductEnum = AccessKeyProductEnum.getByProductSn(product.getSn());
        if (null == accessKeyProductEnum) {
            log.info("当前产品无对应oneNet,productId:{}", JSON.toJSON(product));
            return;
        }

        JSONObject oneNet = new JSONObject();
        oneNet.put("id", generateMsgId());
        oneNet.put("version", "1.0");
        JSONObject params = new JSONObject();
        event.getData().keySet().forEach(key -> {
            if (key.startsWith("_") || "createTime".equals(key) || "imei".equals(key) || "type".equals(key) || "reportTime".equals(key)) {
                return;
            }

            JSONObject val = new JSONObject();
            Object obj = event.getData().get(key);
            if (obj instanceof Collection) {
                val.put("value", obj);
            } else if (obj instanceof JSONObject) {
                val.put("value", obj);
            } else {
                val.put("time", System.currentTimeMillis());
                String value = String.valueOf(obj);
                if (NumberUtil.isLong(value)) {
                    val.put("value", NumberUtil.parseLong(value));
                } else if (NumberUtil.isDouble(value)) {
                    val.put("value", NumberUtil.parseDouble(value));
                } else if (NumberUtil.isInteger(value)) {
                    val.put("value", NumberUtil.parseInt(value));
                } else if (NumberUtil.isNumber(value)) {
                    //这个有问题 不知道是浮点还是整数
                    val.put("value", NumberUtil.parseNumber(value));
                } else {
                    val.put("value", value);
                }
            }
            params.put(key, val);
        });
        oneNet.put("params", params);

        try {
            String result = sendToOneNetRequest.sendRequest("/device/thing/property/post", accessKeyProductEnum, device.getImei(), oneNet);
            log.info("推送数据成功 /device/thing/property/post -> key:{} data:{} msg...{}",accessKeyProductEnum,oneNet, result);
        } catch (Exception e) {
            log.error("推送数据失败 /device/thing/property/post -> msg...{}", e.getMessage(), e);
        }
    }

    @Autowired
    private RedisUtil redisUtil;

    private final String MSG_ID_KEY = "onenet:msgid";

    /**
     * 生成消息ID。
     * <p>
     * 本方法通过在Redis中自增一个键值来生成唯一的消息ID。使用Redis的自增操作确保了ID的唯一性和序列性。
     * 选择Redis作为ID生成的存储介质是因为其高并发性和低延迟，能够保证在高并发场景下顺利生成ID。
     *
     * @return String 返回生成的消息ID的字符串形式。
     */
    private String generateMsgId() {
        // 通过Redis的incr命令自增MSG_ID_KEY的值，并返回自增后的结果
        long incr = redisUtil.incr(MSG_ID_KEY, 1);
        // 将自增后的长整型值转换为字符串形式并返回
        return String.valueOf(incr);
    }

}
