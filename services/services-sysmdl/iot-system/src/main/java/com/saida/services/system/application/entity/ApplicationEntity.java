package com.saida.services.system.application.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.saida.services.common.entity.BaseEntity;
import lombok.Data;

/**
 * <AUTHOR>
 * @ClassName ApplicationEntity
 * @Date 2024/2/28 17:29
 */
@Data
@TableName("iot_application")
public class ApplicationEntity extends BaseEntity<ApplicationEntity> {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 应用名称
     */
    private String name;

    /**
     * code码
     * 也是mqtt的userName
     */
    private String appCode;

    /**
     * 密匙
     * 也是mqtt的password
     */
    private String appSecret;

    /**
     * 应用访问地址
     */
    private String address;

    /**
     * 描述
     */
    private String description;

    /**
     *
     */
    private String clientId;

    /**
     *
     */
    private String topic;

    /**
     * api可访问的列表
     */
    private String apiAuth;



}
