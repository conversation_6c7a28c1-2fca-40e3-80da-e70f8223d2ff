package com.saida.services.system.device.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.saida.services.tools.attr.DisplayField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR>
 * @ClassName DeviceVo
 * @Date 2024/2/29 10:04
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DeviceVo {
    private Long id;

    // 设备id
    private String sn;

    // 设备名称
    private String name;

    // 产品名称
    private String productName;

    private String productSn;

    private Long productId;

    // 状态 1-在线  2-离线
    @DisplayField(field = "statusName", type = String.class)
    private Integer status;

    // 应用名称
    private String appName;

    // 注册时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    private Long cateId;
    /**
     * 在线过期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime expireTime;

    @TableField(exist = false)
    private String imei;

    /**
     * 1-启用  2-禁用
     */
    private Integer enabled;


    public Integer getDeviceStatus() {
        // 部分设备未设置离线时间，返回在线状态
        if (null == dataUpdateTime) {
            return status;
        }
        if (dataUpdateTime.plusMinutes(60).isBefore(LocalDateTime.now())) {
            return 2;
        }
        return 1;
    }

    /*
     * 描述
     */
    private String description;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime dataUpdateTime;

    @TableField(exist = false)
    private String deviceTypeName;
    /**
     * 固定类型
     * 1、北望霸凌终端
     * 2、北望7寸
     * 3、北望10寸
     * @see com.saida.services.iot.enums.IotRegularEnum
     * 有些型号有固定类型  有些没处理就从枚举文件中读取
     */
    private Integer regular;
}