package com.saida.services.system.hikvision.task;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.saida.services.iot.entity.DeviceEntity;
import com.saida.services.iot.entity.ProductEntity;
import com.saida.services.system.codec.dto.LogDto;
import com.saida.services.system.device.service.DeviceService;
import com.saida.services.system.event.PushAppEvent;
import com.saida.services.system.event.RealDataLogEvent;
import com.saida.services.system.event.SaveAndUpdateDataEvent;
import com.saida.services.system.event.listener.oneNet.enums.AccessKeyProductEnum;
import com.saida.services.system.hikvision.dto.HistoryDto;
import com.saida.services.system.hikvision.dto.QueryPage2Dto;
import com.saida.services.system.hikvision.enums.PlantEnum;
import com.saida.services.system.hikvision.util.HkNetUtil;
import com.saida.services.system.onenet.util.PublishUtil;
import com.saida.services.system.product.service.ProductService;
import com.xxl.job.core.handler.annotation.VlinkerXxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@EnableScheduling
@Slf4j
@ConditionalOnProperty(prefix = "scheduled-job", name = "enabled", havingValue = "true")
public class MultispectralTask {

    @Autowired
    private ProductService productService;

    @Autowired
    private DeviceService deviceService;

    @Autowired
    private HkNetUtil hkNetUtil;

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private PublishUtil publishUtil;

    @VlinkerXxlJob(
            value = "multispectralRealTimeDataTask",
            cron = "0 0 * * * ?",
            desc = "海康多光谱任务"
    )
//    @Scheduled(cron = "0 0 * * * ? ")
    public void multispectralRealTimeDataTask() {
        this.doMultispectralTask(AccessKeyProductEnum.KEY_MULTISPECTRAL.getProductSn());
    }

    /**
     * 处理多光谱任务。
     * 根据产品序列号查询产品信息，并进一步获取该产品关联的设备列表。
     * 从海康平台获取这些设备的多光谱数据，并对数据进行处理。
     *
     * @param productSn 产品序列号，用于查询产品信息。
     */
    private void doMultispectralTask(String productSn) {
        try {
            // 根据产品序列号查询产品信息。
            ProductEntity product = productService.getOne(new LambdaQueryWrapper<ProductEntity>().eq(ProductEntity::getSn, productSn));
            // 如果产品信息不存在，则直接返回。
            // 如果产品信息为空，则直接返回。
            if (ObjectUtil.isEmpty(product)) {
                return;
            }
            // 根据产品ID查询关联的设备列表。
            List<DeviceEntity> devices = deviceService.getListByProductIds(new ArrayList<>(Arrays.asList(product.getId())));
            // 如果设备列表为空，则记录日志后返回。
            if (CollectionUtil.isEmpty(devices)) {
                log.info("海康多光谱,查无设备");
                return;
            }
            // 使用IMEI号作为键，设备信息作为值，构建设备映射表。
            Map<String, DeviceEntity> deviceMap = devices.stream().collect(Collectors.toMap(DeviceEntity::getImei, Function.identity()));

            // 提取所有设备的IMEI号列表。
            List<String> deviceIds = devices.stream().map(DeviceEntity::getImei).collect(Collectors.toList());

            List<String> plantList = Arrays.stream(PlantEnum.values()).map(PlantEnum::getValue).collect(Collectors.toList());

            // 初始化查询参数，设置页码、页大小和设备ID列表。
            QueryPage2Dto dto = new QueryPage2Dto();
            dto.setPageNo(1);
            dto.setPageSize(100);
//            dto.setDeviceId(deviceIds);
            dto.setPlantIndex(plantList);
            // 设置查询开始日期为前一天。
//            dto.setStartDate(DateUtil.formatDate(DateUtil.offsetDay(new Date(), -1)));
            // 设置查询开始日期为今天日期
            dto.setStartDate(DateUtil.formatDate(DateUtil.date()));

            // 调用海康平台接口，获取多光谱数据。
            JSONArray jsonArray = hkNetUtil.plantIndexDay(dto);
            // 如果没有获取到数据，则记录日志后返回。
            if (null == jsonArray || jsonArray.size() < 1) {
                log.info("海康多光谱, 无实时数据");
                return;
            }

            JSONObject firstElement = jsonArray.getJSONObject(0);
            if (firstElement.isEmpty()) {
                log.info("海康多光谱, 无实时数据!");
                return;
            }
            // 对获取的多光谱数据进行处理。
            this.packMultispectralTask(firstElement, deviceMap, product);
        } catch (Exception e) {
            // 捕获在执行任务过程中可能出现的任何异常，记录错误日志。
            log.error("海康多光谱,原因", e);
        }
    }


    /**
     * 将多光谱任务的数据打包处理。
     * @param jsonObject 包含历史数据的JSONObject。
     * @param deviceMap 设备映射表，用于查找设备实体。
     * @param product 产品实体，用于标识数据所属产品。
     * 此方法主要负责解析JSON数组中的历史数据，对每个设备的每个光谱测量数据进行处理，
     * 包括日志记录、数据存储和推送操作。
     */
    private void packMultispectralTask(JSONObject jsonObject, Map<String, DeviceEntity> deviceMap, ProductEntity product){
        // 初始化日志列表，用于记录数据处理操作。
        List<LogDto> logDtoList = new ArrayList<>();
        // 获取所有植物类型，用于遍历检查每种植物的光谱数据。
        List<PlantEnum> plantEnumList = Arrays.stream(PlantEnum.values())
                .collect(Collectors.toList());
        // 遍历JSON数组中的每个对象，处理每个设备的数据。

        // 遍历所有植物类型，检查是否有对应的数据。
        for (PlantEnum plantEnum : plantEnumList) {
            // 如果当前植物类型的数据不存在，则跳过。
            if (null == jsonObject.get(plantEnum.getValue())) {
                continue;
            }
            // 解析当前植物类型的数据对象。
            HistoryDto historyDto = jsonObject.getObject(plantEnum.getValue(), HistoryDto.class);
            // 从数据对象中获取设备名称（IMEI）。
            //注：进过和海康研发人员沟通，deviceName填入imei值，因此可以将此字段当做是imei来使用
            String imei = historyDto.getDeviceName();
            // 如果设备映射表中不存在当前IMEI，则跳过。
            if (null == deviceMap.get(imei)) {
                continue;
            }
            // 从设备映射表中获取对应的设备实体。
            DeviceEntity device = deviceMap.get(imei);

            // 解析数据对象中的时间信息，并转换为时间戳。
            long time = DateUtil.parse(historyDto.getTime(), "yyyy/MM/dd HH:mm:ss").getTime();

            // 构建查询条件，查找已存在的报告记录。
            /*Query query = new Query();
            query.addCriteria(Criteria.where("imei").is(imei));
//            query.addCriteria(Criteria.where("id").is(historyDto.getId()));
            query.addCriteria(Criteria.where("plantIndex").is(plantEnum.getValue()));
            query.addCriteria(Criteria.where("time").is(historyDto.getTime()));
            // 从MongoDB中查询报告记录。
            JSONObject reportRecord = mongoTemplate.findOne(query, JSONObject.class, product.getSn() + "_record");
            // 如果已存在报告记录，则跳过当前数据处理。
            if (null != reportRecord) {
                log.info("新普惠实时数据,设备：" + imei + "，时间：" + time + "，数据已存在，跳过");
                continue;
            }*/

            // 记录日志
            LogDto log = new LogDto();
            log.setData(historyDto.getAverValue());
            log.setCreateTime(time);
            log.setProperty("averValue");
            log.setImei(imei);
            logDtoList.add(log);

            // 构建待存储和推送的数据对象。
            JSONObject data = new JSONObject();
            data.put("id", historyDto.getId());
            data.put("deviceName", device.getName());
            data.put("scene", historyDto.getScene());
            data.put("imei", imei);
            data.put("reportTime", time);    //上报时间
            data.put("type", 1);    //类型 1-普通消息 2-告警消息
            data.put("averValue", historyDto.getAverValue());
            data.put("plantIndex", plantEnum.getValue());
            data.put("time", historyDto.getTime());
            //图片ip地址转换
            data.put("picUrls", this.convertPicUrls(historyDto.getPicUrls()));
            data.put("checkStatus", historyDto.getCheckStatus());
            data.put("checkList", historyDto.getCheckList());

            // 发布事件，触发数据的保存和推送操作。
            applicationEventPublisher.publishEvent(new SaveAndUpdateDataEvent(this, imei, data, product));
            applicationEventPublisher.publishEvent(new PushAppEvent(this, device, data));
            //向OneNet平台推送数据
            publishUtil.pushOneNet(product.getSn(), device, data);
        }

        // 发布事件，触发日志数据的落地操作。
        applicationEventPublisher.publishEvent(new RealDataLogEvent(this, logDtoList, product));
    }

    /**
     * 将图片URL列表转换为JSON数组，替换URL中的IP和端口。
     *
     * @param picUrls 图片URL列表。
     * @return 转换后的JSON数组，包含替换IP和端口后的URL。
     */
    private JSONArray convertPicUrls(List<String> picUrls){
        // 初始化JSON数组，用于存储处理后的URL
        JSONArray jsonArray = new JSONArray();
        // 检查输入列表是否为空，如果为空直接返回空的JSON数组
        if (CollectionUtil.isEmpty(picUrls)) {
            return jsonArray;
        }
        // 遍历每个图片URL进行处理
        picUrls.forEach(picUrl -> {
            // 检查URL是否为空或只包含空格，如果是则忽略
            if (StringUtils.isNotBlank(picUrl)) {
                // 定义新的IP和端口组合
                String newIpPort = "************:6040";
                // 替换URL中的旧IP和端口为新IP和端口
                String replacedStr = picUrl.replace("************:6120", newIpPort);
                // 将处理后的URL添加到JSON数组中
                jsonArray.add(replacedStr);
            }
        });
        // 返回包含处理后URL的JSON数组
        return jsonArray;
    }


    @VlinkerXxlJob(
            value = "doDeviceOnlineTask",
            cron = "0 */10 * * * ?",
            desc = "海康-更新设备状态"
    )
//    @Scheduled(cron = "0 */10 * * * ?")
    public void deviceOnlineTask() {
        this.doDeviceOnlineTask("HGGMR2024071001");
    }

    /**
     * 处理设备在线状态检查任务。
     * 根据产品序列号查询产品及相关设备，并获取设备的在线状态，更新设备的在线状态信息。
     *
     * @param productSn 产品序列号
     */
    private void doDeviceOnlineTask(String productSn) {
        // 根据产品序列号查询产品信息
        ProductEntity product = productService.getOne(new LambdaQueryWrapper<ProductEntity>().eq(ProductEntity::getSn, productSn));
        // 如果产品信息为空，则直接返回。
        if (ObjectUtil.isEmpty(product)) {
            return;
        }
        // 根据产品ID查询关联的设备列表
        List<DeviceEntity> devices = deviceService.getListByProductIds(new ArrayList<>(Arrays.asList(product.getId())));
        // 如果设备列表为空，则记录日志后返回。
        if (CollectionUtil.isEmpty(devices)) {
            log.info("海康多光谱,查无设备");
            return;
        }

        // 提取所有设备的IMEI号列表
        List<String> imeiList = devices.stream().map(DeviceEntity::getImei).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(imeiList)) {
            log.info("海康多光谱, 设备imei为空");
            return;
        }
        // 调用海康接口查询设备的在线状态
        JSONArray jsonArray = hkNetUtil.queryOnlineStatus(imeiList);
        if (null == jsonArray) {
            log.info("海康多光谱, 无在线状态数据");
            return;
        }

        // 构建设备IMEI与在线状态的映射
        Map<String, Integer> statusMap = new HashMap<>();
        jsonArray.forEach(obj -> {
            if (null == obj) {
                return;
            }
            JSONObject jsonObject = (JSONObject) obj;
            if ("在线".equals(jsonObject.getString("status"))) {
                statusMap.put(jsonObject.getString("imei"), 1);
            } else {
                statusMap.put(jsonObject.getString("imei"), 2);
            }
        });
        // 更新设备的在线状态
        for (DeviceEntity device : devices) {
            if (null != statusMap.get(device.getImei())) {
                device.setStatus(statusMap.get(device.getImei()));
            }
        }
        // 批量更新设备状态
        deviceService.updateBatchById(devices);
    }


}
