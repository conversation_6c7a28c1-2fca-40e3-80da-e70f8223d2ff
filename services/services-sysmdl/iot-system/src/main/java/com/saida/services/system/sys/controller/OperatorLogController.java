package com.saida.services.system.sys.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.saida.services.common.base.Result;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.log.ModuleEnum;
import com.saida.services.system.sys.entity.OperatorLogEntity;
import com.saida.services.system.sys.service.OperatorLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 操作日志
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-10-13 09:18:19
 */
@RestController
@RequestMapping("sys/operatorlog")
public class OperatorLogController {
    @Autowired
    private OperatorLogService operatorLogService;

    /**
     * 获取功能模块名称
     * @return
     */
    @GetMapping("getModuleList")
    public Result getModuleList(){
        return Result.ok(ModuleEnum.getNames());
    }

    /**
     * 查询日志
     * @return
     */
    @GetMapping("listPage")
    public Result listPage(OperatorLogEntity entity){
        if(StringUtil.isEmpty(entity.getStartDate()) || StringUtil.isEmpty(entity.getEndDate())){
            return Result.error("开始结束时间不可为空");
        }
        entity.setStartDate(entity.getStartDate() + " 00:00:00");
        entity.setEndDate(entity.getEndDate() + " 23:59:59");
        IPage<OperatorLogEntity> page = operatorLogService.listPage(entity);
        return Result.ok(page);
    }
}
