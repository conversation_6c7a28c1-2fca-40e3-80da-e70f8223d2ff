package com.saida.services.system.event.listener;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.iot.entity.DeviceEntity;
import com.saida.services.iot.entity.ProductEntity;
import com.saida.services.system.device.service.DeviceService;
import com.saida.services.system.event.SaveAndUpdateDataEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.Map;

@Slf4j
@Component
public class SaveAndUpdateDataListener {

    @Autowired
    private MongoTemplate mongoTemplate;
    @Resource
    private DeviceService deviceService;

    @EventListener(classes = {SaveAndUpdateDataEvent.class})
    public JSONObject saveAndUpdateDataEvent(SaveAndUpdateDataEvent event){
        Map<String, Object> filteredMap = this.filterJsonObject(event.getData());
        JSONObject data = JSON.parseObject(JSON.toJSONString(filteredMap));
        ProductEntity product = event.getProduct();
        if (null == product) {
            return null;
        }
        String imei = event.getImei();
        JSONObject device = mongoTemplate.findById(imei, JSONObject.class, product.getSn());
        if(device == null || StringUtil.isEmpty(device.getString("_id"))){
            data.put("createTime", new Date().getTime());
            data.put("updateTime", new Date().getTime());
            data.put("_id",imei);
            log.info("存储表名：" + product.getSn());
            mongoTemplate.insert(data, product.getSn());
        }else{
            data.put("updateTime", new Date().getTime());
            Query query = new Query(Criteria.where("_id").is(imei));
            Update update = new Update();
            for(Map.Entry<String, Object> entry : data.entrySet()){
                if (null == entry.getKey() || null == entry.getValue()) {
                    continue;
                }
                update.set(entry.getKey(), entry.getValue());
            }
            log.info("存储表名：" + product.getSn());
            mongoTemplate.upsert(query, update, product.getSn());
        }
        data.put("createTime", new Date().getTime());
        data.remove("updateTime");
        log.info("存储表名：" + product.getSn() + "_record");
        mongoTemplate.insert(data, product.getSn() + "_record");

        // 更新设备表的数据更新时间
        LambdaUpdateWrapper<DeviceEntity> update = Wrappers.lambdaUpdate();
        update.eq(DeviceEntity::getImei, imei)
                .set(DeviceEntity::getStatus, 1)
                        .set(DeviceEntity::getDataUpdateTime, new Date());
        deviceService.update(update);
        return data;
    }

    /**
     * 过滤JSONObject，去除其中键或值为null的条目。
     * 该方法将JSONObject转换为Map，然后遍历Map，排除键或值为null的条目，
     * 最终返回一个新的LinkedHashMap，保持了原始条目的插入顺序。
     *
     * @param jsonObject 要过滤的JSONObject对象。
     * @return 过滤后的Map对象，不包含任何键或值为null的条目。
     */
    private static Map<String, Object> filterJsonObject(JSONObject jsonObject) {
        // 将JSONObject转换为Map
        Map<String, Object> map = jsonObject;

        // 创建一个新的LinkedHashMap以保持插入顺序
        Map<String, Object> filteredMap = new LinkedHashMap<>();

        for (Map.Entry<String, Object> entry : map.entrySet()) {
            if (entry.getKey() != null && entry.getValue() != null) {
                filteredMap.put(entry.getKey(), entry.getValue());
            }
        }

        return filteredMap;
    }
}
