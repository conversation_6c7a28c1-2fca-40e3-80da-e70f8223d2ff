package com.saida.services.system.event;

import com.saida.services.iot.entity.ProductEntity;
import com.saida.services.system.codec.dto.LogDto;
import lombok.Data;
import org.springframework.context.ApplicationEvent;

import java.util.List;


/**
 * 设备上报日志
 * 存在MongoDB里面
 */
@Data
public class RealDataLogEvent extends ApplicationEvent {

    private List<LogDto> data;

    private ProductEntity product;

    public RealDataLogEvent(Object source, List<LogDto> data, ProductEntity product){
        super(source);
        this.data = data;
        this.product = product;
    }
}
