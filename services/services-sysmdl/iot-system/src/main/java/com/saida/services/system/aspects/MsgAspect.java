package com.saida.services.system.aspects;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.saida.services.system.msg.entity.MsgRecordEntity;
import com.saida.services.system.msg.service.MsgRecordService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


@Aspect
@Component
@Slf4j
public class MsgAspect {

    @Pointcut("@annotation(com.saida.services.system.aspects.MsgOperation)")
    public void pointCut() {
    }

    @Autowired
    private MsgRecordService msgRecordService;

    @Around("pointCut()")
    public Object doAround(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
        Object result = proceedingJoinPoint.proceed();
        if (ObjectUtil.isEmpty(result)) {
            return null;
        }
        JSONObject data = (JSONObject) result;
        log.info("<========MsgAspect-doAround=======>,{}", data);

        MsgRecordEntity entity = new MsgRecordEntity();
        entity.setImei(data.getString("imei"));
        entity.setMsgInfo(data.getString("msgInfo"));
        entity.setMsgType(data.getString("msgType"));
        entity.setMsgStep(data.getString("msgStep"));
        entity.setMsgStatus(data.getIntValue("msgStatus"));
        entity.setMsgResponse(data.getString("msgResponse") != null ? data.getString("msgResponse") : "");
        entity.setCreateTime(LocalDateTimeUtil.of(DateUtil.parseDateTime(data.getString("createTime"))));
        entity.setCreateBy(0L);
        entity.setUpdateBy(0L);
        msgRecordService.save(entity);
        return data;
    }

}
