package com.saida.services.system.message.controller;

import com.alibaba.fastjson.JSONObject;
import com.saida.services.common.base.Result;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.entities.base.BaseRequest;
import com.saida.services.system.exception.IotBizException;
import com.saida.services.system.message.entity.MqPushEntity;
import com.saida.services.system.message.service.MqPushService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @ClassName MqPushController
 * @Date 2024/2/29 16:38
 */
@Slf4j
@RestController
@RequestMapping("/mq/push")
public class MqPushController {

    @Resource
    private MqPushService mqPushService;

    @PostMapping("/add")
    public Result add(MqPushEntity param) {
        try {
            checkSaveOrUpdate(param, false);
            mqPushService.add(param);
            return Result.ok();
        } catch (IotBizException e) {
            log.error("新增mq推送异常, param:{}, e:", JSONObject.toJSONString(param), e);
            return Result.error(e.getErrorMessage());
        } catch (Exception e) {
            log.error("新增mq推送异常, param:{}, e:", JSONObject.toJSONString(param), e);
            return Result.error("网络异常");
        }
    }

    @PostMapping("/edit")
    public Result edit(MqPushEntity param) {
        try {
            checkSaveOrUpdate(param, true);
            mqPushService.edit(param);
            return Result.ok();
        } catch (IotBizException e) {
            log.error("编辑mq推送异常, param:{}, e:", JSONObject.toJSONString(param), e);
            return Result.error(e.getErrorMessage());
        } catch (Exception e) {
            log.error("编辑mq推送异常, param:{}, e:", JSONObject.toJSONString(param), e);
            return Result.error("网络异常");
        }
    }

    @PostMapping("/delete")
    public Result delete(Long id) {
        try {
            if (Objects.isNull(id)) {
                throw new IotBizException("id不能为空");
            }
            mqPushService.deleteById(id);
            return Result.ok();
        } catch (IotBizException e) {
            log.error("删除mq推送异常, id:{}, e:", id, e);
            return Result.error(e.getErrorMessage());
        } catch (Exception e) {
            log.error("删除mq推送异常, id:{}, e:", id, e);
            return Result.error("网络异常");
        }
    }

    @GetMapping("info")
    public Result info(Long id) {
        try {
            if (Objects.isNull(id)) {
                throw new IotBizException("id不能为空");
            }

            return Result.ok(mqPushService.findById(id));
        } catch (IotBizException e) {
            log.error("查询mq推送详情异常, id:{}, e:", id, e);
            return Result.error(e.getErrorMessage());
        } catch (Exception e) {
            log.error("查询mq推送详情异常, id:{}, e:", id, e);
            return Result.error("网络异常");
        }
    }

    @GetMapping("listPage")
    public Result listPage(MqPushEntity param, BaseRequest baseRequest) {
        return Result.ok(mqPushService.listPage(param, baseRequest));
    }

    private void checkSaveOrUpdate(MqPushEntity param, Boolean idFlag) {
        if (idFlag && Objects.isNull(param.getId())) {
            throw new IotBizException("id不能为空");
        }

        if (StringUtil.isEmpty(param.getTopicName())) {
            throw new IotBizException("topic名称不能为空");
        }

        if (Objects.isNull(param.getAppId())) {
            throw new IotBizException("应用id不能为空");
        }

        if (StringUtil.isEmpty(param.getEnabled())) {
            throw new IotBizException("启用状态不能为空");
        }
    }
}
