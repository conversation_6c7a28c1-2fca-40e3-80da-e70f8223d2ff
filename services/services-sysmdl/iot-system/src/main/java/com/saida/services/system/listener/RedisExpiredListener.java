package com.saida.services.system.listener;

import com.alibaba.fastjson.JSON;
import com.saida.services.common.base.DtoResult;
import com.saida.services.feign.open.system.IFeignOpenSystemApiController;
import com.saida.services.feign.srv.system.IFeignSrvSystemApiController;
import com.saida.services.iot.constant.RedisKeyConstant;
import com.saida.services.open.dto.IotOnOffLinePushDto;
import com.saida.services.open.entity.IotDataSubscribeEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * redis过期key监听器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class RedisExpiredListener implements MessageListener {
    @Resource
    private IFeignOpenSystemApiController iFeignOpenSystemApiController;
    @Resource
    private IFeignSrvSystemApiController iFeignSrvSystemApiController;

    @Override
    public void onMessage(Message message, byte[] pattern) {
        String expiredKey = new String(message.getBody());
        // 筛选设备过期key
        if (expiredKey.startsWith(RedisKeyConstant.DEVICE_ONLINE_REDIS_KEY_PREFIX)) {
            log.info("设备过期key监听器处理.key={}", expiredKey);
            //获取最后一个冒号后的值
            String deviceId = expiredKey.substring(expiredKey.lastIndexOf(":") + 1);
            IotDataSubscribeEntity subscribeEntity = new IotDataSubscribeEntity();
            subscribeEntity.setDeviceId(deviceId);
            try {
                //vlinker2.0改造-通知应用平台设备下线
                subscribeEntity.setOnlineStatus(0);
                iFeignOpenSystemApiController.acceptIotDeviceOnOffline(subscribeEntity);
                //物联告警通用改造-通知融合平台设备下线
                DtoResult<Void> dtoResult = iFeignSrvSystemApiController.vlinkOpenIotOnOffLineReceive(IotOnOffLinePushDto.builder().deviceId(deviceId).online(false).build());
                log.info("设备下线通知融合平台结果：{}", JSON.toJSONString(dtoResult));
            } catch (Exception e) {
                log.error("设备过期key监听器处理异常，msg={}", e.getMessage(), e);
            }
        }
    }

}

