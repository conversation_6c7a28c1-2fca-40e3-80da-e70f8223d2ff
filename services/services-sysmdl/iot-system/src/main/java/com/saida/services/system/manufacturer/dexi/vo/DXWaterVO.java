package com.saida.services.system.manufacturer.dexi.vo;

import lombok.Data;

/**
 * 德希水位
 */
@Data
public class DXWaterVO {

    /**
     * 标识符，设备上传数据为1，服务器下发为2
     */
    private Integer flag;

    /**
     * 协议版本
     */
    private Double ver;

    /**
     * 报文类型，实时数据固定为：RealData
     */
    private String type;

    /**
     * 流水号，设备每一包数据流水号会自增，范围1-65535，重发的数据流水号不变。
     */
    private Integer SERIAL;

    /**
     * 设备站点编号，唯一编号，10位数字
     */
    private String ST;

    /**
     * 数据上传时间YYYY-MM-DD hh:mm:ss 格式
     */
    private String UT;

    /**
     * 数据正文，JSON格式
     */
    private String data;

}
