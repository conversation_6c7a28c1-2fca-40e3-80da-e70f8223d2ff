package com.saida.services.system.paAlarm.pager.test;

import com.saida.services.system.paAlarm.pager.demo.utils.Log;
import com.saida.services.system.paAlarm.pager.pa.APIHelper;
import com.saida.services.system.paAlarm.pager.pa.PAUtils;
import com.saida.services.system.paAlarm.pager.pa.Protocol;
import com.saida.services.system.paAlarm.protobuf.InterfacePaging.*;
import com.saida.services.system.paAlarm.protobuf.Public;
import com.saida.services.system.paAlarm.protobuf.Public.*;

import java.util.ArrayList;
import java.util.List;

public class PADeviceTest {

    private static final String TAG = PADeviceTest.class.getName();

    public static void getDevices() {

        // 1.获取全部设备信息
        // 获取当前登录用户管制的全部设备
        List<pb_pub_address> address = new ArrayList<>();
        pbret_main_getzoneinfo ret1 = APIHelper.if_main_getzoneinfo(0, address);
        System.out.println(ret1);
        System.out.println(address);


        // 2.获取多个设备信息
        // 获取当前登录用户管制的多个设备
//        address.add(pb_pub_address.newBuilder().setId(devid_1).setMask(32).setMode(2).build());
//        address.add(pb_pub_address.newBuilder().setId(devid_2).setMask(32).setMode(2).build());
//        pbret_main_getzoneinfo ret2 = APIHelper.if_main_getzoneinfo(0, address);
    }

    /**
     * 设备类型
     * 具体分类参考 docs/设备ID号分配规则.doc
     * @param devId 设备ID
     * @return
     */

    public String getDevTypeStr(int devId) {
        if ((devId & Protocol.SERVER_ID_AREA) == Protocol.SERVER_ID_AREA) {
            System.out.println("area server");
        } else if  ((devId & Protocol.SERVER_ID_MEDIA) == Protocol.SERVER_ID_MEDIA) {
            System.out.println("media server");
        } else if  ((devId & Protocol.SERVER_ID_STREAM) == Protocol.SERVER_ID_STREAM) {
            System.out.println("stream server");
        }

        if ((devId >> 24) == 0xff) {
            System.out.println("area server");
        } else if ((devId >> 24) == 0xfe) {
            System.out.println("media server");
        } else if ((devId >> 24) == 0xfd) {
            System.out.println("medstreamia server");
        }

        int mask = 0xfffc0000;
        int maskId = devId & mask;
        if (maskId == 0x4c0000) {
//            alarm_terminal: 0x4c0000, // 防欺凌终端 4980736
        } else if (maskId == 0x200000) {
            System.out.println("devType.pager_10");
//            pager_10: 0x200000, // 10寸屏寻呼器 2097152
        } else if (maskId == 0x580000) {
            System.out.println("devType.pager_7");
//            pager_7: 0x580000, // 7寸屏寻呼器 5767168
        } else if (maskId == 0x400000) {
            System.out.println("devType.terminal");  // 普通终端
        }

        return "";

    }
}
