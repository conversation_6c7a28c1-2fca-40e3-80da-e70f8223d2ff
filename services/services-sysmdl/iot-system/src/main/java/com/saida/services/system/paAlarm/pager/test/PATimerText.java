package com.saida.services.system.paAlarm.pager.test;

import com.google.protobuf.ByteString;
import com.google.protobuf.InvalidProtocolBufferException;
import com.saida.services.system.paAlarm.pager.demo.utils.Log;
import com.saida.services.system.paAlarm.pager.pa.APIHelper;
import com.saida.services.system.paAlarm.pager.pa.ProtocolUtils;
import com.saida.services.system.paAlarm.pager.pa.ReturnError;
import com.saida.services.system.paAlarm.protobuf.InterfacePaging.*;
import com.saida.services.system.paAlarm.protobuf.Public;

public class PATimerText {

    private static final String TAG = PATimerText.class.getName();

    public static void testTimer() {
//        pbif_set_setTimerPlan.Builder timerPlan = pbif_set_setTimerPlan.newBuilder();
//        timerPlan.setPlanId(0);
//        try {
//            timerPlan.setName(ByteString.copyFrom("aasaa", "UTF-8"));
//            byte[] pbData = timerPlan.build().toByteArray();
//            byte[] ret = CLibrary.call(45, pbData);
//            pbret_common common = pbret_common.parseFrom(ret);
//            Log.i(TAG, "planId " + common.getVal1());
//
//            // 方案逻辑：
//            // 每个用户都
//            byte[] pddata = pbif_set_getTimerPlan.newBuilder()
////                       .setUserId(1)
//                    .setFlag(0)
//                    .build().toByteArray();
//            ret = CLibrary.call(46, pddata);
//            pbret_set_timerPlan plans = pbret_set_timerPlan.parseFrom(ret);
//            for (int i = 0; i < plans.getSingleList().size(); i++) {
//                Log.i(TAG, plans.getSingle(i).getPlanId() + " " + plans.getSingle(i).getName().toStringUtf8());
//            }
//        } catch (UnsupportedEncodingException | InvalidProtocolBufferException e) {
//            e.printStackTrace();
//        }

    }

    public static void getTimer() {
        Log.i(TAG,"getTimer start");
        pbret_set_getTimer ret = APIHelper.if_set_getTimer(0, 0);
        if (ret == null || ret.getTimerList().size() == 0) {
            return;
        }

        Log.i(TAG, "TimerList.size():" + ret.getTimerList().size());
        Log.i(TAG, ret.getTimerList().get(0).getName().toStringUtf8());
        Log.i(TAG,"getTimer end " + ReturnError.getString(ret.getRet().getErr()));
    }

    public static void delTimer() {
        try {
            pbif_set_setTimer.Builder setTimerBuilder = pbif_set_setTimer.newBuilder();
            pbif_timer_single.Builder timerBuilder = pbif_timer_single.newBuilder();
            timerBuilder.setName(ByteString.copyFromUtf8("tting"));
            timerBuilder.setTimerId(0);
            pbif_set_setTimer pbif = setTimerBuilder.setTimer(timerBuilder.build()).build();
            Public.pbret_common ret = ProtocolUtils.call(pbif);

            int timerId = ret.getVal1();

            pbif_set_getTimer.Builder builder = pbif_set_getTimer.newBuilder();
            builder.setTimerid(timerId);
            pbif_set_getTimer getTimerPbif = builder.build();
            pbret_set_getTimer ret1 = ProtocolUtils.call(getTimerPbif);
            if (ret1.getRet().getErr() == 0) {
                Log.i(TAG, "getTimer: " + ret1.getTimer(0).getName().toStringUtf8());
            }

//            int timerId = 0;

            // del timer 再获取  timer， 检测是否存在，是否成功删除
            pbif_set_delTimer.Builder delTimerBuilder = pbif_set_delTimer.newBuilder();
            delTimerBuilder.addTimerId(timerId);
            Public.pbret_common ret_delTimer = ProtocolUtils.call(delTimerBuilder.build());

            builder = pbif_set_getTimer.newBuilder();
            builder.setTimerid(timerId);
//            pbif_set_getTimer getTimerPbif = ;

            ret1 = ProtocolUtils.call(builder.build());
            if (ret1.getRet().getErr() != 0) {
                Log.i(TAG, "delTimer_pbret.val1: " + ret1.getRet().getErr());
            }


        } catch (InvalidProtocolBufferException e) {
            e.printStackTrace();
        }

    }
}
