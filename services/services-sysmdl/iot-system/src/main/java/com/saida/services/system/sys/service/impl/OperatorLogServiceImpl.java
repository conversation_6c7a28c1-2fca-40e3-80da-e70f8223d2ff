package com.saida.services.system.sys.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.system.sys.entity.OperatorLogEntity;
import com.saida.services.system.sys.mapper.OperatorLogMapper;
import com.saida.services.system.sys.service.OperatorLogService;
import com.xxl.job.core.handler.annotation.VlinkerXxlJob;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;


@Service("operatorLogService")
public class OperatorLogServiceImpl extends ServiceImpl<OperatorLogMapper, OperatorLogEntity> implements OperatorLogService {

    /**
     * 每天凌晨4点清理表里面过多的数据
     */
    @VlinkerXxlJob(
            value = "clearData",
            cron = "0 0 4 * * ?",
            desc = "每天凌晨4点清理表里面过多的数据"
    )
//    @Scheduled(cron = "0 0 4 * * ?  ")
    public void clearData() {
        this.remove(new LambdaUpdateWrapper<OperatorLogEntity>()
                .le(OperatorLogEntity::getOperatorTime, LocalDateTime.now().minusDays(30 * 6)));
    }

    @Override
    public void saveOperatorLog(OperatorLogEntity entity) {
        save(entity);
    }

    @Override
    public IPage<OperatorLogEntity> listPage(OperatorLogEntity entity) {

        return page(new Page<>(entity.getPageNum(), entity.getPageSize()),
                new LambdaQueryWrapper<OperatorLogEntity>()
                        .ge(OperatorLogEntity::getOperatorTime, entity.getStartDate())
                        .le(OperatorLogEntity::getOperatorTime, entity.getEndDate())
                        .eq(!StringUtil.isEmpty(entity.getModule()), OperatorLogEntity::getModule, entity.getModule())
                        .eq(!StringUtil.isEmpty(entity.getSource()), OperatorLogEntity::getSource, entity.getSource())
                        .eq(!StringUtil.isEmpty(entity.getResult()), OperatorLogEntity::getResult, entity.getResult())
                        .like(!StringUtil.isEmpty(entity.getUserName()), OperatorLogEntity::getUserName, entity.getUserName())
                        .orderByDesc(OperatorLogEntity::getId)
        );
    }
}
