package com.saida.services.system.analyse.pojo.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Set;

/**
 * 添加分析任务
 */
@Getter
@Setter
public class AnalyseTaskListPageDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /*
     * 接口,SQL传参- 任务批次ID
     */
    private Long taskBatchId;

    /*
     * 接口传参- 设备树ID
     */
    private Long deviceTreeId;

    /*
     * 接口传参- 类型：1-区域；2-设备
     */
    private Integer type;

    /*
     * SQL传参- 设备ID
     */
    private Long deviceId;

    /*
     * SQL传参- 设备ID集合
     */
    private Set<Long> deviceIdSet;

    /*
     * SQL传参- 分析任务ID集合
     */
    private Set<Long> taskIdSet;

    /*
     * SQL传参- 分析任务ID集合
     */
    private Set<Long> notInTaskIdSet;

    /*
     * 接口,SQL传参- 算力来源
     */
    private Long algorithmSource;

    /*
     * 接口,SQL传参- 设备名称
     */
    private String deviceName;

    private String channelName;

    /*
     * 接口,SQL传参- 是否执行中：1-是；0-否
     */
    private Integer inProgress;

    /*
     * 接口,SQL传参- 运行算法ID
     */
    private Long algorithmId;

    /*
     * 接口,SQL传参- 状态：0-暂停；1-启动
     */
    private Integer enable;

    private Long queryDeviceId;
}