package com.saida.services.system.alarm.pojo.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
@JsonInclude
public class SdAiNmAlarmReceiveDto implements Serializable {
    private static final long serialVersionUID = 1L;

    private String type;

    private String channelId;

    private String token;

    private String session;

    @JsonProperty("Data")
    private Data Data;

    @Getter
    @Setter
    public static class Data implements Serializable {
        private static final long serialVersionUID = 1L;

        private String time;

        private String chId;

        private Integer pluginId;

        private String pluginName;

        private Integer seq;

        private Integer errorCode;

        private String errorMsg;

        private Result result;
    }

    @Getter
    @Setter
    public static class Result implements Serializable {
        private static final long serialVersionUID = 1L;

        @JsonProperty("ObjectNum")
        private Integer ObjectNum;

        @JsonProperty("ImageData")
        @JSONField(name = "ImageData", serialize = false, deserialize = false)
        private String ImageData;

        @JsonProperty("Objects")
        private List<Objects> Objects;

        /**
         * 告警图片宽
         */
        @JsonProperty("ImageWidth")
        private Integer ImageWidth;

        /**
         * 告警图片高
         */
        @JsonProperty("ImageHeight")
        private Integer ImageHeight;
    }

    @Getter
    @Setter
    public static class Objects implements Serializable {
        private static final long serialVersionUID = 1L;

        private Integer moduleId;

        private String moduleName;

        private double prob;

        @JsonProperty("X")
        private Integer X;

        @JsonProperty("Y")
        private Integer Y;

        @JsonProperty("Width")
        private Integer Width;

        @JsonProperty("Height")
        private Integer Height;
    }
}