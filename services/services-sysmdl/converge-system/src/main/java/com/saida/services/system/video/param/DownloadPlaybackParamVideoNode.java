package com.saida.services.system.video.param;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class DownloadPlaybackParamVideoNode extends VideoNodeBaseParam implements Serializable {
    private static final long serialVersionUID = 1L;

    private String uuid;

    private Long startTime;

    private Long endTime;
}