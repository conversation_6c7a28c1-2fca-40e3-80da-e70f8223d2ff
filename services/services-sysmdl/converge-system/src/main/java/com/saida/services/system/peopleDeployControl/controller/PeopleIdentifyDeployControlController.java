package com.saida.services.system.peopleDeployControl.controller;

import com.saida.services.common.base.DtoResult;
import com.saida.services.common.entity.BasePageInfoEntity;
import com.saida.services.entities.base.BaseRequest;
import com.saida.services.system.peopleDeployControl.dto.PeopleIdentifyDeployControlPageQryDto;
import com.saida.services.system.peopleDeployControl.service.PeopleIdentifyDeployControlService;
import com.saida.services.system.peopleDeployControl.vo.PeopleIdentifyDeployControlAddDto;
import com.saida.services.system.peopleDeployControl.vo.PeopleIdentifyDeployControlEditDto;
import com.saida.services.system.peopleDeployControl.vo.PeopleIdentifyDeployControlPageQryVo;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 人员识别布控(PeopleIdentifyDeployControlEntity)表控制层
 *
 * <AUTHOR>
 * @since 2025-06-17 09:42:36
 */
@RestController
@RequestMapping("peopleIdentifyDeployControl")
public class PeopleIdentifyDeployControlController {
    /**
     * 服务对象
     */
    @Resource
    private PeopleIdentifyDeployControlService peopleIdentifyDeployControlService;

    /**
     * 分页查询
     */
    @GetMapping("listPage")
    public DtoResult<BasePageInfoEntity<PeopleIdentifyDeployControlPageQryVo>> listPage(PeopleIdentifyDeployControlPageQryDto peopleIdentifyDeployControlDto, BaseRequest request) {
        return DtoResult.ok(peopleIdentifyDeployControlService.listPage(peopleIdentifyDeployControlDto, request));
    }

    /**
     * 新增
     */
    @PostMapping("insert")
    public DtoResult<Boolean> insert(@RequestBody @Validated PeopleIdentifyDeployControlAddDto dto) {
        return DtoResult.ok(peopleIdentifyDeployControlService.insert(dto));
    }

    /**
     * 修改
     */
    @PostMapping("update")
    public DtoResult<Boolean> update(@RequestBody @Validated PeopleIdentifyDeployControlEditDto dto) {
        return DtoResult.ok(peopleIdentifyDeployControlService.update(dto));
    }

    /**
     * 删除
     */
    @PostMapping("delete")
    public DtoResult<Boolean> delete(Long id) {
        return DtoResult.ok(peopleIdentifyDeployControlService.delete(id));
    }

}

