package com.saida.services.system.ops.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.saida.services.converge.entity.OpsDeviceVcpEntity;
import com.saida.services.converge.entity.dto.OpsDeviceVcpDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-10-24 17:11:13
 */
@Mapper
public interface OpsDeviceVcpMapper extends BaseMapper<OpsDeviceVcpEntity> {

    List<OpsDeviceVcpDto> getList(@Param("entity") OpsDeviceVcpDto entity);


    List<OpsDeviceVcpDto> getListByDeviceCodes(@Param("deviceCodes")List<String> deviceCodes);
}
