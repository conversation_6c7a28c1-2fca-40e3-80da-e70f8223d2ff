package com.saida.services.system.sys.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.saida.services.system.sys.entity.AttributeEntity;

import java.util.List;

/**
 * 属性表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-09-25 09:12:40
 */
public interface AttributeService extends IService<AttributeEntity> {

    void addOrUpdate(AttributeEntity entity);

    IPage<AttributeEntity> listPage(AttributeEntity entity);

    List<AttributeEntity> getFixedDicList();

    void delete(Long id);
}

