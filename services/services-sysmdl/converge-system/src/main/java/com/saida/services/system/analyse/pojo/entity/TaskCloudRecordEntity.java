package com.saida.services.system.analyse.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.saida.services.common.entity.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * 任务记录表
 */
@Getter
@Setter
@TableName("task_cloud_record")
@JsonInclude
public class TaskCloudRecordEntity extends BaseEntity<TaskCloudRecordEntity> {
    private static final long serialVersionUID = 1L;

    /**
     * 任务ID
     */
    private Long taskDispatchId;

    /**
     */
    private Integer type;
    /**
     */
    private String content;

    private Long time;

    private Long sort;

}
