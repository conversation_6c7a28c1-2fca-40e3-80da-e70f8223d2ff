package com.saida.services.system.algVideo.service;

import com.saida.services.algorithm.dto.*;
import com.saida.services.algorithm.vo.AlgorithmPrePointListVo;
import com.saida.services.common.base.DtoResult;
import com.saida.services.open.biz.resp.ThirdPartyPlatformsVideoLiveUrlResp;

import java.util.List;

public interface AlgVideoService {

    DtoResult<ThirdPartyPlatformsVideoLiveUrlResp> getLiveUrl(AlgorithmVideoLiveUrlDto dto);

    DtoResult<Void> stopLive(AlgorithmVideoLiveUrlDto dto);

    DtoResult<Void> ptzControl(AlgorithmPtzControlDto dto);

    DtoResult<List<AlgorithmPrePointListVo>> getPrePointList(AlgorithmPrePointListDto dto);

    DtoResult<Void> setPrePoint(AlgorithmSetPrePointDto dto);

    DtoResult<Boolean> canDelPrePoint(AlgorithmDelPrePointDto dto);

    DtoResult<Void> delPrePoint(AlgorithmDelPrePointDto dto);

    DtoResult<Void> jumpPrePoint(AlgorithmJumpPrePointDto dto);
}