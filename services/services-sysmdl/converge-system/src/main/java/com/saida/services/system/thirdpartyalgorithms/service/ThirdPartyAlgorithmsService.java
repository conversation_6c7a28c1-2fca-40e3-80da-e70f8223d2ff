package com.saida.services.system.thirdpartyalgorithms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.saida.services.common.base.Result;
import com.saida.services.entities.base.BaseRequest;
import com.saida.services.system.thirdpartyalgorithms.pojo.entity.ThirdPartyAlgorithmsEntity;

public interface ThirdPartyAlgorithmsService extends IService<ThirdPartyAlgorithmsEntity> {

    Result listPage(ThirdPartyAlgorithmsEntity entity, BaseRequest baseRequest);
}

