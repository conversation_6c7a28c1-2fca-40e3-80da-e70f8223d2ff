package com.saida.services.system.config;

import com.nimbusds.jose.JWSVerifier;
import com.nimbusds.jose.crypto.RSASSAVerifier;
import lombok.SneakyThrows;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.rsa.crypto.KeyStoreKeyFactory;
import org.springframework.stereotype.Component;

import java.security.KeyPair;
import java.security.interfaces.RSAPublicKey;

@Configuration
@Component
public class BeanConfig {

    @Bean
    public KeyPair keyPair() {
        KeyStoreKeyFactory factory = new KeyStoreKeyFactory(new ClassPathResource("oauth2.jks"), "Saida@123#@!".toCharArray());
        return factory.getKeyPair("saida", "Saida@123#@!".toCharArray());
    }

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    @SneakyThrows
    @Bean
    public RSAPublicKey rsaPublicKey() {
        KeyPair keyPair = keyPair();
        return (RSAPublicKey) keyPair.getPublic();
    }

    @SneakyThrows
    @Bean
    public JWSVerifier jWSVerifier() {
        return new RSASSAVerifier(rsaPublicKey());
    }
}
