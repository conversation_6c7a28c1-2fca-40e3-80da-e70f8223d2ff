package com.saida.services.system.alarm.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.saida.services.algorithm.entity.AlgorithmManageEntity;
import com.saida.services.algorithm.entity.AlgorithmMappingEntity;
import com.saida.services.common.base.DtoResult;
import com.saida.services.common.service.FileService;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.entities.pojo.FileModel;
import com.saida.services.enums.AlgAlgorithmSourceEnum;
import com.saida.services.enums.TerminalBoxTypeEnum;
import com.saida.services.system.alarm.entity.AlarmEntity;
import com.saida.services.system.alarm.pojo.dto.YltAiBoxAlarmPushDto;
import com.saida.services.system.alarm.service.AlarmService;
import com.saida.services.system.alarm.service.YltAiBoxAlarmReceiveService;
import com.saida.services.system.algorithm.service.AlgorithmManageService;
import com.saida.services.system.algorithm.service.AlgorithmMappingService;
import com.saida.services.system.callback.CallBackInvoke;
import com.saida.services.system.callback.CallBackMessage;
import com.saida.services.system.device.entity.CameraEntity;
import com.saida.services.system.device.entity.TerminalBoxEntity;
import com.saida.services.system.device.service.CameraService;
import com.saida.services.system.device.service.TerminalBoxService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
public class YltAiBoxAlarmReceiveServiceImpl implements YltAiBoxAlarmReceiveService {

    @Resource
    private CameraService cameraService;
    @Resource
    private FileService fileService;
    @Resource
    private TerminalBoxService terminalBoxService;
    @Resource
    private CallBackInvoke callBackInvoke;
    @Resource
    private AlarmService alarmService;
    @Resource
    private AlgorithmMappingService algorithmMappingService;
    @Resource
    private AlgorithmManageService algorithmManageService;

    @Override
    public JSONObject receiveYlt(JSONArray jsonArray) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("code", "1");
        jsonObject.put("msg", "success");
        try {
            List<YltAiBoxAlarmPushDto> yltAiBoxAlarmPushDtoList = JSONArray.parseArray(jsonArray.toJSONString(), YltAiBoxAlarmPushDto.class);
            log.info("V-LINKER算法中台.HTTP接收亚略特AI盒子告警...data={}", JSON.toJSON(yltAiBoxAlarmPushDtoList));

            if (CollectionUtil.isEmpty(yltAiBoxAlarmPushDtoList)) {
                log.info("V-LINKER算法中台.HTTP接收亚略特AI盒子告警...告警数据为空");
                return jsonObject;
            }
            List<AlgorithmManageEntity> algorithmManageEntityList = algorithmManageService.list();
            if (CollectionUtil.isEmpty(algorithmManageEntityList)) {
                log.info("V-LINKER算法中台.HTTP接收亚略特AI盒子告警...算法仓库数据为空");
                return jsonObject;
            }
            List<TerminalBoxEntity> terminalBoxEntityList = terminalBoxService.list(new LambdaQueryWrapper<TerminalBoxEntity>()
                    .eq(TerminalBoxEntity::getThirdType, TerminalBoxTypeEnum.ARATEK.getCode()));
            for (YltAiBoxAlarmPushDto yltAiBoxAlarmPushDto : yltAiBoxAlarmPushDtoList) {
                try {
                    String channelId = yltAiBoxAlarmPushDto.getFaisleinfoid();
                    String channelName = yltAiBoxAlarmPushDto.getFaislename();
                    String sn = yltAiBoxAlarmPushDto.getFcamerasn();
                    String fnetgateno = yltAiBoxAlarmPushDto.getFnetgateno();
                    String alarmType = yltAiBoxAlarmPushDto.getAnalysisTypeNumber();
                    String alarmTypeName = yltAiBoxAlarmPushDto.getFanlysistypeid();
                    String createTime = yltAiBoxAlarmPushDto.getFcreatetime();
                    String oriImgBase64 = yltAiBoxAlarmPushDto.getOriImgBase64();
                    String imgBase64 = yltAiBoxAlarmPushDto.getImgBase64();
                    log.info("V-LINKER算法中台.HTTP接收亚略特AI盒子告警...channelId={}, channelName={}, sn={}, fnetgateno={}, alarmType={}, alarmTypeName={}, createTime={}, oriImgBase64.length={}, imgBase64.length={}",
                            channelId, channelName, sn, fnetgateno, alarmType, alarmTypeName, createTime, oriImgBase64.length(), imgBase64.length());

                    CameraEntity cameraEntity = cameraService.getOne(new LambdaQueryWrapper<CameraEntity>()
                            .eq(CameraEntity::getChannelId, sn)
                            .last("LIMIT 1"), false);
                    if (cameraEntity == null) {
                        log.info("V-LINKER算法中台.HTTP接收亚略特AI盒子告警..对应的摄像头不存在...程序continue");
                        continue;
                    }
                    List<AlgorithmMappingEntity> algorithmMappingEntityList = algorithmMappingService.list(new LambdaUpdateWrapper<AlgorithmMappingEntity>()
                            .eq(AlgorithmMappingEntity::getSourceId, TerminalBoxTypeEnum.ARATEK.getCode())
                            .eq(AlgorithmMappingEntity::getCode, alarmType));

                    if (CollectionUtil.isEmpty(algorithmMappingEntityList)) {
                        log.info("V-LINKER算法中台.HTTP接收亚略特AI盒子告警..对应的盒子算法不存在...程序continue");
                        continue;
                    }

                    TerminalBoxEntity terminalBoxEntity = terminalBoxEntityList.stream().filter(item -> item.getThirdCode().equals(fnetgateno)).findFirst().orElse(null);
                    for (AlgorithmMappingEntity algorithmMappingEntity : algorithmMappingEntityList) {
                        Optional<AlgorithmManageEntity> optional = algorithmManageEntityList.stream().filter(item -> item.getId().equals(algorithmMappingEntity.getAlgorithmId())).findFirst();
                        if (!optional.isPresent()) {
                            log.info("V-LINKER算法中台.HTTP接收亚略特AI盒子告警..对应的平台算法不存在...程序continue");
                            continue;
                        }
                        AlgorithmManageEntity algorithmManageEntity = optional.get();
                        if (algorithmManageEntity.getStatus() != 1) {
                            log.info("V-LINKER算法中台.HTTP接收亚略特AI盒子告警..对应的平台算法未启用...程序continue");
                            continue;
                        }
                        AlarmEntity alarmEntity = new AlarmEntity();
                        alarmEntity.setAlarmSource(AlgAlgorithmSourceEnum.TERMINAL_BOX.getDicId());
                        alarmEntity.setDeviceId(cameraEntity.getId());
                        alarmEntity.setAlgorithmId(algorithmManageEntity.getId());
                        alarmEntity.setOriginalAlarmStr("aratek-box:" + alarmType);
                        alarmEntity.setAlarmTimeLong(System.currentTimeMillis());

                        if (terminalBoxEntity != null) {
                            alarmEntity.setTerminalBoxId(terminalBoxEntity.getId());
                        }

                        // 上传文件
                        DtoResult<FileModel> fileModelDtoResult = fileService.uploadBase64(oriImgBase64, "jpg", "alarm/ylt");
                        if (!fileModelDtoResult.success()) {
                            log.info("V-LINKER算法中台.HTTP接收亚略特AI盒子告警..上传文件失败...程序continue");
                            continue;
                        }
                        String oriImgUrl = fileModelDtoResult.getData().getUrl();

                        // 上传文件
                        DtoResult<FileModel> fileModelDtoResult2 = fileService.uploadBase64(imgBase64, "jpg", "alarm/ylt");
                        if (!fileModelDtoResult2.success()) {
                            log.info("V-LINKER算法中台.HTTP接收亚略特AI盒子告警..上传文件失败...程序continue");
                            continue;
                        }
                        String imgUrl = fileModelDtoResult2.getData().getUrl();
                        if (StringUtil.isNotEmpty(oriImgUrl)) {
                            alarmEntity.setOriginalImageUrl(oriImgUrl);
                        }
                        if (StringUtil.isNotEmpty(imgUrl)) {
                            alarmEntity.setAlarmImageUrl(imgUrl);
                        }

                        String alarmTime = DateUtil.format(DateUtil.date(Long.parseLong(createTime)), DatePattern.NORM_DATETIME_FORMAT);
                        if (StringUtil.isNotEmpty(alarmTime)) {
                            alarmEntity.setYear(alarmTime.substring(0, 4));
                            alarmEntity.setMonth(alarmTime.substring(0, 7));
                            alarmEntity.setDay(alarmTime.substring(0, 10));
                            alarmEntity.setTime(alarmTime.substring(11));
                            alarmEntity.setAlarmTime(alarmTime);
                        }
                        alarmEntity.setRemark(String.format("%s（%s）", AlgAlgorithmSourceEnum.TERMINAL_BOX.getName(), null == terminalBoxEntity ? "亚略特AI盒子" : terminalBoxEntity.getName()));
                        Long alarmId = IdWorker.getId();
                        alarmEntity.setId(alarmId);
                        CallBackMessage callBackMessage = new CallBackMessage() {{
                            setAlarmId(alarmId);
                            setDeviceId(String.valueOf(alarmEntity.getDeviceId()));
                            setAlgId(alarmEntity.getAlgorithmId());
                            setDeviceCode(cameraEntity.getThirdCode());
                            setChannelId(cameraEntity.getChannelId());
                            setAlertType(String.valueOf(algorithmManageEntity.getId()));
                            setCreateTime(alarmTime);
                            setAlertSource(Math.toIntExact(AlgAlgorithmSourceEnum.TERMINAL_BOX.getTag()));
                            setAlertSourceName(AlgAlgorithmSourceEnum.TERMINAL_BOX.getName());
                            setSrcUrl(alarmEntity.getAlarmImageUrl());
                        }};
                        alarmEntity.setCallbackMessage(JSON.toJSONString(callBackMessage));
                        callBackInvoke.callBack(alarmEntity, callBackMessage, true, true);
                    }
                } catch (Exception e) {
                    log.info("V-LINKER算法中台.HTTP接收亚略特AI盒子告警，错误..继续下次循环...msg={}", e.getMessage(), e);
                }
            }
        } catch (Exception e) {
            log.info("V-LINKER算法中台.HTTP接收亚略特AI盒子告警..错误...msg={}", e.getMessage(), e);
        } finally {
            log.info("V-LINKER算法中台.HTTP接收亚略特AI盒子告警...结束");
        }
        return jsonObject;
    }
}