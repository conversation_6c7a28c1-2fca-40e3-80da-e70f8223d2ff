package com.saida.services.system.mqtt;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.system.mqtt.pojo.AlgoTaskFind;
import com.saida.services.system.mqtt.pojo.DeviceInfo;
import com.saida.services.system.mqtt.pojo.MqttAlarm;
import com.saida.services.system.mqtt.pojo.TerminalBoxMessage;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class MqttDispatcher {

    @Autowired
    private MqttSubscribe mqttSubscribe;

    ObjectMapper objectMapper = new ObjectMapper();

    @Autowired
    private MqttAlarmReceive mqttAlarmReceive;

    @SneakyThrows
    public void dispatcher(JSONObject payload) {
        String method = payload.getString("method");
//        log.info("盒子mqtt告警接受 ----》 method:{}，payload:{}", method, payload);
        MqttSubscribe.EventResultType event = MqttSubscribe.EventResultType.getEventType(method);
        if (StringUtil.isEmpty(method) || event == null) {
            return;
        }
        TerminalBoxMessage message = JSON.toJavaObject(payload, TerminalBoxMessage.class);
        if (event == MqttSubscribe.EventResultType.push_data) {//接收盒子推送告警
            MqttAlarm alarm = JSON.parseObject(message.getBody().toString(), MqttAlarm.class);
            alarm.setBoxCode(message.getDeviceId());
            mqttAlarmReceive.receive(alarm);
            return;
        }
        MqttSubscribe.Event subscribe = mqttSubscribe.getOkSubscribe(message.getMessageId());
        if (subscribe == null) {
            return;
        }
        mqttSubscribe.removeOkSubscribe(message.getMessageId());
        switch (event) {
            case camera_find:
                MqttSubscribe.EventResult eventResult = new MqttSubscribe.EventResult(
                        MqttSubscribe.EventResultType.camera_find,
                        objectMapper.readValue(JSON.toJSONString(message.getBody()), new TypeReference<List<DeviceInfo>>() {
                        }));
                subscribe.response(eventResult);
                break;
            case algo_task_find:
                MqttSubscribe.EventResult eventResult2 = new MqttSubscribe.EventResult(
                        MqttSubscribe.EventResultType.algo_task_find,
                        objectMapper.readValue(JSON.toJSONString(message.getBody()), new TypeReference<List<AlgoTaskFind>>() {
                        }));
                subscribe.response(eventResult2);
                break;
        }
    }
}
