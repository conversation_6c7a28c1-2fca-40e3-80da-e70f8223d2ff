package com.saida.services.system.video.service.impl.v2;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.saida.services.common.base.DtoResult;
import com.saida.services.common.config.oss.OSSBean;
import com.saida.services.common.config.oss.OSSUtil;
import com.saida.services.common.config.oss.S3ObjectDto;
import com.saida.services.common.tools.RedisUtil;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.converge.entity.*;
import com.saida.services.converge.entity.dto.StreamUrlDto;
import com.saida.services.converge.qxNode.QxNodeCmdType;
import com.saida.services.converge.qxNode.req.sd.ConvergeChannelRecordTimeLineReq;
import com.saida.services.converge.qxNode.req.sd.ConvergeControlPlaybackReq;
import com.saida.services.converge.qxNode.resp.ChannelRecordTimeLine;
import com.saida.services.converge.qxNode.resp.ChannelRecordUrlResp;
import com.saida.services.converge.qxNode.resp.sd.ConvergeBaseResp;
import com.saida.services.converge.qxNode.resp.sd.ConvergeChannelRecordTimeLineResp;
import com.saida.services.converge.vo.DownloadLocalPlaybackVo;
import com.saida.services.deviceApi.req.CommonCallDeviceByVideoReq;
import com.saida.services.deviceApi.req.CommonStopPlayBackReq;
import com.saida.services.deviceApi.resp.CommonCallDeviceByVideoResp;
import com.saida.services.exception.BizRuntimeException;
import com.saida.services.open.resp.VlinkerConvergeAddDeviceResp;
import com.saida.services.open.resp.VlinkerConvergeSnapshotResp;
import com.saida.services.open.resp.rtc.VlinkerConvergeRtcConnectResp;
import com.saida.services.system.client.nodev2.SunConfig;
import com.saida.services.system.client.nodev2.SunGrpcUtil;
import com.saida.services.system.nodeGrpc.GrpcConfig;
import com.saida.services.system.ops.service.*;
import com.saida.services.system.pb.*;
import com.saida.services.system.video.algHall.AlgHall;
import com.saida.services.system.video.algHall.AlgRoom;
import com.saida.services.system.video.param.*;
import com.saida.services.system.video.service.VideoStreamService;
import io.grpc.ManagedChannel;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Hex;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;

/**
 *
 */
@Slf4j
@Service
public class VideoNodeV2StreamServiceImpl implements VideoStreamService {

    @Resource
    private DeviceService deviceService;
    @Resource
    private SignalNodeService signalNodeService;
    @Resource
    private DeviceRecordPlanService deviceRecordPlanService;
    @Resource
    private CloudStorageService cloudStorageService;
    @Resource
    private OSSUtil ossUtil;
    @Resource
    private GrpcConfig grpcConfig;

    @Getter
    private static final String platformId = "conv";
    @Resource
    private RedisUtil redisUtil;

    @Override
    public DtoResult<VlinkerConvergeAddDeviceResp> addDevice(DeviceEntity entity, SignalNodeEntity signalNode) {
        if (entity.getAccessWay() != 2 && entity.getAccessWay() != 5) {
            return DtoResult.error("当前节点暂不支持此协议接入！");
        }

        switch (entity.getAccessWay()) {
            case 2:
                OpenSunSaida.AddSaidaDeviceReply addSaidaDeviceReply;
                try {
                    ManagedChannel grpcChannel = grpcConfig.getGrpcChannel(signalNode);
                    SunOpenGrpc.SunOpenBlockingStub sunOpenBlockingStub = SunOpenGrpc.newBlockingStub(grpcChannel);
                    addSaidaDeviceReply = sunOpenBlockingStub.addSaidaDevice(OpenSunSaida.AddSaidaDeviceRequest.newBuilder()
                            .setAuthInfo(grpcConfig.getAuthUser())
                            .setDeviceId(entity.getDeviceCode())
                            .setPlatformId(platformId)
                            .setName(entity.getName())
                            .build());
                } catch (Exception e) {
                    log.error("设备新增失败！", e);
                    return DtoResult.error("设备新增失败！");
                }
                if (addSaidaDeviceReply.getStatus() == OpenCommonEnum.ReplyStatus.REPLY_STATUS_SUC) {
                    VlinkerConvergeAddDeviceResp resp = new VlinkerConvergeAddDeviceResp();
                    resp.setNodeXId(addSaidaDeviceReply.getXId());
                    resp.setAuthKey(addSaidaDeviceReply.getAuthKey());
                    resp.setSignalingAddr(addSaidaDeviceReply.getSignalingAddr());
                    return DtoResult.ok(resp);
                } else {
                    return DtoResult.error("设备新增失败！", addSaidaDeviceReply.getDesc());
                }
            case 5:
                OpenSunSaida.AddCommonDeviceReply addCommonDeviceReply;
                try {
                    ManagedChannel grpcChannel = grpcConfig.getGrpcChannel(signalNode);
                    SunOpenGrpc.SunOpenBlockingStub sunOpenBlockingStub = SunOpenGrpc.newBlockingStub(grpcChannel);
                    addCommonDeviceReply = sunOpenBlockingStub.addCommonDevice(OpenSunSaida.AddCommonDeviceRequest.newBuilder()
                            .setAuthInfo(grpcConfig.getAuthUser())
                            .setDeviceId(entity.getDeviceCode())
                            .setPlatformId(platformId)
                            .setPlatformType(OpenCommonEnum.PlatformType.PLATFORM_TYPE_HIK_E_HOME_ISUP)
                            .setName(entity.getName())
                            .build());
                } catch (Exception e) {
                    log.error("设备新增失败！", e);
                    return DtoResult.error("设备新增失败！");
                }
                if (addCommonDeviceReply.getStatus() == OpenCommonEnum.ReplyStatus.REPLY_STATUS_SUC) {
                    VlinkerConvergeAddDeviceResp resp = new VlinkerConvergeAddDeviceResp();
                    resp.setNodeXId(addCommonDeviceReply.getXId());
                    resp.setAuthKey(addCommonDeviceReply.getAuthKey());
                    return DtoResult.ok(resp);
                } else {
                    return DtoResult.error("设备新增失败！", addCommonDeviceReply.getDesc());
                }
            default:
                return DtoResult.error("当前节点暂不支持此协议接入！");
        }

    }

    @Override
    public DtoResult<VlinkerConvergeAddDeviceResp> delDevice(DeviceEntity entity, SignalNodeEntity signalNode) {
        if (entity.getNodeXId() == null) {
            return DtoResult.ok();
        }
        OpenSun.StatefulReply baseReply;
        try {
            OpenCommonMessage.RequestAuthInfo authInfo = grpcConfig.getAuthUserByXId(entity.getNodeXId());
            ManagedChannel grpcChannel = grpcConfig.getGrpcChannel(signalNode);
            SunOpenGrpc.SunOpenBlockingStub sunOpenBlockingStub = SunOpenGrpc.newBlockingStub(grpcChannel);
            baseReply = sunOpenBlockingStub.deviceOperation(OpenSunMessage.DeviceOperationRequest.newBuilder()
                    .setAuthInfo(authInfo)
                    .setXId(entity.getNodeXId())
                    .setOperation(OpenSunMessage.DeviceOperationRequest.DeviceOperation.DEVICE_OPERATION_DELETE)
                    .build());
        } catch (Exception e) {
            log.error("设备删除失败！", e);
            return DtoResult.error("设备删除失败！");
        }
        if (baseReply.getStatus() == OpenCommonEnum.ReplyStatus.REPLY_STATUS_SUC) {
            return DtoResult.ok();
        } else {
            return DtoResult.error("设备新增失败！", baseReply.getDesc());
        }
    }

    @Resource
    private AlgHall algHall;
    @Resource
    private OpsDeviceChannelService opsDeviceChannelService;
    @Autowired(required = false)
    private SunConfig sunConfig;

    @Override
    public DtoResult<StreamUrlDto> getStreamUrl(GetStreamUrlParamVideoNode param) {
        if (param.getVideoProtocol() == null) {
            param.setVideoProtocol(8);
        }
        if (param.getPlayType() == null) {
            param.setPlayType(1);
        }
        if (param.getNetworking() == null) {
            param.setNetworking(1);
        }
        if (param.getUserPlatformType() == null) {
            param.setUserPlatformType(1);
        }
        OpenCommonEnum.ValidityPeriodType validityPeriodType = OpenCommonEnum.ValidityPeriodType.VALIDITY_PERIOD_TYPE_NORMAL;
        if (param.getUserPlatformType() == 11) {
            validityPeriodType = OpenCommonEnum.ValidityPeriodType.VALIDITY_PERIOD_TYPE_DEBUG;
        } else if (param.getUserPlatformType() == 12) {
            validityPeriodType = OpenCommonEnum.ValidityPeriodType.VALIDITY_PERIOD_TYPE_PERMANENT;
        }
        SunOpenGrpc.SunOpenBlockingStub sunOpenBlockingStub = grpcConfig.getStub(param.getDeviceEntity().getNodeId());
        OpenCommonMessage.RequestAuthInfo authInfo = grpcConfig.getAuthUserByXIdAndUserPlatformType(param.getChannelXId());
        StreamUrlDto streamUrlDto = new StreamUrlDto();
        if (param.getVideoProtocol() == 9) {
            param.setNetworking(3);
        }
        // 5 webrtc 8 私有流
        if (param.getVideoProtocol() == 8 || param.getVideoProtocol() == 9) {
            if (param.getOpenAi() || param.getStartAi()) {
                //如果开启AI 则返回AI房间
                AlgRoom algRoom = algHall.computeIfAbsent(param.getChannelXId(), (xId) -> {
                    if (StringUtil.isNotEmpty(param.getOpsDeviceChannelEntity().getAiRoom())) {
                        try {
                            OpenStreamMessage.StreamReplyForPublisher streamReplyForPublisher = OpenStreamMessage.StreamReplyForPublisher.parseFrom(Base64.getDecoder().decode(param.getOpsDeviceChannelEntity().getAiRoom()));
                            return new AlgRoom(param.getChannelXId(), streamReplyForPublisher);
                        } catch (Exception e) {
                            log.info("获取AI房间失败！(parseFrom)", e);
                        }
                    }
                    boolean inLocalAreaNetwork = true;
                    if (sunConfig != null) {
                        inLocalAreaNetwork = sunConfig.getConfu().getInLocalAreaNetwork();
                    }
                    log.info("创建新的AI房间！inLocalAreaNetwork:{}", inLocalAreaNetwork);
                    OpenStreamMessage.StreamPublishLowLevelRequest streamPublishRequest = OpenStreamMessage.StreamPublishLowLevelRequest
                            .newBuilder()
                            .setAuthInfo(grpcConfig.getAuthUser())
                            .setInLocalAreaNetwork(inLocalAreaNetwork)
                            .setNetworking(OpenCommonEnum.BaseNetworkingProtocol.NETWORK_PROTOCOL_TCP)
                            .setValidityPeriodType(OpenCommonEnum.ValidityPeriodType.VALIDITY_PERIOD_TYPE_DEBUG)
                            .build();
                    OpenStreamMessage.StreamReplyForPublisher streamReplyForPublisher = sunOpenBlockingStub.streamPublishLowLevel(streamPublishRequest);

                    byte[] byteArray = streamReplyForPublisher.toByteArray();
                    opsDeviceChannelService.update(new LambdaUpdateWrapper<OpsDeviceChannelEntity>()
                            .eq(OpsDeviceChannelEntity::getId, param.getOpsDeviceChannelEntity().getId())
                            .set(OpsDeviceChannelEntity::getAiRoom, Base64.getEncoder().encodeToString(byteArray)));
                    return new AlgRoom(param.getChannelXId(), streamReplyForPublisher);
                });
                if (param.getOpenAi()) {
                    return getAiRoom(param, algRoom);
                } else {
                    return startAiRoom(param, algRoom);
                }
            }
            OpenStreamMessage.StreamPlayRequest build = OpenStreamMessage.StreamPlayRequest
                    .newBuilder()
                    .setPlayTypeValue(param.getPlayType())
                    .setNetworkingValue(param.getNetworking())
                    .setXId(param.getChannelXId())
                    .setAuthInfo(authInfo)
                    .setValidityPeriodType(validityPeriodType)
                    .build();
            OpenStreamMessage.StreamReplyForPlayer streamReply;
            try {
                streamReply = sunOpenBlockingStub.streamPlayVer1(build);
            } catch (Exception e) {
                log.error("获取直播流失败！", e);
                return DtoResult.error("获取直播流失败！");
            }
            if (streamReply.getStatus() != OpenCommonEnum.ReplyStatus.REPLY_STATUS_SUC) {
                return DtoResult.error("获取直播流失败！", streamReply.getDesc());
            }
            byte[] byteArray = streamReply.toByteArray();
            if (param.getVideoProtocol() == 8) {
                streamUrlDto.setSdUrl(Base64.getEncoder().encodeToString(byteArray));
            } else {
                streamUrlDto.setSdWebRtcUrl(Base64.getEncoder().encodeToString(byteArray));
            }
        } else if (param.getVideoProtocol() == 4) {
            OpenSunMessage.GetStandardStreamURLRequest request = OpenSunMessage.GetStandardStreamURLRequest
                    .newBuilder()
                    .setType(OpenCommonEnum.StandardProtocolType.STANDARD_PROTOCOL_TYPE_RTSP)
                    .setPlayReq(OpenStreamMessage.StreamPlayRequest.newBuilder()
                            .setPlayTypeValue(param.getPlayType())
                            .setNetworkingValue(param.getNetworking())
                            .setXId(param.getChannelXId())
                            .setAuthInfo(authInfo)
                            .setValidityPeriodType(validityPeriodType)
                            .build())
                    .build();
            OpenSun.StatefulReply standardStreamURIVer;
            try {
                standardStreamURIVer = sunOpenBlockingStub.getStandardStreamURLVer1(request);
            } catch (Exception e) {
                log.error("获取直播流失败！", e);
                return DtoResult.error("获取直播流失败");
            }
            if (standardStreamURIVer.getStatus() != OpenCommonEnum.ReplyStatus.REPLY_STATUS_SUC) {
                return DtoResult.error("开启直播失败！", standardStreamURIVer.getDesc());
            }
            streamUrlDto.setRtsp(standardStreamURIVer.getResult());
        } else if (param.getVideoProtocol() == 1) {
            OpenSunMessage.GetStandardStreamURLRequest request = OpenSunMessage.GetStandardStreamURLRequest
                    .newBuilder()
                    .setType(OpenCommonEnum.StandardProtocolType.STANDARD_PROTOCOL_TYPE_HTTP_FLV)
                    .setPlayReq(OpenStreamMessage.StreamPlayRequest.newBuilder()
                            .setPlayTypeValue(param.getPlayType())
                            .setNetworkingValue(param.getNetworking())
                            .setXId(param.getChannelXId())
                            .setAuthInfo(authInfo)
                            .setValidityPeriodType(validityPeriodType)
                            .build())
                    .build();
            OpenSun.StatefulReply standardStreamURIVer;
            try {
                standardStreamURIVer = sunOpenBlockingStub.getStandardStreamURLVer1(request);
            } catch (Exception e) {
                log.error("获取直播流失败！", e);
                return DtoResult.error("获取直播流失败！");
            }
            if (standardStreamURIVer.getStatus() != OpenCommonEnum.ReplyStatus.REPLY_STATUS_SUC) {
                return DtoResult.error("获取直播流失败！", standardStreamURIVer.getDesc());
            }
            streamUrlDto.setHttpFlv(standardStreamURIVer.getResult());
        } else if (param.getVideoProtocol() == 7) {
            OpenSunMessage.GetStandardStreamURLRequest request = OpenSunMessage.GetStandardStreamURLRequest
                    .newBuilder()
                    .setType(OpenCommonEnum.StandardProtocolType.STANDARD_PROTOCOL_TYPE_WS_FLV)
                    .setPlayReq(OpenStreamMessage.StreamPlayRequest.newBuilder()
                            .setPlayTypeValue(param.getPlayType())
                            .setNetworkingValue(param.getNetworking())
                            .setXId(param.getChannelXId())
                            .setAuthInfo(authInfo)
                            .setValidityPeriodType(validityPeriodType)
                            .build())
                    .build();
            OpenSun.StatefulReply standardStreamURIVer;
            try {
                standardStreamURIVer = sunOpenBlockingStub.getStandardStreamURLVer1(request);
            } catch (Exception e) {
                log.error("获取直播流失败！", e);
                return DtoResult.error("获取直播流失败！");
            }
            if (standardStreamURIVer.getStatus() != OpenCommonEnum.ReplyStatus.REPLY_STATUS_SUC) {
                return DtoResult.error("获取直播流失败！", standardStreamURIVer.getDesc());
            }
            streamUrlDto.setWsFlv(standardStreamURIVer.getResult());
        } else {
            return DtoResult.error("暂不支持！");
        }
        return DtoResult.ok(streamUrlDto);
    }

    /*
     * 1、普通播放
     * StreamPlayVer1
     * 2、AI播放
     * conv-> sun.StreamPublishLowLevel(room) - 创建一个空房间
     * conv-> sun.StreamPlayVer1(playVer1) 拿到原流
     * conv-> room 的 stream_token 调用 sun.StreamJoin(streamJoin1) 拿到confu需要推流的信息
     * confu-> playVer1 的 addr 和 handshake_data 进行帧交互
     * confu-> streamJoin1 的 addr 和 handshake_data 进行推流
     * conv-> room 的 stream_token 调用 sun.StreamJoin(streamJoin2) 拿到观看者的握手信息
     *
     */
    public DtoResult<StreamUrlDto> getAiRoom(GetStreamUrlParamVideoNode param, AlgRoom algRoom) {
        SunOpenGrpc.SunOpenBlockingStub sunOpenBlockingStub = grpcConfig.getStub(param.getDeviceEntity().getNodeId());
        OpenStreamMessage.StreamJoinRequest streamJoinRequest = OpenStreamMessage.StreamJoinRequest
                .newBuilder()
                .setAuthInfo(grpcConfig.getAuthUser())
                .setByLanAddr(false)
                .setNetworkingValue(param.getNetworking())
                .setStreamToken(algRoom.getStreamReplyForPublisher().getStreamToken())
                .build();
        OpenStreamMessage.StreamReplyForPlayer streamReplyForPlayer = sunOpenBlockingStub.streamJoin(streamJoinRequest);
        if (streamReplyForPlayer.getStatus() != OpenCommonEnum.ReplyStatus.REPLY_STATUS_SUC) {
            return DtoResult.error("获取直播流失败！", streamReplyForPlayer.getDesc());
        }
        OpenStreamMessage.StreamReplyForPlayer.Builder builder = streamReplyForPlayer.toBuilder();
        builder.setAddr(builder.getAddr().replace("127.0.0.1", "192.168.60.114"));
        streamReplyForPlayer = builder.build();
        log.info("getAlRoom,xid:{} networking:{}", param.getChannelXId(), param.getNetworking());
        StreamUrlDto streamUrlDto = new StreamUrlDto();
        byte[] byteArray = streamReplyForPlayer.toByteArray();
        streamUrlDto.setSdUrl(Base64.getEncoder().encodeToString(byteArray));
        return DtoResult.ok(streamUrlDto);
    }


    public DtoResult<StreamUrlDto> startAiRoom(GetStreamUrlParamVideoNode param, AlgRoom algRoom) {
        SunOpenGrpc.SunOpenBlockingStub sunOpenBlockingStub = grpcConfig.getStub(param.getDeviceEntity().getNodeId());
        OpenCommonMessage.RequestAuthInfo authInfo = grpcConfig.getAuthUserByXIdAndUserPlatformType(param.getChannelXId());
        StreamUrlDto streamUrlDto = new StreamUrlDto();
        byte[] byteArray = algRoom.getStreamReplyForPublisher().toByteArray();
        log.info("获取AI房间成功！{}", algRoom.getStreamReplyForPublisher().getAddr());
        String hex = Hex.encodeHexString(algRoom.getStreamReplyForPublisher().getHandshakeData().toByteArray());
        log.info("获取AI房间成功！{}", hex);
        streamUrlDto.setSdPushUrl(Base64.getEncoder().encodeToString(byteArray));
        boolean inLocalAreaNetwork = true;
        if (sunConfig != null) {
            inLocalAreaNetwork = sunConfig.getConfu().getInLocalAreaNetwork();
        }
        OpenStreamMessage.StreamPlayRequest build = OpenStreamMessage.StreamPlayRequest
                .newBuilder()
                .setPullStreamWithLanAddr(inLocalAreaNetwork)
                .setPlayTypeValue(param.getPlayType())
                .setNetworking(OpenCommonEnum.BaseNetworkingProtocol.NETWORK_PROTOCOL_TCP)
                .setXId(param.getChannelXId())
                .setAuthInfo(authInfo)
                .setValidityPeriodType(OpenCommonEnum.ValidityPeriodType.VALIDITY_PERIOD_TYPE_DEBUG)
                .build();
        OpenStreamMessage.StreamReplyForPlayer streamReply;
        try {
            streamReply = sunOpenBlockingStub.streamPlayVer1(build);
        } catch (Exception e) {
            log.error("获取直播流失败！", e);
            return DtoResult.error("获取直播流失败！");
        }
        if (streamReply.getStatus() != OpenCommonEnum.ReplyStatus.REPLY_STATUS_SUC) {
            return DtoResult.error("获取直播流失败！", streamReply.getDesc());
        }
        streamUrlDto.setSdUrl(Base64.getEncoder().encodeToString(streamReply.toByteArray()));
        return DtoResult.ok(streamUrlDto);
    }

    @Override
    public DtoResult<Void> stopLive(VideoNodeBaseParam param) {
        SignalNodeEntity node = signalNodeService.getById(param.getDeviceEntity().getNodeId());
        if (node == null) {
            return DtoResult.error("节点不存在");
        }
        OpenSun.StatefulReply baseReply;
        try {
            OpenCommonMessage.RequestAuthInfo authInfo = grpcConfig.getAuthUserByXId(param.getChannelXId());
            ManagedChannel grpcChannel = grpcConfig.getGrpcChannel(node);
            SunOpenGrpc.SunOpenBlockingStub sunOpenBlockingStub = SunOpenGrpc.newBlockingStub(grpcChannel);
            baseReply = sunOpenBlockingStub.deviceOperation(OpenSunMessage.DeviceOperationRequest.newBuilder()
                    .setAuthInfo(authInfo)
                    .setXId(param.getChannelXId())
                    .setOperation(OpenSunMessage.DeviceOperationRequest.DeviceOperation.DEVICE_OPERATION_STOP_STREAM)
                    .build());
        } catch (Exception e) {
            log.error("停止直播失败！", e);
            return DtoResult.error("停止直播失败！");
        }
        if (baseReply.getStatus() == OpenCommonEnum.ReplyStatus.REPLY_STATUS_SUC) {
            return DtoResult.ok();
        } else {
            return DtoResult.error("停止直播失败！", baseReply.getDesc());
        }
    }

    @Resource
    private SunGrpcUtil sunGrpcUtil;

    @Override
    public DtoResult<VlinkerConvergeSnapshotResp> getSnapshot(GetSnapshotParam param) {
        // 2. 获取设备所在节点
        SignalNodeEntity node = signalNodeService.getById(param.getDeviceEntity().getNodeId());
        if (node == null) {
            return DtoResult.error("节点不存在");
        }
        ConvergeBaseResp baseResp = sunGrpcUtil.signaling(param.getDeviceEntity().getNodeId(), param.getDeviceEntity().getNodeXId(),
                QxNodeCmdType.CONVERGE_GET_SNAPSHOT.getType());
        VlinkerConvergeSnapshotResp vlinkerConvergeSnapshotResp = new VlinkerConvergeSnapshotResp();
        vlinkerConvergeSnapshotResp.setType("image/jpeg");
        String base64 = baseResp.getRsp().replaceAll("^\"|\"$", "");
        vlinkerConvergeSnapshotResp.setImg(base64);
        return DtoResult.ok(vlinkerConvergeSnapshotResp);
    }

    @Override
    public DtoResult<LinkedHashMap<String, Integer>> getChannelRecordMonthsByCloud(GetChannelRecordMonthsParamVideoNode param) {
        //查询s3
        DeviceRecordPlanEntity one = deviceRecordPlanService.getOne(new LambdaQueryWrapper<DeviceRecordPlanEntity>()
                .eq(DeviceRecordPlanEntity::getDeviceId, param.getDeviceId())
                .eq(DeviceRecordPlanEntity::getChannelId, param.getChannelId()), false);
        if (one == null) {
            return DtoResult.error("录像计划不存在！");
        }
        CloudStorageEntity cloudStorageEntity = cloudStorageService.getById(one.getStorageId());
        if (cloudStorageEntity == null) {
            return DtoResult.error("云存储不存在！");
        }
        OSSBean build = OSSBean
                .builder()
                .accessKey(cloudStorageEntity.getKeyId())
                .secretKey(cloudStorageEntity.getSecret())
                .endPoint(cloudStorageEntity.getEndPoint())
                .returnPoint(cloudStorageEntity.getEndPoint())
                .bucket(cloudStorageEntity.getBucket())
                .region(cloudStorageEntity.getRegion())
                .build();
        LinkedHashMap<String, Integer> linkedHashMap = new LinkedHashMap<>();
        DateTime parse = DateUtil.parse(param.getDates() + "01", DatePattern.PURE_DATE_PATTERN);
        List<DateTime> dateTimes = DateUtil.rangeToList(DateUtil.beginOfMonth(parse), DateUtil.endOfMonth(parse), DateField.DAY_OF_MONTH);
        dateTimes.forEach(date -> linkedHashMap.put(date.toString("yyyy-MM-dd"), 0));
        String prefix = "r/" + one.getStrategyId() + "/" + param.getDeviceEntity().getDeviceCode() + "/" + param.getChannelXId() + "/";
        S3ObjectDto s3ObjectDto = ossUtil.listObjects(build, "/", prefix, null);
        s3ObjectDto.getContents().stream().filter(e -> e.getType() == 1).forEach(e -> {
            if (e.getName().startsWith(param.getDates())) {
                DateTime dateTemp = DateUtil.parse(e.getName(), DatePattern.PURE_DATE_PATTERN);
                linkedHashMap.put(dateTemp.toString("yyyy-MM-dd"), 1);
            }
        });
        return DtoResult.ok(linkedHashMap);
    }

    @Override
    public DtoResult<LinkedHashMap<String, Integer>> getChannelRecordMonthsByLocal(GetChannelRecordMonthsParamVideoNode param) {
        SignalNodeEntity node = signalNodeService.getById(param.getDeviceEntity().getNodeId());
        if (node == null) {
            return DtoResult.error("节点不存在");
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("month", param.getDates());
        String jsonString = jsonObject.toJSONString();
        ConvergeBaseResp baseResp = sunGrpcUtil.signaling(param.getDeviceEntity().getNodeId(), param.getChannelXId(),
                QxNodeCmdType.CONVERGE_GET_RECORD_MONTH.getType(), jsonString);
        JSONObject resp = JSON.parseObject(baseResp.getRsp(), JSONObject.class);
        String days = resp.getString("days");
        LinkedHashMap<String, Integer> linkedHashMap = new LinkedHashMap<>();
        DateTime nowDate = DateTime.now();
        DateTime parse = DateUtil.parse(param.getDates() + "01", DatePattern.PURE_DATE_PATTERN);
        List<DateTime> dateTimes = DateUtil.rangeToList(DateUtil.beginOfMonth(parse), DateUtil.endOfMonth(parse), DateField.DAY_OF_MONTH);
        dateTimes.forEach(date -> {
            if (date.isAfter(nowDate)) {
                linkedHashMap.put(date.toString("yyyy-MM-dd"), 0);
            } else {
                int i = DateUtil.dayOfMonth(date);
                if (i > days.length()) {
                    linkedHashMap.put(date.toString("yyyy-MM-dd"), 0);
                } else {
                    if (days.charAt(i - 1) == '1') {
                        linkedHashMap.put(date.toString("yyyy-MM-dd"), 1);
                    } else {
                        linkedHashMap.put(date.toString("yyyy-MM-dd"), 0);
                    }
                }
            }
        });
        return DtoResult.ok(linkedHashMap);
    }


    @Override
    public DtoResult<VlinkerConvergeRtcConnectResp> getVoiceUrl(GetVoiceUrlParam param) {
        SignalNodeEntity node = signalNodeService.getById(param.getDeviceEntity().getNodeId());
        if (node == null) {
            return DtoResult.error("节点不存在");
        }
        OpenCommonEnum.BaseNetworkingProtocol networkingProtocol;
        try {
            networkingProtocol = OpenCommonEnum.BaseNetworkingProtocol.forNumber(param.getPlayType());
        } catch (Exception e) {
            throw new BizRuntimeException("播放类型错误！");
        }
        OpenStreamMessage.StreamReplyForPublisher streamReply;
        try {
            SunOpenGrpc.SunOpenBlockingStub sunOpenBlockingStub = grpcConfig.getStub(param.getDeviceEntity().getNodeId());
            OpenCommonMessage.RequestAuthInfo authInfo = grpcConfig.getAuthUser();
            OpenStreamMessage.StreamPublishRequest build = OpenStreamMessage.StreamPublishRequest
                    .newBuilder()
                    .setAuthInfo(authInfo)
                    .setNetworking(networkingProtocol)
                    .setValidityPeriodType(OpenCommonEnum.ValidityPeriodType.VALIDITY_PERIOD_TYPE_NORMAL)
                    .setMediaInfo(OpenStreamMessage.StreamMediaInfo
                            .newBuilder()
                            .setAudioFormat(OpenCommonEnum.AudioFormat.AUDIO_FORMAT_G711A)
                            .setVideoFormat(OpenCommonEnum.VideoFormat.VIDEO_FORMAT_NONE)
                            .setAudioChannels(1)
                            .setAudioSampleRate(8000)
                            .setAudioCodecDesc("PCMA 8000 1")
                            .build())
                    .build();
            streamReply = sunOpenBlockingStub.streamPublishVer1(build);
        } catch (Exception e) {
            log.error("开启对讲失败！", e);
            return DtoResult.error("开启对讲失败！");
        }
        if (streamReply.getStatus() != OpenCommonEnum.ReplyStatus.REPLY_STATUS_SUC) {
            return DtoResult.error("开启对讲失败！", streamReply.getDesc());
        }
        try {
            SunOpenGrpc.SunOpenBlockingStub sunOpenBlockingStub = grpcConfig.getStub(param.getDeviceEntity().getNodeId());
            OpenCommonMessage.RequestAuthInfo authInfo = grpcConfig.getAuthUserByXId(param.getChannelXId());
            OpenStreamMessage.InviteDeviceToJoinStreamRequest build = OpenStreamMessage.InviteDeviceToJoinStreamRequest
                    .newBuilder()
                    .setAuthInfo(authInfo)
                    .setStreamToken(streamReply.getStreamToken())
                    .setXid(param.getChannelXId())
                    .build();
            OpenSun.StatefulReply statefulReply = sunOpenBlockingStub.inviteDeviceToJoin(build);
            if (statefulReply.getStatus() != OpenCommonEnum.ReplyStatus.REPLY_STATUS_SUC) {
                return DtoResult.error("开启对讲失败！", statefulReply.getDesc());
            }
        } catch (Exception e) {
            log.error("开启对讲失败！", e);
            return DtoResult.error("开启对讲失败！");
        }
        // 构建并返回GB类型的RTC连接响应
        return DtoResult.ok(VlinkerConvergeRtcConnectResp.builder()
                .type("sd")
                .sdUrl(Base64.getEncoder().encodeToString(streamReply.toByteArray()))
                .build());
    }


    @Override
    public DtoResult<List<ChannelRecordTimeLine>> getChannelRecordTimeLineByCloud(GetChannelRecordTimeLineParamVideoNode param) {
        SignalNodeEntity node = signalNodeService.getById(param.getDeviceEntity().getNodeId());
        if (node == null) {
            return DtoResult.error("节点不存在");
        }
        List<ChannelRecordTimeLine> resList = new ArrayList<>();
        // CLOUD: 云存录像; LOCAL: 卡存录像
        //直接查询s3
        DeviceRecordPlanEntity one = deviceRecordPlanService.getOne(new LambdaQueryWrapper<DeviceRecordPlanEntity>()
                .eq(DeviceRecordPlanEntity::getDeviceId, param.getDeviceId())
                .eq(DeviceRecordPlanEntity::getChannelId, param.getChannelId()), false);
        if (one == null) {
            return DtoResult.error("录像计划不存在！");
        }
        CloudStorageEntity cloudStorageEntity = cloudStorageService.getById(one.getStorageId());
        if (cloudStorageEntity == null) {
            return DtoResult.error("云存储不存在！");
        }
        OSSBean build = OSSBean
                .builder()
                .accessKey(cloudStorageEntity.getKeyId())
                .secretKey(cloudStorageEntity.getSecret())
                .endPoint(cloudStorageEntity.getEndPoint())
                .returnPoint(cloudStorageEntity.getEndPoint())
                .bucket(cloudStorageEntity.getBucket())
                .region(cloudStorageEntity.getRegion())
                .build();
        List<Date> dates = new ArrayList<>();

        // 将时间戳转换为 LocalDate
        LocalDate startDate = Instant.ofEpochMilli(param.getStart()).atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate endDate = Instant.ofEpochMilli(param.getEnd()).atZone(ZoneId.systemDefault()).toLocalDate();
        // 循环遍历，逐日添加日期
        while (!startDate.isAfter(endDate)) {
            // 将 LocalDate 转换回 Date
            Date date = Date.from(startDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
            dates.add(date);
            startDate = startDate.plusDays(1); // 加一天
        }
        if (dates.isEmpty()) {
            return DtoResult.ok(resList);
        }
        AtomicLong lastEndTime = new AtomicLong(0L);  // 记录上一个文件的结束时间
        dates.forEach(date -> {
            String prefix = "r/" + one.getStrategyId() + "/" + param.getDeviceEntity().getDeviceCode()
                    + "/" + param.getChannelXId() + "/" + DateUtil.format(date, DatePattern.PURE_DATE_PATTERN) + "/";
            S3ObjectDto s3ObjectDto = ossUtil.listObjects(build, "/", prefix, null);
            s3ObjectDto.getContents().stream().filter(e -> e.getType() == 2).forEach(e -> {
                // 从文件名中提取开始时间和持续时间
                String[] parts = e.getName().split("_");
                if (parts.length >= 2) {
                    try {
                        // 解析开始时间（秒）和持续时间（秒）
                        long start = Long.parseLong(parts[0]);
                        long duration = Long.parseLong(parts[1].replace(".ps", ""));
                        long end = start + duration;  // 计算文件的结束时间
                        long startMs = start * 1000;
                        long endMs = end * 1000;
                        // 判断文件的时间段是否与查询时间段有交集
                        boolean isOverlapping = (startMs >= param.getStart() && startMs <= param.getEnd()) ||  // 文件开始时间在查询时间段内
                                (endMs >= param.getStart() && endMs <= param.getEnd()) ||    // 文件结束时间在查询时间段内
                                (startMs <= param.getStart() && endMs >= param.getEnd());   // 文件完全覆盖查询时间段

                        // 如果有重叠，加入返回结构
                        if (isOverlapping || (startMs <= lastEndTime.get() && endMs > lastEndTime.get())) {
                            // 创建一个 ChannelRecordTimeLine 对象
                            ChannelRecordTimeLine timeLine = new ChannelRecordTimeLine();
                            timeLine.setStart(startMs);
                            timeLine.setAccessWay(param.getDeviceEntity().getAccessWay());
                            timeLine.setDuration(duration * 1000);  // 转换为毫秒
                            String idxName = start + ".idx";
                            if (s3ObjectDto.getContents()
                                    .stream()
                                    .anyMatch(fileTemp -> fileTemp.getType() == 2 && fileTemp.getName().equals(idxName))) {

                                // 更新上一个文件的结束时间
                                lastEndTime.set(Math.max(lastEndTime.get(), endMs));

                                // 将该对象加入列表
                                resList.add(timeLine);
                            }

                        }
                    } catch (NumberFormatException ignored) {
                    }
                }
            });

        });
        return DtoResult.ok(resList);
    }

    @Override
    public DtoResult<List<ChannelRecordTimeLine>> getChannelRecordTimeLineByLocal(GetChannelRecordTimeLineParamVideoNode param) {

        SignalNodeEntity node = signalNodeService.getById(param.getDeviceEntity().getNodeId());
        if (node == null) {
            return DtoResult.error("节点不存在");
        }
        List<ChannelRecordTimeLine> resList = new ArrayList<>();
        String jsonString = JSONObject.toJSONString(ConvergeChannelRecordTimeLineReq.builder()
                .startTime(DateUtil.format(new Date(param.getStart()), DatePattern.NORM_DATETIME_MS_PATTERN))
                .endTime(DateUtil.format(new Date(param.getEnd()), DatePattern.NORM_DATETIME_MS_PATTERN))
                .build());
        try {
            ConvergeBaseResp baseResp = sunGrpcUtil.signaling(param.getDeviceEntity().getNodeId(), param.getDeviceEntity().getNodeXId(),
                    QxNodeCmdType.CONVERGE_GET_CHANNEL_RECORD_TIME_LINE.getType(), jsonString);
            if (StringUtil.isBlank(baseResp.getRsp())) {
                return DtoResult.error("当前时间段无录像！");
            }
            List<ConvergeChannelRecordTimeLineResp> nodeResp = JSON.parseArray(baseResp.getRsp(), ConvergeChannelRecordTimeLineResp.class);

            if (!nodeResp.isEmpty()) {
                for (ConvergeChannelRecordTimeLineResp resp : nodeResp) {
                    ChannelRecordTimeLine timeLine = new ChannelRecordTimeLine();
                    Date startDate = DateUtil.parse(resp.getStartTime(), DatePattern.NORM_DATETIME_PATTERN);
                    Date endDate = DateUtil.parse(resp.getEndTime(), DatePattern.NORM_DATETIME_PATTERN);
                    timeLine.setAccessWay(param.getDeviceEntity().getAccessWay());
                    timeLine.setStart(startDate.getTime());
                    timeLine.setDuration(endDate.getTime() - startDate.getTime());  // 转换为毫秒
                    resList.add(timeLine);
                }
            }
            return DtoResult.ok(resList);
        } catch (Exception e) {
            log.error("查询录像轴失败！", e);
            return DtoResult.error("查询录像轴失败！", e.getMessage());
        }
    }

    @Override
    public DtoResult<ChannelRecordUrlResp> getChannelRecordUrlByCloud(GetChannelRecordTimeLineParamVideoNode param) {
        SignalNodeEntity node = signalNodeService.getById(param.getDeviceEntity().getNodeId());
        if (node == null) {
            return DtoResult.error("节点不存在");
        }
        ChannelRecordUrlResp resp = new ChannelRecordUrlResp();
        resp.setAccessWay(param.getDeviceEntity().getAccessWay());
        List<OpenSunMessage.RecordsFileQueryReply.RecordsInfo> recordsInfoList = new ArrayList<>();
        // CLOUD: 云存录像; LOCAL: 卡存录像
        //直接查询s3
        DeviceRecordPlanEntity one = deviceRecordPlanService.getOne(new LambdaQueryWrapper<DeviceRecordPlanEntity>()
                .eq(DeviceRecordPlanEntity::getDeviceId, param.getDeviceId())
                .eq(DeviceRecordPlanEntity::getChannelId, param.getChannelId()), false);
        if (one == null) {
            return DtoResult.error("录像计划不存在！");
        }
        CloudStorageEntity cloudStorageEntity = cloudStorageService.getById(one.getStorageId());
        if (cloudStorageEntity == null) {
            return DtoResult.error("云存储不存在！");
        }
        OSSBean build = OSSBean
                .builder()
                .accessKey(cloudStorageEntity.getKeyId())
                .secretKey(cloudStorageEntity.getSecret())
                .endPoint(cloudStorageEntity.getEndPoint())
                .returnPoint(cloudStorageEntity.getEndPoint())
                .bucket(cloudStorageEntity.getBucket())
                .region(cloudStorageEntity.getRegion())
                .build();
        List<Date> dates = new ArrayList<>();
        // 将时间戳转换为 LocalDate
        LocalDate startDate = Instant.ofEpochMilli(param.getStart()).atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate endDate = Instant.ofEpochMilli(param.getEnd()).atZone(ZoneId.systemDefault()).toLocalDate();
        // 循环遍历，逐日添加日期
        while (!startDate.isAfter(endDate)) {
            // 将 LocalDate 转换回 Date
            Date date = Date.from(startDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
            dates.add(date);
            startDate = startDate.plusDays(1); // 加一天
        }
        if (dates.isEmpty()) {
            log.error("无录像 start:{},end:{}", param.getStart(), param.getEnd());
            return DtoResult.error("无录像", "没有录像文件");
        }
        AtomicLong lastEndTime = new AtomicLong(0L);  // 记录上一个文件的结束时间
        dates.forEach(date -> {
            String prefix = "r/" + one.getStrategyId() + "/" + param.getDeviceEntity().getDeviceCode()
                    + "/" + param.getChannelXId() + "/" + DateUtil.format(date, DatePattern.PURE_DATE_PATTERN) + "/";
            S3ObjectDto s3ObjectDto = ossUtil.listObjects(build, "/", prefix, null);
            s3ObjectDto.getContents().stream().filter(e -> e.getType() == 2).forEach(e -> {
                // 从文件名中提取开始时间和持续时间
                String[] parts = e.getName().split("_");
                if (parts.length >= 2) {
                    try {
                        // 解析开始时间（秒）和持续时间（秒）
                        long start = Long.parseLong(parts[0]);
                        long duration = Long.parseLong(parts[1].replace(".ps", ""));
                        long end = start + duration;  // 计算文件的结束时间
                        long startMs = start * 1000;
                        long endMs = end * 1000;
                        // 判断文件的时间段是否与查询时间段有交集
                        boolean isOverlapping = (startMs >= param.getStart() && startMs <= param.getEnd()) ||  // 文件开始时间在查询时间段内
                                (endMs >= param.getStart() && endMs <= param.getEnd()) ||    // 文件结束时间在查询时间段内
                                (startMs <= param.getStart() && endMs >= param.getEnd());   // 文件完全覆盖查询时间段
                        // 如果有重叠，加入返回结构
                        if (isOverlapping || (startMs <= lastEndTime.get() && endMs > lastEndTime.get())) {
                            // 创建一个 ChannelRecordTimeLine 对象
                            String idxName = start + ".idx";
                            boolean haxIdx = s3ObjectDto.getContents()
                                    .stream()
                                    .anyMatch(fileTemp -> fileTemp.getType() == 2 && fileTemp.getName().equals(idxName));
                            if (haxIdx) {
                                // 更新上一个文件的结束时间
                                lastEndTime.set(Math.max(lastEndTime.get(), endMs));
                                String psFileUrl = ossUtil.downloadUrl(build, e.getKey());
                                // 构建 RecordsInfo 对象
                                OpenSunMessage.RecordsFileQueryReply.RecordsInfo.Builder builder =
                                        OpenSunMessage.RecordsFileQueryReply.RecordsInfo.newBuilder()
                                                .setStartTime(start)
                                                .setEndTime(end)
                                                .setPsFileUrl(psFileUrl);
                                String idxOssKey = prefix + idxName;
                                String idxFileUrl = ossUtil.downloadUrl(build, idxOssKey);
                                builder.setIdxFileUrl(idxFileUrl);
                                OpenSunMessage.RecordsFileQueryReply.RecordsInfo recordsInfo =
                                        builder.build();
                                recordsInfoList.add(recordsInfo);
                            }
                        }
                    } catch (NumberFormatException ignored) {
                    }
                }
            });
        });
        if (recordsInfoList.isEmpty()) {
            log.info("所选时间段内没录像,startDate:{},endDate:{},dates:{}", param.getStart(), param.getEnd(), dates);
            return DtoResult.error("无录像", "所选时间段内没录像");
        }
        OpenSunMessage.RecordsFileQueryReply reply = OpenSunMessage.RecordsFileQueryReply
                .newBuilder()
                .addAllInfos(recordsInfoList)
                .build();
        byte[] byteArray = reply.toByteArray();
        resp.setUrl(Base64.getEncoder().encodeToString(byteArray));
        return DtoResult.ok(resp);
    }

    @Override
    public DtoResult<ChannelRecordUrlResp> getChannelRecordUrlByLocal(GetChannelRecordTimeLineParamVideoNode param) {
        SignalNodeEntity node = signalNodeService.getById(param.getDeviceEntity().getNodeId());
        if (node == null) {
            return DtoResult.error("节点不存在");
        }
        ChannelRecordUrlResp resp = new ChannelRecordUrlResp();
        resp.setAccessWay(param.getDeviceEntity().getAccessWay());
        // CLOUD: 云存录像; LOCAL: 卡存录像
        if (param.getNetworking() == null) {
            return DtoResult.error("请指定播放方式");
        }
        OpenStreamMessage.StreamReplyForPlayer streamReply;
        try {
            log.info("获取回放流 入参：{}", JSON.toJSONString(param));
            SunOpenGrpc.SunOpenBlockingStub sunOpenBlockingStub = grpcConfig.getStub(param.getDeviceEntity().getNodeId());
            OpenCommonMessage.RequestAuthInfo authInfo = grpcConfig.getAuthUserByXIdAndUserPlatformType(param.getChannelXId());
            OpenStreamMessage.StreamPlayRequest build = OpenStreamMessage.StreamPlayRequest
                    .newBuilder()
                    .setPlayType(OpenCommonEnum.PlayType.PLAY_TYPE_PLAYBACK_MAIN)
                    .setNetworking(OpenCommonEnum.BaseNetworkingProtocol.forNumber(param.getNetworking()))
                    .setPlaybackBeginTimestamp(param.getStart())
                    .setPlaybackEndTimestamp(param.getEnd())
                    .setXId(param.getChannelXId())
                    .setAuthInfo(authInfo)
                    .setValidityPeriodType(OpenCommonEnum.ValidityPeriodType.VALIDITY_PERIOD_TYPE_NORMAL)
                    .build();
            streamReply = sunOpenBlockingStub.streamPlayVer1(build);
        } catch (Exception e) {
            log.error("获取回放流失败！", e);
            return DtoResult.error("获取回放流失败！");
        }
        if (streamReply.getStatus() != OpenCommonEnum.ReplyStatus.REPLY_STATUS_SUC) {
            return DtoResult.error("获取回放流失败！", streamReply.getDesc());
        }
        byte[] byteArray = streamReply.toByteArray();
        resp.setUrl(Base64.getEncoder().encodeToString(byteArray));
        return DtoResult.ok(resp);
    }


    @Override
    public DtoResult<Void> controlPlayback(ControlPlaybackParamVideoNode param) {
        SignalNodeEntity node = signalNodeService.getById(param.getDeviceEntity().getNodeId());
        if (node == null) {
            return DtoResult.error("节点不存在");
        }
        String jsonString = JSONObject.toJSONString(ConvergeControlPlaybackReq.builder()
                .startTime(DateUtil.format(new Date(param.getStartTime()), "yyyyMMddHHmmss"))
                .endTime(DateUtil.format(new Date(param.getEndTime()), "yyyyMMddHHmmss"))
                .speed(param.getSpeed())
                .build());
        sunGrpcUtil.signaling(param.getDeviceEntity().getNodeId(), param.getDeviceEntity().getNodeXId(),
                QxNodeCmdType.CONVERGE_LOCAL_VIDEO_PLAYBACK.getType(), jsonString);
        return DtoResult.ok();
    }

    @Override
    public DtoResult<DownloadLocalPlaybackVo> downloadPlayback(DownloadPlaybackParamVideoNode param) {
        return DtoResult.error("不支持下载！");
    }


    @Override
    public DtoResult<Void> stopPlayback(VideoNodeBaseParam param, CommonStopPlayBackReq playBackReq) {
        return DtoResult.ok();
    }

    @Resource
    private OpsDeviceScreenInfoService opsDeviceScreenInfoService;

    @Override
    public DtoResult<CommonCallDeviceByVideoResp> callDeviceByVideo(VideoNodeBaseParam videoNodeBaseParam, CommonCallDeviceByVideoReq req) {
        SignalNodeEntity node = signalNodeService.getById(videoNodeBaseParam.getDeviceEntity().getNodeId());
        if (node == null) {
            return DtoResult.error("节点不存在");
        }
        OpenCommonEnum.AudioFormat audioFormat;
        OpenCommonEnum.VideoFormat videoFormat;
        OpenCommonEnum.BaseNetworkingProtocol networkingProtocol = OpenCommonEnum.BaseNetworkingProtocol.forNumber(req.getNetworkingProtocol());
        if (networkingProtocol == null) {
            return DtoResult.error("播放类型错误！");
        }
        OpenStreamMessage.StreamReplyForPublisher streamReply;
        try {
            OpenCommonMessage.RequestAuthInfo authInfo = grpcConfig.getAuthUser();
            SunOpenGrpc.SunOpenBlockingStub sunOpenBlockingStub = grpcConfig.getStub(videoNodeBaseParam.getDeviceEntity().getNodeId());
            OpsDeviceScreenInfoEntity opsDeviceScreenInfoEntity = opsDeviceScreenInfoService.getById(videoNodeBaseParam.getDeviceEntity().getId());
            if (opsDeviceScreenInfoEntity == null){
                return DtoResult.error("读取设备支持的视频格式不存在");
            }
            audioFormat = OpenCommonEnum.AudioFormat.AUDIO_FORMAT_G711A;
            if (opsDeviceScreenInfoEntity.getUiVideoEncAbility() == 0x02) {
                videoFormat = OpenCommonEnum.VideoFormat.VIDEO_FORMAT_AVC;
            } else if (opsDeviceScreenInfoEntity.getUiVideoEncAbility() == 0x04) {
                videoFormat = OpenCommonEnum.VideoFormat.VIDEO_FORMAT_HEVC;
            } else if (opsDeviceScreenInfoEntity.getUiVideoEncAbility() == (0x02 | 0x04)) {
                videoFormat = OpenCommonEnum.VideoFormat.VIDEO_FORMAT_AVC;
            } else {
                log.info("读取设备支持的视频格式异常 {}", opsDeviceScreenInfoEntity.getUiVideoEncAbility());
                return DtoResult.error("发起视频通话失败！", "读取设备支持的视频格式异常");
            }
            OpenStreamMessage.StreamPublishRequest build = OpenStreamMessage.StreamPublishRequest
                    .newBuilder()
                    .setAuthInfo(authInfo)
                    .setNetworking(networkingProtocol)
                    .setValidityPeriodType(OpenCommonEnum.ValidityPeriodType.VALIDITY_PERIOD_TYPE_NORMAL)
                    .setMediaInfo(OpenStreamMessage.StreamMediaInfo
                            .newBuilder()
                            .setAudioFormat(audioFormat)
                            .setVideoFormat(videoFormat)
                            .setAudioChannels(1)
                            .setAudioSampleRate(8000)
                            .setAudioCodecDesc("PCMA 8000 1")
                            .build())
                    .build();
            streamReply = sunOpenBlockingStub.streamPublishVer1(build);
            log.info("streamPublishVer1：statusValue{} desc:{}", streamReply.getStatusValue(), streamReply.getDesc());
        } catch (Exception e) {
            log.error("创建视频房间失败！", e);
            return DtoResult.error("创建视频房间失败！");
        }
        if (streamReply.getStatus() != OpenCommonEnum.ReplyStatus.REPLY_STATUS_SUC) {
            return DtoResult.error("创建视频房间失败！", streamReply.getDesc());
        }
        try {
            SunOpenGrpc.SunOpenBlockingStub sunOpenBlockingStub = grpcConfig.getStub(videoNodeBaseParam.getDeviceEntity().getNodeId());
            OpenCommonMessage.RequestAuthInfo authInfo = grpcConfig.getAuthUserByXId(videoNodeBaseParam.getChannelXId());
            OpenStreamMessage.InviteDeviceToJoinStreamRequest build = OpenStreamMessage.InviteDeviceToJoinStreamRequest
                    .newBuilder()
                    .setAuthInfo(authInfo)
                    .setStreamToken(streamReply.getStreamToken())
                    .setXid(videoNodeBaseParam.getChannelXId())
                    .build();
            OpenSun.StatefulReply statefulReply = sunOpenBlockingStub.inviteDeviceToJoin(build);
            log.info("inviteDeviceToJoin：statusValue{} desc:{}", statefulReply.getStatusValue(), statefulReply.getDesc());
            if (statefulReply.getStatus() != OpenCommonEnum.ReplyStatus.REPLY_STATUS_SUC) {
                log.error("发起视频通话失败！ res:{}，status:{},desc:{}", statefulReply.getResult(), statefulReply.getStatus(), statefulReply.getDesc());
                return DtoResult.error("发起视频通话失败！", statefulReply.getDesc());
            }
        } catch (Exception e) {
            log.error("发起视频通话失败！", e);
            return DtoResult.error("发起视频通话失败！");
        }
        CommonCallDeviceByVideoResp callDeviceByVideoResp = new CommonCallDeviceByVideoResp();
        byte[] byteArray = streamReply.toByteArray();
        String callData = Base64.getEncoder().encodeToString(byteArray);
        log.info("statusValue :{} byteArray :{} callData: {}", streamReply.getStatusValue(), byteArray.length, callData);
        callDeviceByVideoResp.setCallData(callData);
        callDeviceByVideoResp.setVideoFormat(videoFormat.getNumber());
        callDeviceByVideoResp.setAudioFormat(audioFormat.getNumber());
        // 构建并返回GB类型的RTC连接响应
        return DtoResult.ok(callDeviceByVideoResp);
    }

}
