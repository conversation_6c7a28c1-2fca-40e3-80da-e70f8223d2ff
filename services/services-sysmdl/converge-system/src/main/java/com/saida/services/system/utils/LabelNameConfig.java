package com.saida.services.system.utils;

import com.alibaba.fastjson.JSON;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Data
@RefreshScope
@Component
@ConfigurationProperties(prefix = "alg-saida-label-name")
public class LabelNameConfig {

    private Map<Integer, String> labels = new HashMap<>();

    // 静态变量保存实例
    private static LabelNameConfig instance;

    @PostConstruct
    public void init() {
        instance = this;
        log.info("LabelNameConfig init {}", JSON.toJSONString(labels));
    }

    public static Map<Integer, String> getAllLabels() {
        return instance.labels;
    }
}

