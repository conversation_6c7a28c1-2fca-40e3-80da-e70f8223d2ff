package com.saida.services.system.callback;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.saida.services.algorithm.dto.BaseAlarmDto;
import com.saida.services.algorithm.dto.PushAlarmDto;
import com.saida.services.algorithm.entity.ThirdPartyEntity;
import com.saida.services.common.base.DtoResult;
import com.saida.services.common.config.ThreadPoolConfig;
import com.saida.services.feign.open.system.IFeignOpenSystemApiController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
@Async(ThreadPoolConfig.TASK_EXECUTOR_BEAN_NAME)
public class CallBackFeign {

    @Lazy
    @Resource
    private IFeignOpenSystemApiController iFeignOpenSystemApiController;


    public void feignPush(PushAlarmDto pushAlarmDto) {
        try {
            DtoResult<Void> dtoResult = iFeignOpenSystemApiController.acceptDataFromVlinkAlgorithm(pushAlarmDto);
            if (dtoResult.success()) {
                log.info("V-LINKER算法中台.通过Feign向能开推送告警成功");
            } else {
                log.info("V-LINKER算法中台.通过Feign向能开推送告警失败..., msg={}", dtoResult.getMessage());
            }
        } catch (Exception e) {
            log.error("V-LINKER算法中台.通过Feign向能开推送告警..错误..., msg={}", e.getMessage(), e);
        } finally {
            log.info("V-LINKER算法中台.通过Feign向能开推送告警..结束...");
        }
    }

    public void httpPush(ThirdPartyEntity thirdPartyEntity, BaseAlarmDto baseAlarmDto) {
        String resp = "";
        try {
            HttpResponse httpResponse = HttpRequest.post(thirdPartyEntity.getPushUrl())
                    .header("appKey", thirdPartyEntity.getAccount())
                    .body(JSON.toJSONString(baseAlarmDto))
                    .execute();
            resp = httpResponse.body();
            if (httpResponse.getStatus() == 200) {
                log.info("V-LINKER算法中台.向第三方推送告警成功...resp={}", resp);
            } else {
                throw new RuntimeException("httpPushByCount 推送失败");
            }
        } catch (Exception e) {
            log.error("V-LINKER算法中台.向第三方推送告警错误...resp={}, msg={}", resp, e.getMessage(), e);
        } finally {
            log.info("V-LINKER算法中台.向第三方推送告警结束...resp={}", resp);
        }
    }

    /**
     * 只是补充告警图片
     */
//    public void feignPushOnlyImg(CallBackMessage message) {
//        // 内部告警推送
//        try {
//            PushAlarmDto pushAlarmDto = new PushAlarmDto();
//            pushAlarmDto.setMsgType(2);
//            pushAlarmDto.setMsgReqNo(message.getMsgId());
//            pushAlarmDto.setDeviceCode(message.getDeviceCode());
//            pushAlarmDto.setChannelId(message.getChannelId());
//            pushAlarmDto.setSrcUrl(message.getSrcUrl());
//            pushAlarmDto.setAlertType(message.getAlertType());
//
//            DtoResult<Void> dtoResult = iFeignOpenSystemApiController.acceptDataFromVlinkAlgorithm(pushAlarmDto);
//            if (dtoResult.success()) {
//                log.info("V-LINKER算法中台.通过Feign向能开推送告警图片成功");
//            } else {
//                log.info("V-LINKER算法中台.通过Feign向能开推送告警图片失败..., msg={}", dtoResult.getMessage());
//            }
//        } catch (Exception e) {
//            log.error("V-LINKER算法中台.通过Feign向能开推送告警图片..错误..., msg={}", e.getMessage(), e);
//        } finally {
//            log.info("V-LINKER算法中台.通过Feign向能开推送告警图片..结束...");
//        }
//    }
}