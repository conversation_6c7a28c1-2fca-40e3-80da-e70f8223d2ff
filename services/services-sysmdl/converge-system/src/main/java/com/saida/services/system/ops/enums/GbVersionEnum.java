package com.saida.services.system.ops.enums;

import com.saida.services.converge.enums.DeviceAlarmMethodTypeEnums;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @ClassName GbVersionEnum
 * @Desc
 * @Date 2024/10/22 09:21
 */
@Getter
@AllArgsConstructor
public enum GbVersionEnum {
    ELEVEN("2011", "GB/T 28181-2011"),
    SIXTEEN("2016", "GB/T 28181-2016"),
    TWENTY_TWO("2022", "GB/T 28181-2022");

    /*
     * 枚举code码
     */
    private final String code;

    /*
     * 枚举描述
     */
    private final String des;

    public static String getNameByType(String type) {
        for (GbVersionEnum ele : values()) {
            if (ele.getCode().equals(type)) {
                return ele.getDes();
            }
        }
        return "";
    }
}
