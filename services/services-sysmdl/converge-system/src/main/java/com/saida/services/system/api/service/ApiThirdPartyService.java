package com.saida.services.system.api.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.saida.services.algorithm.dto.DeviceInfoDto;
import com.saida.services.algorithm.entity.AlgorithmManageEntity;
import com.saida.services.algorithm.entity.ThirdPartyDeviceEntity;
import com.saida.services.algorithm.entity.ThirdPartyEntity;
import com.saida.services.common.base.Result;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.exception.BizRuntimeException;
import com.saida.services.system.algorithm.service.AlgorithmManageService;
import com.saida.services.system.api.pojo.SubResult;
import com.saida.services.system.basicData.entity.BasicGroupEntity;
import com.saida.services.system.basicData.service.BasicGroupService;
import com.saida.services.system.device.entity.CameraEntity;
import com.saida.services.system.device.service.CameraService;
import com.saida.services.system.sys.dto.ThirdpartyDeviceReq;
import com.saida.services.system.sys.service.ThirdPartyDeviceService;
import com.saida.services.system.sys.service.ThirdPartyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ApiThirdPartyService {

    @Resource
    private ThirdPartyService thirdPartyService;
    @Resource
    private ThirdPartyDeviceService thirdPartyDeviceService;
    @Resource
    private CameraService cameraService;
    @Resource
    private AlgorithmManageService algorithmManageService;
    @Resource
    private BasicGroupService basicGroupService;

    public Result save(ThirdPartyEntity entity) {
        if (StringUtil.isEmpty(entity.getAccount())) {
            return Result.error("三方平台账号必填");
        }
        ThirdPartyEntity thirdParty = thirdPartyService.getOne(new LambdaQueryWrapper<ThirdPartyEntity>()
                .eq(ThirdPartyEntity::getAccount, entity.getAccount()), false);
        if (thirdParty == null) {
            entity.setCreateTime(DateTime.now());
            thirdPartyService.save(entity);
            //新增的应用初始化一个默认的人脸库分组
            BasicGroupEntity basicGroup = new BasicGroupEntity();
            basicGroup.setAppId(entity.getAccount());
            basicGroup.setName("陌生人分组");
            basicGroup.setType(0);
            basicGroup.setCreateTime(LocalDateTime.now());
            basicGroupService.save(basicGroup);
        } else {
            entity.setId(thirdParty.getId());
            entity.setUpdateTime(DateTime.now());
            thirdPartyService.updateById(entity);
        }
        return Result.ok();
    }

    /**
     * 订阅
     */
    @Transactional(rollbackFor = Exception.class)
    public Result subscribe(ThirdpartyDeviceReq req) {
        log.info("V-LINKER算法平台.算法订阅...req={}", JSON.toJSON(req));
        try {
            if (StringUtil.isEmpty(req.getThirdAppKey())) {
                log.error("应用ID不能为空");
                return Result.error("应用ID不能为空");
            }
            if (StringUtil.isEmpty(req.getSubscribe())) {
                log.error("未选择告警算法");
                return Result.error("未选择告警算法");
            }
            ThirdPartyEntity thirdPartyEntity = thirdPartyService.getOne(new LambdaQueryWrapper<ThirdPartyEntity>()
                    .eq(ThirdPartyEntity::getAccount, req.getThirdAppKey()), false);
            if (thirdPartyEntity == null) {
                return Result.error("应用不存在");
            }

            List<DeviceInfoDto> deviceInfoDtoList = req.getDeviceInfoDtoList();
            List<CameraEntity> cameraEntityList = cameraService.getCameraListByCodeAndChannelId(deviceInfoDtoList);
            if (CollectionUtil.isEmpty(cameraEntityList)) {
                return Result.error("设备不存在");
            }
            List<ThirdPartyDeviceEntity> thirdPartyDeviceEntityList = thirdPartyDeviceService
                    .list(new LambdaQueryWrapper<ThirdPartyDeviceEntity>()
                            .eq(ThirdPartyDeviceEntity::getThirdId, thirdPartyEntity.getId()));
            if (CollectionUtil.isEmpty(thirdPartyDeviceEntityList)) {
                return Result.error("应用未绑定设备");
            }
            for (CameraEntity cameraEntity : cameraEntityList) {
                Optional<ThirdPartyDeviceEntity> optional = thirdPartyDeviceEntityList.stream().filter(t1 -> Objects.equals(t1.getCameraId(), cameraEntity.getId())).findFirst();
                if (optional.isPresent()) {
                    ThirdPartyDeviceEntity thirdPartyDeviceEntity = optional.get();
                    List<SubResult> subResults = new ArrayList<>();
                    List<String> subscribeAlgIdList = Arrays.asList(req.getSubscribe().split(","));
                    List<AlgorithmManageEntity> algorithmManageEntityList = algorithmManageService.list(new LambdaQueryWrapper<AlgorithmManageEntity>()
                            .eq(AlgorithmManageEntity::getStatus, 1)
                    );
                    if (CollectionUtil.isEmpty(algorithmManageEntityList)) {
                        subscribeAlgIdList.forEach(subscribeAlgId -> {
                            subResults.add(new SubResult(subscribeAlgId, "0"));
                        });
                        return Result.ok(subResults);
                    }
                    subscribeAlgIdList.forEach(subscribeAlgId -> {
                        if (algorithmManageEntityList.stream().anyMatch(t1 -> Objects.equals(String.valueOf(t1.getId()), subscribeAlgId))) {
                            subResults.add(new SubResult(subscribeAlgId, "1"));
                        } else {
                            subResults.add(new SubResult(subscribeAlgId, "0"));
                        }
                    });
                    String alertTypes = thirdPartyDeviceEntity.getSubscribe();
                    if (StringUtil.isEmpty(alertTypes)) {
                        thirdPartyDeviceEntity.setSubscribe(subResults.stream().filter(o -> "1".equals(o.getResult())).map(SubResult::getAlgId).distinct().collect(Collectors.joining(",")));
                    } else {
                        Set<String> alertTypeSet = Sets.newHashSet(alertTypes.split(","));
                        alertTypeSet.addAll(subResults.stream().filter(o -> "1".equals(o.getResult())).map(SubResult::getAlgId).collect(Collectors.toSet()));
                        thirdPartyDeviceEntity.setSubscribe(String.join(",", alertTypeSet));
                    }
                    thirdPartyDeviceService.updateById(thirdPartyDeviceEntity);
                }
            }
            thirdPartyEntity.setPushUrl(req.getPushUrl());
            thirdPartyService.updateById(thirdPartyEntity);
            return Result.ok();
        } catch (Exception e) {
            log.error("V-LINKER算法中台.告警订阅错误...msg={}", e.getMessage(), e);
            throw new BizRuntimeException(e.getMessage());
        }
    }

    /**
     * 退订
     */
    @Transactional(rollbackFor = Exception.class)
    public Result unsubscribe(ThirdpartyDeviceReq req) {
        log.info("V-LINKER算法平台.算法退订...req={}", JSON.toJSON(req));
        try {
            if (StringUtil.isEmpty(req.getThirdAppKey())) {
                return Result.error("应用不存在");
            }
            if (StringUtil.isEmpty(req.getSubscribe())) {
                return Result.error("未选择告警算法");
            }
            ThirdPartyEntity thirdPartyEntity = thirdPartyService.getOne(new LambdaQueryWrapper<ThirdPartyEntity>()
                    .eq(ThirdPartyEntity::getAccount, req.getThirdAppKey()), false);
            if (thirdPartyEntity == null) {
                return Result.error("应用不存在");
            }
            List<DeviceInfoDto> deviceInfoDtoList = req.getDeviceInfoDtoList();
            List<CameraEntity> cameraEntityList = cameraService.getCameraListByCodeAndChannelId(deviceInfoDtoList);
            if (CollectionUtil.isEmpty(cameraEntityList)) {
                return Result.error("设备为空");
            }
            List<ThirdPartyDeviceEntity> thirdPartyDeviceEntityList = thirdPartyDeviceService.list(new LambdaQueryWrapper<ThirdPartyDeviceEntity>()
                    .eq(ThirdPartyDeviceEntity::getThirdId, thirdPartyEntity.getId()));
            if (CollectionUtil.isEmpty(thirdPartyDeviceEntityList)) {
                return Result.error("应用未绑定设备");
            }

            List<ThirdPartyDeviceEntity> updateThirdPartyDeviceEntityList = new ArrayList<>();
            for (ThirdPartyDeviceEntity thirdPartyDeviceEntity : thirdPartyDeviceEntityList) {
                for (CameraEntity cameraEntity : cameraEntityList) {
                    if (Objects.equals(cameraEntity.getId(), thirdPartyDeviceEntity.getCameraId())) {
                        String subscribe = thirdPartyDeviceEntity.getSubscribe();
                        Set<String> subscribeAlgIdSet = (StringUtil.isEmpty(subscribe)) ? new HashSet<>() : Sets.newHashSet(subscribe.split(","));
                        Set<String> unsubscribeAlgIdSet = Sets.newHashSet(req.getSubscribe().split(","));
                        subscribeAlgIdSet.removeAll(unsubscribeAlgIdSet);

                        if (CollectionUtil.isEmpty(subscribeAlgIdSet)) {
                            thirdPartyDeviceService.update(new LambdaUpdateWrapper<ThirdPartyDeviceEntity>()
                                    .set(ThirdPartyDeviceEntity::getSubscribe, null)
                                    .eq(ThirdPartyDeviceEntity::getId, thirdPartyDeviceEntity.getId()));
                        } else {
                            ThirdPartyDeviceEntity updateThirdPartyDeviceEntity = new ThirdPartyDeviceEntity();
                            updateThirdPartyDeviceEntity.setId(thirdPartyDeviceEntity.getId());
                            updateThirdPartyDeviceEntity.setSubscribe(String.join(",", subscribeAlgIdSet));
                            updateThirdPartyDeviceEntityList.add(updateThirdPartyDeviceEntity);
                        }
                    }
                }
            }
            if (CollectionUtil.isNotEmpty(updateThirdPartyDeviceEntityList)) {
                thirdPartyDeviceService.updateBatchById(updateThirdPartyDeviceEntityList);
            }
            return Result.ok();
        } catch (Exception e) {
            log.error("退订算法错误...msg={}", e.getMessage(), e);
            throw new BizRuntimeException(e.getMessage());
        }
    }

    /**
     * 三方平台设备绑定
     */
    public Result deviceBind(ThirdpartyDeviceReq req) {
        String deviceCodes = req.getDeviceCodes();
        if (StringUtil.isEmpty(req.getThirdAppKey())) {
            return Result.error("未选择三方平台");
        }
        if (StringUtil.isEmpty(deviceCodes)) {
            return Result.error("未选择设备");
        }
        List<DeviceInfoDto> deviceInfoDtoList = Lists.newArrayList(deviceCodes.split(",")).stream().map(t1 -> {
            DeviceInfoDto deviceInfoDto = new DeviceInfoDto();
            if (StringUtil.isNotEmpty(t1)) {
                String[] str = t1.split("_");
                deviceInfoDto.setDeviceCode(str[0]);
                if (str.length == 2) {
                    deviceInfoDto.setChannelId(str[1]);
                }
            }
            return deviceInfoDto;
        }).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(deviceInfoDtoList)) {
            return Result.error("未选择设备");
        }
        ThirdPartyEntity thirdPartyEntity = thirdPartyService.getOne(new LambdaQueryWrapper<ThirdPartyEntity>()
                .eq(ThirdPartyEntity::getAccount, req.getThirdAppKey()), false);
        if (thirdPartyEntity == null) {
            return Result.error("平台不存在");
        }
        List<CameraEntity> cameraEntityList = cameraService.getCameraListByCodeAndChannelId(deviceInfoDtoList);
        if (cameraEntityList == null || cameraEntityList.isEmpty()) {
            return Result.error("设备不存在");
        }
        thirdPartyService.deviceBind(thirdPartyEntity.getId(), cameraEntityList.stream().map(CameraEntity::getId).distinct().collect(Collectors.toList()));
        return Result.ok();
    }

    /**
     * 三方平台设备解绑
     */
    public Result deviceUnbind(ThirdpartyDeviceReq req) {
        String deviceCodes = req.getDeviceCodes();
        if (StringUtil.isEmpty(req.getThirdAppKey())) {
            return Result.error("未选择三方平台");
        }
        if (StringUtil.isEmpty(deviceCodes)) {
            return Result.error("未选择设备");
        }
        List<DeviceInfoDto> deviceInfoDtoList = Lists.newArrayList(deviceCodes.split(",")).stream().map(t1 -> {
            DeviceInfoDto deviceInfoDto = new DeviceInfoDto();
            if (StringUtil.isNotEmpty(t1)) {
                String[] str = t1.split("_");
                deviceInfoDto.setDeviceCode(str[0]);
                if (str.length == 2) {
                    deviceInfoDto.setChannelId(str[1]);
                }
            }
            return deviceInfoDto;
        }).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(deviceInfoDtoList)) {
            return Result.error("未选择设备");
        }
        ThirdPartyEntity third = thirdPartyService.getOne(new LambdaQueryWrapper<ThirdPartyEntity>().eq(ThirdPartyEntity::getAccount, req.getThirdAppKey()), false);
        if (third == null) {
            return Result.error("平台不存在");
        }
        List<CameraEntity> cameraEntityList = cameraService.getCameraListByCodeAndChannelId(deviceInfoDtoList);
        if (cameraEntityList == null || cameraEntityList.isEmpty()) {
            return Result.error("设备不存在");
        }
        thirdPartyService.deviceUnbind(third.getId(), cameraEntityList.stream().map(CameraEntity::getId).distinct().collect(Collectors.toList()));
        return Result.ok();
    }
}
