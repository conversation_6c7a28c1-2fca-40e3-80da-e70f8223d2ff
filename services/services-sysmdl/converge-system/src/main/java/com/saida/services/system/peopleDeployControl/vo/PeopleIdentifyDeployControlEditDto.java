package com.saida.services.system.peopleDeployControl.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * description your class purpose
 *
 * <AUTHOR>
 * @version PeopleIdentifyDeployControlPageQryDto v1.0.0
 * @since 2025/6/17 9:50
 */
@Data
public class PeopleIdentifyDeployControlEditDto {
    /**
     * id
     */
    @NotNull(message = "id不能为空")
    private Long id;
    /**
     * 任务名称
     */
    @NotBlank(message = "任务名称不能为空")
    private String name;
    /**
     * 运行状态 0：未启用 1：运行中
     */
    private Integer status;
    /**
     * 时间计划id
     */
    private Long timeTemplateId;
    /**
     * 人脸相似度
     */
    @NotBlank(message = "人脸相似度不能为空")
    private String faceSimilarity;
    /**
     * 相同人告警间隔
     */
    @NotNull(message = "相同人告警间隔不能为空")
    private Integer samePersonAlarmInterval;
    /**
     * 相同人告警间隔单位，1-秒，2-分，3-时，4-天
     */
    @NotNull(message = "相同人告警间隔单位不能为空")
    private Integer samePersonAlarmIntervalUnit;
    /**
     * 分组id
     */
    @NotEmpty(message = "分组id不能为空")
    private List<Long> groupIds;
    /**
     * 通道id
     */
    @NotEmpty(message = "通道id不能为空")
    private List<String> channelIds;
    /**
     * 算法id
     */
    @NotEmpty(message = "算法id不能为空")
    private List<Long> algorithmIds;
}
