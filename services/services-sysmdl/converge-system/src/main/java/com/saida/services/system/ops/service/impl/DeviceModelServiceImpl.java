package com.saida.services.system.ops.service.impl;

import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.saida.services.exception.BizRuntimeException;
import com.saida.services.common.tools.JwtUtil;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.converge.entity.DeviceModelEntity;
import com.saida.services.system.ops.mapper.DeviceModelMapper;
import com.saida.services.system.ops.service.DeviceModelService;
import com.saida.services.system.sys.service.AttributeDetailService;
import com.saida.services.tools.attr.AttrUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service("deviceModelService")
public class DeviceModelServiceImpl extends ServiceImpl<DeviceModelMapper, DeviceModelEntity> implements DeviceModelService {

    @Autowired
    private AttributeDetailService attributeDetailService;

    @Override
    public void addOrUpdate(DeviceModelEntity entity) {
        if(entity.getId() == null){//新增
            entity.setCreateUser(JwtUtil.getUserId());
            entity.setCreateTime(DateTime.now());
            DeviceModelEntity tmp = getOne(new LambdaQueryWrapper<DeviceModelEntity>().eq(DeviceModelEntity::getModel, entity.getModel()), false);
            if(tmp != null){
                throw new BizRuntimeException("设备型号已存在");
            }
            save(entity);
        }else{//修改
            entity.setUpdateUser(JwtUtil.getUserId());
            entity.setUpdateTime(DateTime.now());
            DeviceModelEntity tmp = getOne(new LambdaQueryWrapper<DeviceModelEntity>().eq(DeviceModelEntity::getModel, entity.getModel()).ne(DeviceModelEntity::getId, entity.getId()), false);
            if(tmp != null){
                throw new BizRuntimeException("设备型号已存在");
            }
            updateById(entity);
        }
    }

    @Override
    public IPage<DeviceModelEntity> listPage(DeviceModelEntity entity) {
        IPage<DeviceModelEntity> page = page(new Page<DeviceModelEntity>(entity.getPageNum(), entity.getPageSize()),
                new LambdaQueryWrapper<DeviceModelEntity>()
                .eq(entity.getManufactor() != null, DeviceModelEntity::getManufactor, entity.getManufactor())
                .eq(entity.getDeviceType() != null, DeviceModelEntity::getDeviceType, entity.getDeviceType())
                .like(!StringUtil.isEmpty(entity.getModel()), DeviceModelEntity::getModel, entity.getModel())
                        .orderByDesc(DeviceModelEntity::getId)
        );
        if(page == null || page.getRecords() == null || page.getRecords().isEmpty()){
            return page;
        }
        fillAttr(page.getRecords());
        return page;
    }

    @Override
    public DeviceModelEntity getInfo(Long id) {
        DeviceModelEntity model = getById(id);
        if(model == null){
            return null;
        }
        return fillAttr(new ArrayList<DeviceModelEntity>(){{add(model);}}).get(0);
    }

    @Override
    public List<DeviceModelEntity> getList(DeviceModelEntity entity) {
        List<DeviceModelEntity> records = list(new LambdaQueryWrapper<DeviceModelEntity>()
                .eq(entity.getManufactor() != null, DeviceModelEntity::getManufactor, entity.getManufactor())
                .eq(entity.getDeviceType() != null, DeviceModelEntity::getDeviceType, entity.getDeviceType())
                .like(!StringUtil.isEmpty(entity.getModel()), DeviceModelEntity::getModel, entity.getModel())
        );
        if(records == null || records.isEmpty()){
            return records;
        }
        fillAttr(records);
        return records;
    }

    @Override
    public void delete(Long id) {
        removeById(id);
    }

    private List<DeviceModelEntity> fillAttr(List<DeviceModelEntity> records) {
        if(records == null){
            return records;
        }
        Map<Object, Object> dicMap = new HashMap<>();
        Map<Long, String> attrMap = attributeDetailService.getAllIdNameMap();
        if(attrMap != null && !attrMap.isEmpty()){
            dicMap.putAll(attrMap);
        }
        records.replaceAll(o -> AttrUtil.putAttr(o, dicMap));
        return records;
    }
}