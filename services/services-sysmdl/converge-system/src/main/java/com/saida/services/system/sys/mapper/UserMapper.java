package com.saida.services.system.sys.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.saida.services.system.sys.entity.UserEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-09-23 14:03:54
 */
@Mapper
public interface UserMapper extends BaseMapper<UserEntity> {

    /**
     *
     * @param userEntityPage
     * @param entity
     * @param orgIds
     * @return
     */
    IPage<UserEntity> listPage(Page<UserEntity> userEntityPage, @Param("entity") UserEntity entity, @Param("orgIds") List<Long> orgIds);
}
