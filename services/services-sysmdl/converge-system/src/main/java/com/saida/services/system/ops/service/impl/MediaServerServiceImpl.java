package com.saida.services.system.ops.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.saida.services.common.base.DtoResult;
import com.saida.services.common.base.Result;
import com.saida.services.exception.BizRuntimeException;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.converge.entity.*;
import com.saida.services.converge.entity.dto.ServiceSmsDto;
import com.saida.services.converge.entity.dto.StreamInfoDto;
import com.saida.services.converge.qxNode.QxNodeApiEnum;
import com.saida.services.converge.qxNode.req.ConvergeChangeServiceReq;
import com.saida.services.converge.qxNode.req.ConvergeFindSubscriptionsReq;
import com.saida.services.converge.qxNode.req.ConvergeServicesStreamsReq;
import com.saida.services.converge.qxNode.resp.MediaStreamItem;
import com.saida.services.converge.qxNode.resp.MediaStreamResp;
import com.saida.services.converge.qxNode.resp.ServiceSmsResp;
import com.saida.services.converge.qxNode.resp.StreamSubscriptionsResp;
import com.saida.services.entities.pojo.CountDto;
import com.saida.services.system.client.nodev1.QxNodeReqService;
import com.saida.services.system.client.nodev1.QxNodeReqUtil;
import com.saida.services.system.client.nodev1.ResponseDto;
import com.saida.services.system.ops.dto.ChangeServiceDto;
import com.saida.services.system.ops.mapper.DeviceMapper;
import com.saida.services.system.ops.mapper.MediaServerMapper;
import com.saida.services.system.ops.mapper.SignalNodeMapper;
import com.saida.services.system.ops.service.MediaServerService;
import com.saida.services.system.ops.service.MediaServerStatusRecordService;
import com.saida.services.system.ops.service.OpsDeviceChannelService;
import com.saida.services.tools.attr.AttrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Service("mediaServerService")
public class MediaServerServiceImpl extends ServiceImpl<MediaServerMapper, MediaServerEntity> implements MediaServerService {

    @Autowired
    private SignalNodeMapper signalNodeMapper;

    @Autowired
    private MediaServerStatusRecordService mediaServerStatusRecordService;

    @Autowired
    private QxNodeReqService qxNodeReqService;

    @Autowired
    private DeviceMapper deviceMapper;

    @Resource
    private QxNodeReqUtil qxNodeReqUtil;

    @Resource
    private OpsDeviceChannelService opsDeviceChannelService;

    @Override
    public void addOrUpdate(MediaServerEntity entity) {
        if (StringUtil.isEmpty(entity.getId())) {
            log.error("参数错误");
            return;
        }
        MediaServerEntity server = getById(entity.getId());
        entity.setUpdateTime(DateTime.now());
        if (server == null) {//新增
            save(entity);
            saveStatusRecord(entity);
        } else {//更新
            updateById(entity);
            saveStatusRecord(entity);
        }
    }

    private void saveStatusRecord(MediaServerEntity entity) {
        mediaServerStatusRecordService.save(new MediaServerStatusRecordEntity() {{
            setBearNum(entity.getBearNum());
            setOnLineNum(entity.getOnLineNum());
            setEnterFlow(entity.getEnterFlow());
            setOutFlow(entity.getOutFlow());
            setServerId(entity.getId());
            setNodeId(entity.getNodeId());
            setRecordTime(DateTime.now());
        }});
    }

    @Override
    public IPage<MediaServerEntity> listPage(MediaServerEntity entity) {
        IPage<MediaServerEntity> page = page(new Page<>(entity.getPageNum(), entity.getPageSize()),
                new LambdaQueryWrapper<MediaServerEntity>()
                        .eq(!StringUtil.isEmpty(entity.getNodeId()), MediaServerEntity::getNodeId, entity.getNodeId())
                        .eq(!StringUtil.isEmpty(entity.getDomainUrl()), MediaServerEntity::getDomainUrl, entity.getDomainUrl())
                        .eq(entity.getStatus() != null, MediaServerEntity::getStatus, entity.getStatus())
                        .eq(entity.getType() != null, MediaServerEntity::getType, entity.getType())
                        //只查询1个小时内在线的数据
                        .ge(MediaServerEntity::getUpdateTime, DateTime.now().offset(DateField.HOUR, -3))
        );
        if (page == null || page.getRecords() == null || page.getRecords().isEmpty()) {
            return page;
        }
        fillAttr(page.getRecords());
        return page;
    }

    private List<MediaServerEntity> fillAttr(List<MediaServerEntity> records) {
        if (records == null || records.isEmpty()) {
            return records;
        }
        Map<Object, Object> dicMap = new HashMap<>();
        List<String> nodeIds = records.stream().map(MediaServerEntity::getNodeId).filter(nodeId -> !StringUtil.isEmpty(nodeId)).distinct().collect(Collectors.toList());
        if (!nodeIds.isEmpty()) {
            List<SignalNodeEntity> nodeList = signalNodeMapper.selectBatchIds(nodeIds);
            if (nodeList != null && !nodeList.isEmpty()) {
                dicMap.putAll(nodeList.stream().collect(Collectors.toMap(SignalNodeEntity::getId, SignalNodeEntity::getName)));
            }
        }
        records.replaceAll(o -> AttrUtil.putAttr(o, dicMap));
        return records;
    }

    @Override
    public List<MediaServerEntity> getList(MediaServerEntity entity) {
        List<MediaServerEntity> records = list(new LambdaQueryWrapper<MediaServerEntity>()
                .eq(!StringUtil.isEmpty(entity.getNodeId()), MediaServerEntity::getNodeId, entity.getNodeId())
                .eq(!StringUtil.isEmpty(entity.getDomainUrl()), MediaServerEntity::getDomainUrl, entity.getDomainUrl())
                .eq(entity.getStatus() != null, MediaServerEntity::getStatus, entity.getStatus())
                .eq(entity.getType() != null, MediaServerEntity::getType, entity.getType())
        );
        if (records == null || records.isEmpty()) {
            return records;
        }
        fillAttr(records);
        return records;
    }

    @Override
    public MediaServerEntity getInfo(String id) {
        MediaServerEntity mediaServer = getById(id);
        if (mediaServer == null) {
            return null;
        }
        return fillAttr(new ArrayList<MediaServerEntity>() {{
            add(mediaServer);
        }}).get(0);
    }

    @Override
    public void delete(String id) {
        removeById(id);
    }


    @Override
    public Result groupByStatus(MediaServerEntity entity) {
        List<MediaServerEntity> list = super.list();
        long count = list.stream().filter(e -> ((double) e.getOnLineNum() / e.getBearNum()) > 0.8).count();
        List<CountDto> res = new ArrayList<>();
        res.add(CountDto.builder().type1(0).longCount(count).build());
        res.add(CountDto.builder().type1(1).longCount(list.size() - count).build());
        return Result.ok(res);
    }

    @Override
    public IPage<StreamInfoDto> getStreamPage(MediaServerEntity entity) {
        MediaServerEntity server = getById(entity.getId());
        if (server == null) {
            throw new BizRuntimeException("流媒体服务不存在");
        }
        if (StringUtil.isEmpty(server.getNodeId())) {
            throw new BizRuntimeException("流媒体服务未绑定节点");
        }
        SignalNodeEntity node = signalNodeMapper.selectById(server.getNodeId());
        if (node == null) {
            throw new BizRuntimeException("节点不存在");
        }
        MediaStreamResp resp = qxNodeReqService.servicesStreams(node, server.getId(), entity.getPageNum(), entity.getPageSize(), entity.getDeviceSn(), entity.getChannelId(), entity.getStreamType());
        IPage<StreamInfoDto> page = new Page<StreamInfoDto>(entity.getPageNum(), entity.getPageSize());
        if (resp == null) {
            page.setTotal(0L);
            page.setRecords(new ArrayList<>());
            return page;
        }
        List<StreamInfoDto> records = new ArrayList<>();
        Set<String> sns = new HashSet<>();
        for (MediaStreamItem i : resp.getItems()) {
            StreamInfoDto info = new StreamInfoDto();
            info.setDeviceSN(i.getDevice_id());
            info.setChannelId(i.getChannel_id());
            info.setStreamType(i.getStream_type());
            info.setIpcIp(i.getIpc_ip());
            info.setIpcPort(i.getIpc_port());
            info.setLefttimeMs(i.getLefttime_ms());
            info.setOnlinePlayCount(i.getOnline_play_count());
            if (i.getVideo() != null) {
                info.setVideoCodec(i.getVideo().getCodec());
                info.setVideoBitRate(i.getVideo().getBit_rate());
                info.setVideoFps(i.getVideo().getFps());
                info.setVideoWidth(i.getVideo().getWidth());
                info.setVideoHeight(i.getVideo().getHeight());
            }
            if (i.getAudio() != null) {
                info.setAudioCodec(i.getAudio().getCodec());
                info.setAudioBitRate(i.getAudio().getBit_rate());
                info.setAudioSampleRate(i.getAudio().getSample_rate());
            }
            records.add(info);
            sns.add(i.getDevice_id());
        }
        if (!sns.isEmpty()) {
            List<DeviceEntity> deviceList = deviceMapper.selectList(new LambdaQueryWrapper<DeviceEntity>()
                    .in(DeviceEntity::getDeviceCode, sns));
            if (deviceList != null && !deviceList.isEmpty()) {
                Map<Object, Object> dicMap = new HashMap<>(deviceList.stream().collect(Collectors.toMap(DeviceEntity::getDeviceCode, d -> new HashMap<Object, Object>() {{
                    put("deviceId", d.getId());
                    put("nodeId", d.getNodeId());
                }})));
                records.replaceAll(o -> AttrUtil.putAttr(o, dicMap));
            }
        }
        page.setTotal(resp.getTotal());
        page.setRecords(records);
        return page;
    }

    @Override
    public DtoResult<IPage<StreamInfoDto>> getStreamPageV2(MediaServerEntity entity) {
        MediaServerEntity server = getById(entity.getId());
        if (server == null) {
            throw new BizRuntimeException("流媒体服务不存在");
        }
        if (StringUtil.isEmpty(server.getNodeId())) {
            throw new BizRuntimeException("流媒体服务未绑定节点");
        }
        SignalNodeEntity node = signalNodeMapper.selectById(server.getNodeId());
        if (node == null) {
            throw new BizRuntimeException("节点不存在");
        }

        ConvergeServicesStreamsReq req = ConvergeServicesStreamsReq.builder()
                .id(server.getId())
                .deviceId(entity.getDeviceSn())
                .channelId(entity.getChannelId())
                .streamType(entity.getStreamType())
                .page(entity.getPageNum())
                .size(entity.getPageSize())
                .build();
        ResponseDto resp = qxNodeReqUtil.sendRequest(QxNodeApiEnum.SERVICES_STREAMS, node, req);
        if (resp.getHttpCode() != 200) {
            return DtoResult.error(resp.getMsg());
        }

        MediaStreamResp res = JSONObject.parseObject(resp.getRes(), MediaStreamResp.class);
        IPage<StreamInfoDto> page = new Page<>(entity.getPageNum(), entity.getPageSize());


        // 查询设备id
        List<String> deviceIdList = res.getItems().stream().map(MediaStreamItem::getDevice_id).collect(Collectors.toList());
        Map<String, Long> deviceMap = new HashMap<>();
        Map<String, Long> channelMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(deviceIdList)) {
            LambdaQueryWrapper<DeviceEntity> query = Wrappers.lambdaQuery();
            query.in(DeviceEntity::getDeviceCode, deviceIdList);
            List<DeviceEntity> deviceList = deviceMapper.selectList(query);
            deviceMap = deviceList.stream().collect(Collectors.toMap(DeviceEntity::getDeviceCode, DeviceEntity::getId, (key1, key2) -> key1));
        }

        // 查询通道
        List<String> channelIdList = res.getItems().stream().map(MediaStreamItem::getChannel_id).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(channelIdList)) {
            List<OpsDeviceChannelEntity> channelList = opsDeviceChannelService.getByChannelIds(channelIdList);
            channelMap = channelList.stream().collect(Collectors.toMap(OpsDeviceChannelEntity::getChannelId, OpsDeviceChannelEntity::getId, (key1, key2) -> key1));
        }

        List<StreamInfoDto> records = new ArrayList<>();
        Set<String> sns = new HashSet<>();
        for (MediaStreamItem i : res.getItems()) {
            StreamInfoDto info = new StreamInfoDto();
            info.setDeviceSN(i.getDevice_id());
            info.setChannelId(i.getChannel_id());
            info.setStreamType(i.getStream_type());
            info.setIpcIp(i.getIpc_ip());
            info.setIpcPort(i.getIpc_port());
            info.setId(channelMap.getOrDefault(i.getChannel_id(), null));
            info.setLefttimeMs(i.getLefttime_ms());
            info.setOnlinePlayCount(i.getOnline_play_count());
            info.setDeviceName(i.getDevice_name());
            info.setStatus(i.getStatus());
            info.setDeviceId(deviceMap.getOrDefault(i.getDevice_id(), null));
            info.setLosePacketRate(i.getLose_packet_rate());
            info.setPushAddr(i.getPush_addr());
            info.setDeadtimeMs(i.getDeadtimeMs());
            info.setUnabled(i.getUnabled());
            info.setCreateAt(i.getCreated_at());
            info.setStreamName(i.getStream_name());
            if (i.getVideo() != null) {
                info.setVideoCodec(i.getVideo().getCodec());
                info.setVideoBitRate(i.getVideo().getBit_rate());
                info.setVideoFps(i.getVideo().getFps());
                info.setVideoWidth(i.getVideo().getWidth());
                info.setVideoHeight(i.getVideo().getHeight());
            }
            if (i.getAudio() != null) {
                info.setAudioCodec(i.getAudio().getCodec());
                info.setAudioBitRate(i.getAudio().getBit_rate());
                info.setAudioSampleRate(i.getAudio().getSample_rate());
            }
            records.add(info);
            sns.add(i.getDevice_id());
        }
        if (!sns.isEmpty()) {
            List<DeviceEntity> deviceList = deviceMapper.selectList(new LambdaQueryWrapper<DeviceEntity>()
                    .in(DeviceEntity::getDeviceCode, sns));
            if (deviceList != null && !deviceList.isEmpty()) {
                Map<Object, Object> dicMap = new HashMap<>(deviceList.stream().collect(Collectors.toMap(DeviceEntity::getDeviceCode, d -> new HashMap<Object, Object>() {{
                    put("deviceId", d.getId());
                    put("nodeId", d.getNodeId());
                }})));
                records.replaceAll(o -> AttrUtil.putAttr(o, dicMap));
            }
        }
        page.setTotal(res.getTotal());
        page.setRecords(records);
        return DtoResult.ok(page);
    }

    @Override
    public DtoResult<List<ServiceSmsDto>> getServiceSms(String mediaId) {
        MediaServerEntity server = getById(mediaId);
        if (server == null) {
            throw new BizRuntimeException("流媒体服务不存在");
        }
        if (StringUtil.isEmpty(server.getNodeId())) {
            throw new BizRuntimeException("流媒体服务未绑定节点");
        }
        SignalNodeEntity node = signalNodeMapper.selectById(server.getNodeId());
        if (node == null) {
            throw new BizRuntimeException("节点不存在");
        }

        ResponseDto resp = qxNodeReqUtil.sendRequest(QxNodeApiEnum.SERVICE_SMS, node, new HashMap<>());
        if (resp.getHttpCode() != 200) {
            return DtoResult.error(resp.getMsg());
        }

        List<ServiceSmsResp> smsList = JSONObject.parseArray(resp.getRes(), ServiceSmsResp.class);
        return DtoResult.ok(smsList.stream().map(it -> {
            return ServiceSmsDto.builder()
                    .id(it.getId())
                    .tags(it.getTags())
                    .bearNum(it.getBearNum())
                    .extranet(it.getExtranet())
                    .intranet(it.getIntranet())
                    .port(it.getPort())
                    .name(it.getName())
                    .flvUrl(it.getFlvUrl())
                    .hlsUrl(it.getHlsUrl())
                    .wsFlvUrl(it.getWsFlvUrl())
                    .webrtcUrl(it.getWebrtcUrl())
                    .build();
        }).collect(Collectors.toList()));
    }

    @Override
    public DtoResult<List<StreamSubscriptionsResp>> findSubscriptions(String mediaId, String name) {
        MediaServerEntity server = getById(mediaId);
        if (server == null) {
            throw new BizRuntimeException("流媒体服务不存在");
        }
        if (StringUtil.isEmpty(server.getNodeId())) {
            throw new BizRuntimeException("流媒体服务未绑定节点");
        }
        SignalNodeEntity node = signalNodeMapper.selectById(server.getNodeId());
        if (node == null) {
            throw new BizRuntimeException("节点不存在");
        }

        ResponseDto resp = qxNodeReqUtil.sendRequest(QxNodeApiEnum.SERVICES_STREAMS_SUBSCRIPTIONS, node, ConvergeFindSubscriptionsReq.builder()
                .id(mediaId)
                .name(name)
                .build());
        if (resp.getHttpCode() != 200) {
            return DtoResult.error(resp.getMsg());
        }

        return DtoResult.ok(JSONObject.parseArray(resp.getRes(), StreamSubscriptionsResp.class));
    }

    @Override
    public DtoResult<Void> changeService(ChangeServiceDto dto) {
        MediaServerEntity server = getById(dto.getOldMediaId());
        if (server == null) {
            throw new BizRuntimeException("流媒体服务不存在");
        }
        if (StringUtil.isEmpty(server.getNodeId())) {
            throw new BizRuntimeException("流媒体服务未绑定节点");
        }
        SignalNodeEntity node = signalNodeMapper.selectById(server.getNodeId());
        if (node == null) {
            throw new BizRuntimeException("节点不存在");
        }

        ResponseDto resp = qxNodeReqUtil.sendRequest(QxNodeApiEnum.CHANNEL_CHANGE_SERVICE, node, ConvergeChangeServiceReq.builder()
                .id(dto.getId())
                .smsId(dto.getMediaId())
                .build());
        if (resp.getHttpCode() != 200) {
            return DtoResult.error(resp.getMsg());
        }

        return DtoResult.ok();
    }
}
