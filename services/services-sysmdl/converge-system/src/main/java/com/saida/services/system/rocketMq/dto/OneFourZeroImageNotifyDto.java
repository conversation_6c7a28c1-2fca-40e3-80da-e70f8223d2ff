package com.saida.services.system.rocketMq.dto;

import lombok.Data;

import java.util.List;

@Data
public class OneFourZeroImageNotifyDto {

    /**
     *
     * {
     * 	"ImageInfo ": {
     * 		"ContentDescription ": "ContentDescription ",
     * 		"DeviceID ": "34020000001190000002 ",
     * 		"EventSort ": 10000,
     * 		"FileFormat ": "Jpeg ",
     * 		"Height ": 1080,
     * 		"Width ": 1920,
     * 		"ImageID ": "34020000001190000002032025021016573243251 ",
     * 		"ImageSource ": "99 ",
     * 		"InfoKind ": 1,
     * 		"SecurityLevel ": "5 ",
     * 		"ShotPlaceFullAdress ": "ShotPlaceFullAdress ",
     * 		"ShotTime ": "20250210165732 ",
     * 		"Sort ": "",
     * 		"SortType ": "",
     * 		"StoragePath ": "http: //**************:11400/data/2025021016/34020000001190000002022025021016573243254.Jpeg",
     * 		"Title": "Title"
     *        },
     * 	"FaceList": {
     * 		"FaceObject": [{
     * 			"FaceID": "340200000011900000020320250210165732432510200001",
     * 			"SourceID": "34020000001190000002032025021016573243251",
     * 			"DeviceID": "34020000001190000002",
     * 			"LeftTopX": 514,
     * 			"LeftTopY": 479,
     * 			"RightBtmX": 565,
     * 			"RightBtmY": 533,
     * 			"GenderCode": "0",
     * 			"SubImageList": {
     * 				"SubImageInfoObject": [{
     * 					"ImageID": "34020000001190000002022025021016573243251",
     * 					"EventSort": 10000,
     * 					"DeviceID": "34020000001190000002",
     * 					"StoragePath": "http://**************:11400/data/2025021016/34020000001190000002022025021016573243251_11.Jpeg",
     * 					"Type": "11",
     * 					"FileFormat": "Jpeg",
     * 					"ShotTime": "20250210165732",
     * 					"Width": 51,
     * 					"Height": 54,
     * 					"Data": "data/2025021016/34020000001190000002022025021016573243251_11.Jpeg",
     * 					"FeatureInfoObject": ""
     *                }, {
     * 					"ImageID": "34020000001190000002142025021016573243251",
     * 					"EventSort": 10000,
     * 					"DeviceID": "34020000001190000002",
     * 					"StoragePath": "http://**************:11400/data/2025021016/34020000001190000002142025021016573243251_14.Jpeg",
     * 					"Type": "14",
     * 					"FileFormat": "Jpeg",
     * 					"ShotTime": "20250210165732",
     * 					"Width": 1920,
     * 					"Height": 1080,
     * 					"Data": "data/2025021016/34020000001190000002142025021016573243251_14.Jpeg",
     * 					"FeatureInfoObject": ""
     *                }]
     *            }
     *        }],
     * 		"TotalNum": 0
     *    },
     * 	"PersonList": null,
     * 	"MotorVehiclesList": null,
     * 	"NonMotorVehicleList": {
     * 		"TotalNum": 0,
     * 		"NonMotorVehicleObject": [{
     * 			"NonMotorVehicleID": "340200000011900000020320250210165732432510300001",
     * 			"InfoKind": 1,
     * 			"SourceID": "34020000001190000002032025021016573243251",
     * 			"DeviceID": "34020000001190000002",
     * 			"LeftTopX": 439,
     * 			"LeftTopY": 477,
     * 			"RightBtmX": 610,
     * 			"RightBtmY": 805,
     * 			"MarkTime": "",
     * 			"AppearTime": "",
     * 			"DisappearTime": "",
     * 			"HasPlate": false,
     * 			"PlateClass": "",
     * 			"PlateNo": "",
     * 			"PlateNoAttach": "",
     * 			"PlateDescribe": "",
     * 			"IsDecked": "",
     * 			"IsAltered": "",
     * 			"IsCovered": "",
     * 			"Speed": 0,
     * 			"Direction": "",
     * 			"DrivingStatusCode": "",
     * 			"UsingPropertiesCode": 0,
     * 			"VehicleBrand": "",
     * 			"VehicleType": "",
     * 			"VehicleLength": 0,
     * 			"VehicleWidth": 0,
     * 			"VehicleHeight": 0,
     * 			"VehicleColor": "",
     * 			"VehicleHood": "",
     * 			"VehicleTrunk": "",
     * 			"VehicleWheel": "",
     * 			"WheelPrintedPattern": "",
     * 			"VehicleWindow": "",
     * 			"VehicleRoof": "",
     * 			"VehicleDoor": "",
     * 			"SideOfVehicle": "",
     * 			"CarOfVehicle": "",
     * 			"RearviewMirror": "",
     * 			"VehicleChassis": "",
     * 			"VehicleShielding": "",
     * 			"FilmColor": 0,
     * 			"IsModified": 0,
     * 			"SubImageList": {
     * 				"SubImageInfoObject": [{
     * 					"ImageID": "34020000001190000002022025021016573243253",
     * 					"EventSort": 10000,
     * 					"DeviceID": "34020000001190000002",
     * 					"StoragePath": "http://**************:11400/data/2025021016/34020000001190000002022025021016573243253.Jpeg",
     * 					"Type": "12",
     * 					"FileFormat": "Jpeg",
     * 					"ShotTime": "20250210165732",
     * 					"Width": 224,
     * 					"Height": 432,
     * 					"Data": "data/2025021016/34020000001190000002022025021016573243253.Jpeg",
     * 					"FeatureInfoObject": ""
     *                }, {
     * 					"ImageID": "34020000001190000002022025021016573243254",
     * 					"EventSort": 10000,
     * 					"DeviceID": "34020000001190000002",
     * 					"StoragePath": "http://**************:11400/data/2025021016/34020000001190000002022025021016573243254.Jpeg",
     * 					"Type": "14",
     * 					"FileFormat": "Jpeg",
     * 					"ShotTime": "20250210165732",
     * 					"Width": 1920,
     * 					"Height": 1080,
     * 					"Data": "data/2025021016/34020000001190000002022025021016573243254.Jpeg",
     * 					"FeatureInfoObject": ""
     *                }]
     *            }
     *        }]
     *    },
     * 	"Data": ""
     * }
     */

    private ImageInfoDto ImageInfo;
    private FaceListDto FaceList;
    private PersonListDto PersonList;
    private MotorVehicleListDto MotorVehiclesList;
    private NonMotorVehicleListDto NonMotorVehicleList;
    private String Data;


    @Data
    public static class PersonListDto {
        private Integer TotalNum;
        private List<OneFourZeroPersonNotifyDto> FaceObject;
    }

    @Data
    public static class FaceListDto {
        private Integer TotalNum;
        private List<OneFourZeroFaceNotifyDto> FaceObject;
    }


    @Data
    public static class NonMotorVehicleListDto {
        private Integer TotalNum;
        private List<NonMotorVehicleObjectDto> NonMotorVehicleObject;
    }
    @Data
    public static class MotorVehicleListDto {
        private Integer TotalNum;
        private List<MotorVehicleObjectDto> MotorVehicleObject;
    }

    @Data
    public static class ImageInfoDto {

        private String ContentDescription;
        private String DeviceID;
        private Integer EventSort;
        private String FileFormat;
        private Integer Height;
        private Integer Width;
        private String ImageID;
        private String ImageSource;
        private Integer InfoKind;
        private String SecurityLevel;
        private String ShotPlaceFullAdress;
        private String ShotTime;
        private String Sort;
        private String SortType;
        private String StoragePath;
        private String Title;
    }





}
