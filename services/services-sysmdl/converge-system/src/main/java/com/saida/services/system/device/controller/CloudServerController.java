package com.saida.services.system.device.controller;

import com.saida.services.algorithm.enums.CloudTypeEnum;
import com.saida.services.common.base.Result;
import com.saida.services.common.entity.BasePageInfoEntity;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.entities.pojo.CountDto;
import com.saida.services.system.device.entity.CloudServerEntity;
import com.saida.services.system.device.service.CloudServerService;
import com.saida.services.system.device.vo.CloudServerVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 云服务管理
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2024-01-04 10:44:56
 */
@RestController
@RequestMapping("device/cloudserver")
public class CloudServerController {

    @Autowired
    private CloudServerService cloudServerService;

    @GetMapping("getCloudType")
    public Result getCloudType() {
        // 获取所有AccessWayType枚举常量
        CloudTypeEnum[] values = CloudTypeEnum.values();

        // 将枚举常量流转换为CountDto对象列表
        List<CountDto> collect = Arrays.stream(values).map(t -> {
            // 创建一个新的CountDto对象
            CountDto dto = new CountDto();

            // 设置CountDto对象的type字段为枚举常量的type值的字符串表示
            dto.setType(String.valueOf(t.getType()));

            // 设置CountDto对象的typeName字段为枚举常量的名称
            dto.setTypeName(t.getName());

            // 返回CountDto对象
            return dto;
        }).collect(Collectors.toList());
        return Result.ok(collect);
    }

    @PostMapping("save")
    public Result save(CloudServerEntity entity) {
        if (entity.getCloudType() == null){
            return Result.error("云服务类型必填");
        }
        if (StringUtil.isEmpty(entity.getIp()) || entity.getPort() == null) {
            return Result.error("IP、端口必填");
        }
        cloudServerService.addOrUpdate(entity);
        return Result.ok();
    }

    @GetMapping("listPage")
    public Result listPage(CloudServerEntity entity) {
        BasePageInfoEntity<CloudServerVo> page = cloudServerService.listPage(entity);
        return Result.ok(page);
    }

    @GetMapping("getList")
    public Result getList(CloudServerEntity entity) {
        List<CloudServerVo> list = cloudServerService.getList(entity);
        return Result.ok(list);
    }

    @GetMapping("getInfo")
    public Result getInfo(CloudServerEntity entity) {
        return cloudServerService.getInfo(entity);
    }

    @PostMapping("delete")
    public Result delete(CloudServerEntity entity) {
        if (entity.getId() == null) {
            return Result.error("请选择一条数据");
        }
        cloudServerService.delete(entity.getId());
        return Result.ok();
    }
}