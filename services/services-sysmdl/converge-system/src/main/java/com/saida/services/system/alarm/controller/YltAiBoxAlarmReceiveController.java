package com.saida.services.system.alarm.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.saida.services.system.alarm.service.YltAiBoxAlarmReceiveService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("/openApi")
public class YltAiBoxAlarmReceiveController {

    @Resource
    private YltAiBoxAlarmReceiveService yltAiBoxAlarmReceiveService;

    /**
     * 亚略特告警数据接收
     */
    @PostMapping("/checkRecord/reportCheckRecords")
    public JSONObject reportCheckRecords(@RequestBody JSONArray jsonArray) {
        return yltAiBoxAlarmReceiveService.receiveYlt(jsonArray);
    }
}