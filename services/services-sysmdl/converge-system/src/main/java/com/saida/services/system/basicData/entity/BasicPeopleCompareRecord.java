package com.saida.services.system.basicData.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.saida.services.common.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 人脸比对历史记录(BasicPeopleCompareRecord)实体类
 *
 * <AUTHOR>
 * @since 2025-06-17 16:58:49
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("basic_people_compare_record")
public class BasicPeopleCompareRecord extends BaseEntity implements Serializable {
    private static final long serialVersionUID = -43413446946941538L;

    private Long appid;
    /**
     * 分析耗时(ms)
     */
    private Long duration;
    /**
     * 相似度
     */
    private String faceSimilarity;
    /**
     * 左脸url
     */
    private String leftImage;
    /**
     * 右脸url
     */
    private String rightImage;
    /**
     * 左脸坐标
     */
    private String leftCoordinate;
    /**
     * 右脸坐标
     */
    private String rightCoordinate;

    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime startTime;

    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endTime;

}

