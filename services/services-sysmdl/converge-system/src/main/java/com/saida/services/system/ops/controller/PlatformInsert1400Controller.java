package com.saida.services.system.ops.controller;

import com.saida.services.common.base.Result;
import com.saida.services.converge.entity.PlatformInsert1400Entity;
import com.saida.services.log.LogOperation;
import com.saida.services.log.LogOperationEnum;
import com.saida.services.log.ModuleEnum;
import com.saida.services.system.ops.service.PlatformInsert1400Service;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;


/**
 * 1400平台接入
 *
 * <AUTHOR>
 * email ${email}
 * date 2023-10-10 15:31:54
 */
@RestController
@RequestMapping("/ops/platform")
public class PlatformInsert1400Controller {

    @Resource
    private PlatformInsert1400Service platformInsert1400Service;

    /**
     * 分页列表
     */
    @GetMapping("/listPage")
    @LogOperation(type = LogOperationEnum.QUERY, func = "查询1400平台接入分页列表", module = ModuleEnum.PLATFORM_INSERT)
    public Result listPage(PlatformInsert1400Entity entity) {
        return platformInsert1400Service.listPage(entity);
    }

    /**
     * 详情查询
     */
    @GetMapping("/info")
    @LogOperation(type = LogOperationEnum.QUERY, func = "查询1400平台接入列表", module = ModuleEnum.PLATFORM_INSERT)
    public Result info(PlatformInsert1400Entity entity) {
        return platformInsert1400Service.info(entity);
    }

    /**
     * 新增
     */
    @PostMapping("/add")
    @LogOperation(type = LogOperationEnum.ADD, func = "新增1400平台接入", module = ModuleEnum.PLATFORM_INSERT)
    public Result add(@Valid PlatformInsert1400Entity entity) {
        return platformInsert1400Service.add(entity);
    }

    /**
     * 删除
     */
    @PostMapping("/delete")
    @LogOperation(type = LogOperationEnum.DELETE, func = "删除1400平台接入", module = ModuleEnum.PLATFORM_INSERT)
    public Result delete(PlatformInsert1400Entity entity) {
        return platformInsert1400Service.delete(entity);
    }

    /**
     * 编辑
     */
    @PostMapping("/edit")
    @LogOperation(type = LogOperationEnum.EDIT, func = "修改1400平台接入", module = ModuleEnum.PLATFORM_INSERT)
    public Result edit(PlatformInsert1400Entity entity) {
        return platformInsert1400Service.edit(entity);
    }


}
