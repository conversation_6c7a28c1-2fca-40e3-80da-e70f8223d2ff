package com.saida.services.system.alarm.controller;

import com.saida.services.algorithm.dto.AlgAlarmDownloadTaskListPageDTO;
import com.saida.services.algorithm.entity.AlgAlarmDownloadTaskEntity;
import com.saida.services.algorithm.vo.AlgAlarmDownloadTaskListPageVO;
import com.saida.services.common.base.DtoResult;
import com.saida.services.common.entity.BasePageInfoEntity;
import com.saida.services.entities.base.BaseRequest;
import com.saida.services.system.alarm.service.AlarmDownloadTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("/alarm/download/task")
public class AlarmDownloadTaskController {

    @Resource
    private AlarmDownloadTaskService alarmDownloadTaskService;

    @GetMapping("/listPage")
    public DtoResult<BasePageInfoEntity<AlgAlarmDownloadTaskListPageVO>> listPage(AlgAlarmDownloadTaskListPageDTO dto, BaseRequest baseRequest) {
        return alarmDownloadTaskService.listPage(dto, baseRequest);
    }

    @PostMapping("/delete")
    public DtoResult<Void> delete(@RequestBody AlgAlarmDownloadTaskEntity entity) {
        return alarmDownloadTaskService.delete(entity);
    }
}