package com.saida.services.system.device.biz.doubleMai;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.saida.services.common.base.DtoResult;
import com.saida.services.exception.BizRuntimeException;
import com.saida.services.system.device.biz.BizAuthDto;
import com.saida.services.system.device.biz.doubleMai.req.DoubleMaiAddFaceReq;
import com.saida.services.system.device.biz.doubleMai.req.DoubleMaiDeviceReq;
import com.saida.services.system.device.biz.doubleMai.req.DoubleMaiEditFaceReq;
import com.saida.services.system.device.biz.doubleMai.req.DoubleMaiLoginReq;
import com.saida.services.system.device.biz.doubleMai.resp.DoubleMaiAddFaceResp;
import com.saida.services.system.device.biz.doubleMai.resp.DoubleMaiDeviceResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Slf4j
@Component
public class DoubleMaiBiz {

    @Autowired
    private SendToDoubleMaiRequest sendToDoubleMaiRequest;
    @Resource
    private ObjectMapper objectMapper;

    public void login(BizAuthDto bizAuthDto) {
        DoubleMaiLoginReq loginReq = new DoubleMaiLoginReq();
        loginReq.setName(bizAuthDto.getUsername());
        loginReq.setPassword(bizAuthDto.getPassword());
        try {
            String res = sendToDoubleMaiRequest.sendRequest(DoubleMaiApiEnum.LOGIN, null, loginReq, bizAuthDto);
            log.info("res:{}", res);
        } catch (Exception e) {
            throw new BizRuntimeException("盒子平台授权失败！");
        }
    }


    public List<DoubleMaiDeviceResp> devices(DoubleMaiDeviceReq req, BizAuthDto bizAuthDto) {
        try {
            String res = sendToDoubleMaiRequest.sendRequest(DoubleMaiApiEnum.DEVICE_LIST, null,
                    req, bizAuthDto);

            TypeReference<DoubleMaiBaseResp<List<DoubleMaiDeviceResp>>> typeReference =
                    new TypeReference<DoubleMaiBaseResp<List<DoubleMaiDeviceResp>>>() {
                    };
            // 响应体转换成实体类
            DoubleMaiBaseResp<List<DoubleMaiDeviceResp>> baseResp = objectMapper.readValue(res, typeReference);
            if (baseResp.ok()) {
                return baseResp.getResult();
            }
        } catch (Exception e) {
            throw new BizRuntimeException("盒子平台授权失败！");
        }
        return new ArrayList<>();
    }


    public DtoResult<Integer> addFace(DoubleMaiAddFaceReq req, BizAuthDto bizAuthDto) {
        try {
            HashMap<String, Object> queryMap = new HashMap<>();
            queryMap.put("id", "3");
            String res = sendToDoubleMaiRequest.sendRequest(DoubleMaiApiEnum.FACE_ADD, queryMap,
                    req, bizAuthDto);
            TypeReference<DoubleMaiBaseResp<DoubleMaiAddFaceResp>> typeReference = new TypeReference<DoubleMaiBaseResp<DoubleMaiAddFaceResp>>() {
            };
            // 响应体转换成实体类
            DoubleMaiBaseResp<DoubleMaiAddFaceResp> baseResp = objectMapper.readValue(res, typeReference);
            if (baseResp.ok()) {
                return DtoResult.ok(baseResp.getResult().getFid());
            } else {
                return DtoResult.error("操作失败", baseResp.getReason());
            }
        } catch (Exception e) {
            throw new BizRuntimeException("盒子平台操作失败！");
        }
    }


    public DtoResult<Integer> editFace(DoubleMaiEditFaceReq req, BizAuthDto bizAuthDto) {
        try {
            HashMap<String, Object> queryMap = new HashMap<>();
            queryMap.put("id", "3");
            queryMap.put("fid", req.getFaceId());
            String res = sendToDoubleMaiRequest.sendRequest(DoubleMaiApiEnum.FACE_EDIT, queryMap,
                    req, bizAuthDto);
            TypeReference<DoubleMaiBaseResp<Void>> typeReference = new TypeReference<DoubleMaiBaseResp<Void>>() {
            };
            // 响应体转换成实体类
            DoubleMaiBaseResp<Void> baseResp = objectMapper.readValue(res, typeReference);
            if (baseResp.ok()) {
                return DtoResult.ok();
            } else {
                return DtoResult.error("操作失败", baseResp.getReason());
            }
        } catch (Exception e) {
            throw new BizRuntimeException("盒子平台授权失败！");
        }
    }

    public DtoResult<Integer> delFace(DoubleMaiEditFaceReq req, BizAuthDto bizAuthDto) {
        try {
            HashMap<String, Object> queryMap = new HashMap<>();
            queryMap.put("id", "3");
            queryMap.put("fid", req.getFaceId());
            String res = sendToDoubleMaiRequest.sendRequest(DoubleMaiApiEnum.FACE_DEL, queryMap,
                    req, bizAuthDto);

            TypeReference<DoubleMaiBaseResp<Void>> typeReference = new TypeReference<DoubleMaiBaseResp<Void>>() {
            };
            // 响应体转换成实体类
            DoubleMaiBaseResp<Void> baseResp = objectMapper.readValue(res, typeReference);
            if (baseResp.ok()) {
                return DtoResult.ok();
            } else {
                return DtoResult.error("操作失败", baseResp.getReason());
            }
        } catch (Exception e) {
            throw new BizRuntimeException("盒子平台授权失败！");
        }
    }
}
