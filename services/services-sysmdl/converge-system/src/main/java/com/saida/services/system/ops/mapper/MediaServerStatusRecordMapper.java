package com.saida.services.system.ops.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.saida.services.converge.entity.MediaServerStatusRecordEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 流媒体状态记录
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-11-03 15:02:59
 */
@Mapper
public interface MediaServerStatusRecordMapper extends BaseMapper<MediaServerStatusRecordEntity> {
    List<MediaServerStatusRecordEntity> getList(@Param("entity") MediaServerStatusRecordEntity entity);
}
