package com.saida.services.system.oneFourZero;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Slf4j
@Data
@Component
@ConfigurationProperties("one-four-zero")
@ConditionalOnProperty(prefix = "one-four-zero", name = "enable", havingValue = "true")
public class OneFourZeroConfig {
    private Boolean enable;
    private String host;
    private String token;
}
