//package com.saida.services.system.video.service.impl.v1;
//
//import cn.hutool.core.collection.CollectionUtil;
//import cn.hutool.core.date.DateField;
//import cn.hutool.core.date.DatePattern;
//import cn.hutool.core.date.DateTime;
//import cn.hutool.core.date.DateUtil;
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.saida.services.common.base.DtoResult;
//import com.saida.services.common.base.Result;
//import com.saida.services.common.tools.RedisUtil;
//import com.saida.services.common.tools.StringUtil;
//import com.saida.services.converge.deviceApi.req.ConvSaveOrUpdateCruiseTrackReq;
//import com.saida.services.converge.dto.CtrlPtzPreciseDto;
//import com.saida.services.converge.dto.SubscribeAlarmDto;
//import com.saida.services.converge.entity.CloudStorageEntity;
//import com.saida.services.converge.entity.DeviceEntity;
//import com.saida.services.converge.entity.DeviceRecordPlanEntity;
//import com.saida.services.converge.entity.SignalNodeEntity;
//import com.saida.services.converge.entity.dto.StreamUrlDto;
//import com.saida.services.converge.enums.AccessWayType;
//import com.saida.services.converge.qxNode.QxNodeApiEnum;
//import com.saida.services.converge.qxNode.req.channels.DeviceChannelsLiveReq;
//import com.saida.services.converge.qxNode.req.channels.StopLiveReq;
//import com.saida.services.converge.qxNode.req.device.*;
//import com.saida.services.converge.qxNode.req.gb2022.*;
//import com.saida.services.converge.qxNode.req.ptz.DevicePreListReq;
//import com.saida.services.converge.qxNode.req.ptz.DevicePreSetJumpReq;
//import com.saida.services.converge.qxNode.req.ptz.DevicePreSetReq;
//import com.saida.services.converge.qxNode.req.ptz.DevicePtzCmdReq;
//import com.saida.services.converge.qxNode.resp.*;
//import com.saida.services.converge.qxNode.resp.device.ChannelRecordTimeLineResp;
//import com.saida.services.converge.qxNode.resp.gb2022.DeviceConfigResp;
//import com.saida.services.converge.qxNode.resp.gb2022.HomePositionResp;
//import com.saida.services.converge.qxNode.resp.ptz.DevicePreListResp;
//import com.saida.services.converge.qxNode.resp.qx.GetCruiseTrackListResp;
//import com.saida.services.converge.qxNode.resp.qx.GetCruiseTrackResp;
//import com.saida.services.converge.qxNode.resp.qx.GetPtzPreciseResp;
//import com.saida.services.converge.qxNode.resp.qx.SDCardInfoResp;
//import com.saida.services.converge.qxNode.resp.sd.ConvergeLightingArgResp;
//import com.saida.services.converge.qxNode.resp.sd.ConvergeSdCardCapacityResp;
//import com.saida.services.converge.qxNode.resp.sd.ConvergeVideoResp;
//import com.saida.services.converge.vo.DownloadLocalPlaybackVo;
//import com.saida.services.deviceApi.req.*;
//import com.saida.services.deviceApi.resp.*;
//import com.saida.services.exception.BizRuntimeException;
//import com.saida.services.open.resp.*;
//import com.saida.services.open.resp.rtc.VlinkerConvergeRtcConnectResp;
//import com.saida.services.system.JuLongMqtt.dto.message.req.GetAlgorithmCfgRequest;
//import com.saida.services.system.JuLongMqtt.dto.message.req.GetFaceAreaCfgRequest;
//import com.saida.services.system.JuLongMqtt.dto.message.req.SetAlgorithmCfgRequest;
//import com.saida.services.system.JuLongMqtt.dto.message.req.SetFaceAreaCfgRequest;
//import com.saida.services.system.JuLongMqtt.dto.message.resp.GetAlgorithmCfgResponse;
//import com.saida.services.system.JuLongMqtt.dto.message.resp.GetFaceAreaCfgResponse;
//import com.saida.services.system.JuLongMqtt.dto.message.resp.SetAlgorithmCfgResponse;
//import com.saida.services.system.JuLongMqtt.dto.message.resp.SetFaceAreaCfgResponse;
//import com.saida.services.system.JuLongMqtt.mqtt.MqttProviderConfig;
//import com.saida.services.system.client.nodev1.QxNodeReqService;
//import com.saida.services.system.client.nodev1.QxNodeReqUtil;
//import com.saida.services.system.client.nodev1.ResponseDto;
//import com.saida.services.system.ops.service.CloudStorageService;
//import com.saida.services.system.ops.service.DeviceRecordPlanService;
//import com.saida.services.system.ops.service.SignalNodeService;
//import com.saida.services.system.video.param.*;
//import com.saida.services.system.video.param.CtrlHomePositionReq;
//import com.saida.services.system.video.service.VideoSdkService;
//import com.saida.services.system.video.vo.*;
//import groovy.lang.Lazy;
//import lombok.extern.slf4j.Slf4j;
//import org.eclipse.paho.client.mqttv3.MqttMessage;
//import org.springframework.stereotype.Service;
//
//import javax.annotation.Resource;
//import java.util.*;
//import java.util.concurrent.Executors;
//import java.util.concurrent.ScheduledExecutorService;
//import java.util.concurrent.TimeUnit;
//import java.util.concurrent.atomic.AtomicReference;
//import java.util.stream.Collectors;
//
///**
// * <AUTHOR>
// * @ClassName VideoGbSdkServiceImpl
// * @Desc
// * @Date 2024/11/13 16:26
// */
//@Slf4j
//@Service
//public class VideoNodeV1ServiceImpl implements VideoSdkService {
//
//    @Lazy
//    @Resource
//    private SignalNodeService signalNodeService;
//    @Lazy
//    @Resource
//    private QxNodeReqUtil qxNodeReqUtil;
//    @Lazy
//    @Resource
//    private QxNodeReqService qxNodeReqService;
//
//
//    @Override
//    public DtoResult<VlinkerConvergeAddDeviceResp> addDevice(DeviceEntity entity, SignalNodeEntity signalNode) {
//        AddDeviceReq addDeviceReq = new AddDeviceReq();
//        AccessWayType type = AccessWayType.getType(entity.getAccessWay());
//        if (type == null) {
//            return DtoResult.error("接入方式错误");
//        }
//        addDeviceReq.setProtocol(type.getCode());
//        addDeviceReq.setCode(entity.getDeviceCode());
//        addDeviceReq.setName(entity.getName());
//        addDeviceReq.setRemark(entity.getName());
//        addDeviceReq.setIp(entity.getIp());
//        addDeviceReq.setPort(entity.getPort());
//        addDeviceReq.setIsQuantum(Objects.nonNull(entity.getIsQuantum()) && entity.getIsQuantum() == 1);
//        addDeviceReq.setUsername(entity.getUsername());
//        addDeviceReq.setPassword(entity.getPassword());
//        addDeviceReq.setIsWhite(entity.getIsWhite() != null && entity.getIsWhite() == 2);
//        ResponseDto responseDto = qxNodeReqUtil.sendRequest(QxNodeApiEnum.ADD_DEVICE, signalNode, addDeviceReq);
//        if (responseDto.getHttpCode() != 200) {
//            return DtoResult.error(QxNodeApiEnum.ADD_DEVICE.getDes() + "失败");
//        }
//        return DtoResult.ok();
//    }
//
//    @Override
//    public DtoResult<VlinkerConvergeAddDeviceResp> delDevice(DeviceEntity entity, SignalNodeEntity signalNode) {
//        List<String> collect = CollectionUtil.newArrayList(entity.getDeviceCode());
//        DelDeviceReq req = new DelDeviceReq();
//        req.setIds(collect);
//        ResponseDto responseDto = qxNodeReqUtil.sendRequest(QxNodeApiEnum.DEL_DEVICE, signalNode, req);
//        if (responseDto.getHttpCode() != 200) {
//            return DtoResult.error(QxNodeApiEnum.DEL_DEVICE.getDes() + "失败");
//        }
//        return DtoResult.ok();
//    }
//
//    @Override
//    public DtoResult<StreamUrlDto> getStreamUrl(GetStreamUrlParamVideoNode param) {
//        SignalNodeEntity node = signalNodeService.getById(param.getDeviceEntity().getNodeId());
//        if (node == null) {
//            return DtoResult.error("节点不存在");
//        }
//        if (param.getPlayType() == null) {
//            param.setPlayType(1);
//        }
//        DeviceChannelsLiveReq deviceChannelsLiveReq = new DeviceChannelsLiveReq();
//        deviceChannelsLiveReq.setDeviceId(param.getDeviceEntity().getDeviceCode());
//        deviceChannelsLiveReq.setChannelId(param.getChannelId());
//        deviceChannelsLiveReq.setStream(param.getPlayType() == 1 ? "MAIN" : "SUB");
//        deviceChannelsLiveReq.setActiveSecond(param.getActiveSecond());
//        ResponseDto responseDto = qxNodeReqUtil.sendRequest(QxNodeApiEnum.GET_STREAM_URL, node, deviceChannelsLiveReq);
//        if (responseDto.getHttpCode() != 200) {
//            return DtoResult.error(QxNodeApiEnum.GET_STREAM_URL.getDes() + "失败", responseDto.getMsg());
//        }
//        StreamUrl streamUrl = JSON.parseObject(responseDto.getRes(), StreamUrl.class);
//        // 构建并返回包含直播URL的DTO
//        StreamUrlDto streamUrlDto = new StreamUrlDto();
//        streamUrlDto.setHls(streamUrl.getAddress().getHls());
//        streamUrlDto.setRtmp(streamUrl.getAddress().getRtmp());
//        streamUrlDto.setRtsp(streamUrl.getAddress().getRtsp());
//        streamUrlDto.setWsFlv(streamUrl.getAddress().getWs_flv());
//        streamUrlDto.setWebrtc(streamUrl.getAddress().getWebrtc());
//        streamUrlDto.setHttpFlv(streamUrl.getAddress().getHttp_flv());
//        return DtoResult.ok(streamUrlDto);
//    }
//
//    @Override
//    public DtoResult<Void> stopLive(VideoNodeBaseParam param) {
//        // 根据设备ID查询信号节点信息
//        SignalNodeEntity node = signalNodeService.getById(param.getDeviceEntity().getNodeId());
//        // 如果设备未绑定信号节点，则返回错误信息
//        if (node == null) {
//            return DtoResult.error("设备未绑定节点");
//        }
//        ResponseDto responseDto = qxNodeReqUtil.sendRequest(QxNodeApiEnum.CHANNEL_STOP_LIVE, node,
//                StopLiveReq.builder().channelId(param.getChannelId())
//                        .build());
//        if (responseDto.getHttpCode() != 200) {
//            return DtoResult.error(QxNodeApiEnum.CHANNEL_STOP_LIVE.getDes() + "失败", responseDto.getMsg());
//        }
//        return DtoResult.ok();
//    }
//
//    @Override
//    public DtoResult<VlinkerConvergeSnapshotResp> getSnapshot(GetSnapshotParam param) {
//        SignalNodeEntity node = signalNodeService.getById(param.getDeviceEntity().getNodeId());
//        try {
//            if (param.getGenerateFlag() == null) {
//                param.setGenerateFlag(false);
//            }
//            if (param.getGenerateFlag() && param.getTimes() == null) {
//                param.setTimes(1);
//            }
//            SnapshotReq snapshotReq = new SnapshotReq();
//            snapshotReq.setDeviceId(param.getDeviceEntity().getDeviceCode());
//            snapshotReq.setChannelId(param.getChannelId());
//            snapshotReq.setTimeS(param.getTimes());
//            snapshotReq.setGenerateFlag(param.getGenerateFlag());
//            ResponseDto responseDto = qxNodeReqUtil.sendRequest(QxNodeApiEnum.DEVICE_SNAPSHOT, node, snapshotReq);
//            if (responseDto.getHttpCode() != 200) {
//                return DtoResult.error(QxNodeApiEnum.DEVICE_SNAPSHOT.getDes() + "失败", responseDto.getMsg());
//            }
//            DeviceSnapshotSuperResp resp = JSON.parseObject(responseDto.getRes(), DeviceSnapshotSuperResp.class);
//
//            VlinkerConvergeSnapshotResp convergeSnapshotResp = new VlinkerConvergeSnapshotResp();
//            convergeSnapshotResp.setImg(resp.getImg());
//            convergeSnapshotResp.setType("image/jpeg");
//            convergeSnapshotResp.setCreated_at(resp.getCreated_at());
//            return DtoResult.ok(convergeSnapshotResp);
//        } catch (Exception e) {
//            log.error("获取设备快照失败....msg:{}", e.getMessage(), e);
//        }
//        return DtoResult.error("获取设备快照失败");
//    }
//
//    @Override
//    public DtoResult<VlinkerConvergeRtcConnectResp> getVoiceUrl(GetVoiceUrlParam param) {
//        // 根据设备ID获取信号节点信息
//        SignalNodeEntity node = signalNodeService.getById(param.getDeviceEntity().getNodeId());
//        if (node == null) {
//            return DtoResult.error("设备未绑定节点！");
//        }
//        DeviceTalkConnResp deviceTalkConnResp = null;
//        try {
//            // 尝试获取GB直播地址
//            deviceTalkConnResp = qxNodeReqService.deviceGbTalk(node, param.getChannelId());
//        } catch (Exception e) {
//            log.error("获取GB直播地址失败", e);
//            return DtoResult.error("获取GB直播地址失败");
//        }
//        // 构建并返回GB类型的RTC连接响应
//        return DtoResult.ok(VlinkerConvergeRtcConnectResp.builder()
//                .type("gb")
//                .wsUrl(deviceTalkConnResp.getTalk_url())
//                .transport(deviceTalkConnResp.getTransport())
//                .build());
//    }
//
//    @Resource
//    private DeviceRecordPlanService deviceRecordPlanService;
//    @Resource
//    private CloudStorageService cloudStorageService;
//
//    @Override
//    public DtoResult<LinkedHashMap<String, Integer>> getChannelRecordMonths(GetChannelRecordMonthsParamVideoNode param) {
//
//        SignalNodeEntity node = signalNodeService.getById(param.getDeviceEntity().getNodeId());
//        if (node == null) {
//            return DtoResult.error("节点不存在");
//        }
//        //source     录像源：云存（CLOUD）或本地存储（LOCAL）
//        if ("CLOUD".equals(param.getSource())) {
//            //查询s3
//            DeviceRecordPlanEntity one = deviceRecordPlanService.getOne(new LambdaQueryWrapper<DeviceRecordPlanEntity>()
//                    .eq(DeviceRecordPlanEntity::getDeviceId, param.getDeviceId())
//                    .eq(DeviceRecordPlanEntity::getChannelId, param.getChannelId()), false);
//            if (one == null) {
//                return DtoResult.error("录像计划不存在！");
//            }
//            CloudStorageEntity cloudStorageEntity = cloudStorageService.getById(one.getStorageId());
//            if (cloudStorageEntity == null) {
//                return DtoResult.error("云存储不存在！");
//            }
//        }
//        GetChannelRecordMonthsReq getChannelRecordTimeLineReq = new GetChannelRecordMonthsReq();
//        getChannelRecordTimeLineReq.setChannelId(param.getChannelId());
//        getChannelRecordTimeLineReq.setSource(param.getSource());
//        getChannelRecordTimeLineReq.setDates(param.getDates());
//        ResponseDto responseDto = qxNodeReqUtil.sendRequest(QxNodeApiEnum.GET_CHANNEL_RECORD_MONTHS, node, getChannelRecordTimeLineReq);
//        if (responseDto.getHttpCode() != 200) {
//            return DtoResult.error(QxNodeApiEnum.GET_CHANNEL_RECORD_MONTHS.getDes() + "失败", responseDto.getMsg());
//        }
//        JSONObject resp = JSON.parseObject(responseDto.getRes(), JSONObject.class);
//        if (!resp.containsKey(param.getDates())) {
//            return DtoResult.error("获取月录像失败");
//        }
//        String string = resp.getString(param.getDates());
//        char[] charArray = string.toCharArray();
//        LinkedHashMap<String, Integer> linkedHashMap = new LinkedHashMap<>();
//        DateTime nowDate = DateTime.now();
//        DateTime parse = DateUtil.parse(param.getDates() + "01", DatePattern.PURE_DATE_PATTERN);
//        List<DateTime> dateTimes = DateUtil.rangeToList(DateUtil.beginOfMonth(parse), DateUtil.endOfMonth(parse), DateField.DAY_OF_MONTH);
//        dateTimes.forEach(date -> {
//            if (date.isAfter(nowDate)) {
//                linkedHashMap.put(date.toString("yyyy-MM-dd"), 0);
//            } else {
//                int i = DateUtil.dayOfMonth(date);
//                if (i > charArray.length) {
//                    linkedHashMap.put(date.toString("yyyy-MM-dd"), 0);
//                } else {
//                    char c = charArray[i - 1];
//                    if (c == '1') {
//                        linkedHashMap.put(date.toString("yyyy-MM-dd"), 1);
//                    } else {
//                        linkedHashMap.put(date.toString("yyyy-MM-dd"), 0);
//                    }
//                }
//            }
//        });
//        return DtoResult.ok(linkedHashMap);
//    }
//
//    @Override
//    public DtoResult<List<ChannelRecordTimeLine>> getChannelRecordTimeLine(GetChannelRecordTimeLineParamVideoNode param) {
//        SignalNodeEntity node = signalNodeService.getById(param.getDeviceEntity().getNodeId());
//        if (node == null) {
//            return DtoResult.error("节点不存在");
//        }
//        GetChannelRecordTimeLineReq getChannelRecordTimeLineReq = new GetChannelRecordTimeLineReq();
//        getChannelRecordTimeLineReq.setChannelId(param.getChannelId());
//        getChannelRecordTimeLineReq.setStart(param.getStart());
//        getChannelRecordTimeLineReq.setEnd(param.getEnd());
//        getChannelRecordTimeLineReq.setSource(param.getSource());
//        getChannelRecordTimeLineReq.setSsrc(param.getUuid());
//        ResponseDto responseDto = qxNodeReqUtil.sendRequest(QxNodeApiEnum.GET_CHANNEL_RECORD_TIME_LINE, node, getChannelRecordTimeLineReq);
//        if (responseDto.getHttpCode() != 200) {
//            return DtoResult.error(QxNodeApiEnum.GET_CHANNEL_RECORD_TIME_LINE.getDes() + "失败", responseDto.getMsg());
//        }
//        ChannelRecordTimeLineResp resp = JSON.parseObject(responseDto.getRes(), ChannelRecordTimeLineResp.class);
//        List<ChannelRecordTimeLine> recordTimeLines = resp.getItems();
//        if (CollectionUtil.isNotEmpty(recordTimeLines)) {
//            recordTimeLines.forEach(e -> e.setAccessWay(param.getDeviceEntity().getAccessWay()));
//            // 步骤1: 按照时间戳进行排序
//            recordTimeLines.sort(Comparator.comparing(ChannelRecordTimeLine::getStart));
//
//            //卡存不拼接
//            if ("LOCAL".equals(param.getSource())) {
//                return DtoResult.ok(recordTimeLines);
//            }
//
//            List<ChannelRecordTimeLine> recordTimeLineList = new ArrayList<>();
//            ChannelRecordTimeLine channelRecordTimeLine = null;
//
//            // 步骤2: 遍历排序后的数据，合并时间间隔在2秒之内的数据
//            for (ChannelRecordTimeLine data : recordTimeLines) {
//                if (channelRecordTimeLine == null || (data.getStart() - channelRecordTimeLine.getStart() - channelRecordTimeLine.getDuration()) > 3000) {
//                    // 开始新的时间段
//                    if (channelRecordTimeLine != null) {
//                        recordTimeLineList.add(channelRecordTimeLine);
//                    }
//                    channelRecordTimeLine = new ChannelRecordTimeLine();
//                    channelRecordTimeLine.setStart(data.getStart());
//                    channelRecordTimeLine.setDuration(data.getDuration());
//                    channelRecordTimeLine.setAccessWay(data.getAccessWay());
//                    channelRecordTimeLine.setMedia_id(data.getMedia_id());
//
//                } else {
//                    // 合并时间间隔在2秒之内的数据
//                    channelRecordTimeLine.setDuration((data.getStart() - channelRecordTimeLine.getStart()) + data.getDuration());
//                }
//            }
//            // 处理最后一组数据
//            if (channelRecordTimeLine != null) {
//                recordTimeLineList.add(channelRecordTimeLine);
//            }
//
//            return DtoResult.ok(recordTimeLineList);
//        }
//        return DtoResult.ok(recordTimeLines);
//    }
//
//
//    @Override
//    public DtoResult<ChannelRecordUrlResp> getChannelRecordUrlByCloud(GetChannelRecordTimeLineParamVideoNode param) {
//        if ("LOCAL".equals(param.getSource()) && StringUtil.isBlank(param.getUuid())) {
//            return DtoResult.error("uuid不能为空");
//        }
//        SignalNodeEntity node = signalNodeService.getById(param.getDeviceEntity().getNodeId());
//        if (node == null) {
//            return DtoResult.error("节点不存在");
//        }
//        GetChannelRecordUrlReq getChannelRecordUrlReq = new GetChannelRecordUrlReq();
//        getChannelRecordUrlReq.setChannelId(param.getChannelId());
//        getChannelRecordUrlReq.setStart(param.getStart());
//        getChannelRecordUrlReq.setEnd(param.getEnd());
//        getChannelRecordUrlReq.setSource(param.getSource());
//        getChannelRecordUrlReq.setSsrc(param.getUuid());
//        ResponseDto responseDto = qxNodeReqUtil.sendRequest(QxNodeApiEnum.GET_CHANNEL_RECORD_URL, node, getChannelRecordUrlReq);
//        if (responseDto.getHttpCode() != 200) {
//            return DtoResult.error(QxNodeApiEnum.GET_CHANNEL_RECORD_URL.getDes() + "失败", responseDto.getMsg());
//        }
//        ChannelRecordUrlResp urlResp = JSON.parseObject(responseDto.getRes(), ChannelRecordUrlResp.class);
//        if (urlResp == null) {
//            return DtoResult.error("获取播放地址失败");
//        }
//        urlResp.setAccessWay(param.getDeviceEntity().getAccessWay());
//        return DtoResult.ok(urlResp);
//    }
//
//    @Override
//    public DtoResult<ChannelRecordUrlResp> getChannelRecordUrlByLocal(GetChannelRecordTimeLineParamVideoNode param) {
//        SignalNodeEntity node = signalNodeService.getById(param.getDeviceEntity().getNodeId());
//        if (node == null) {
//            return DtoResult.error("节点不存在");
//        }
//        GetChannelRecordUrlReq getChannelRecordUrlReq = new GetChannelRecordUrlReq();
//        getChannelRecordUrlReq.setChannelId(param.getChannelId());
//        getChannelRecordUrlReq.setStart(param.getStart());
//        getChannelRecordUrlReq.setEnd(param.getEnd());
//        getChannelRecordUrlReq.setSource(param.getSource());
//        getChannelRecordUrlReq.setSsrc(param.getUuid());
//        ResponseDto responseDto = qxNodeReqUtil.sendRequest(QxNodeApiEnum.GET_CHANNEL_RECORD_URL, node, getChannelRecordUrlReq);
//        if (responseDto.getHttpCode() != 200) {
//            return DtoResult.error(QxNodeApiEnum.GET_CHANNEL_RECORD_URL.getDes() + "失败", responseDto.getMsg());
//        }
//        ChannelRecordUrlResp urlResp = JSON.parseObject(responseDto.getRes(), ChannelRecordUrlResp.class);
//        if (urlResp == null) {
//            return DtoResult.error("获取播放地址失败");
//        }
//        urlResp.setAccessWay(param.getDeviceEntity().getAccessWay());
//        return DtoResult.ok(urlResp);
//    }
//
//    @Override
//    public DtoResult<Void> controlPlayback(ControlPlaybackParamVideoNode dto) {
//        return DtoResult.error("暂不支持！");
//    }
//
//    @Override
//    public DtoResult<DownloadLocalPlaybackVo> downloadPlayback(DownloadPlaybackParamVideoNode param) {
//        // 根据设备ID查询信号节点信息
//        SignalNodeEntity node = signalNodeService.getById(param.getDeviceEntity().getNodeId());
//        if (node == null) {
//            return DtoResult.error("设备未绑定节点！");
//        }
//        String ssrc = param.getUuid();
//
//        GetChannelRecordDownloadReq getChannelRecordDownloadReq = new GetChannelRecordDownloadReq();
//        getChannelRecordDownloadReq.setChannelId(param.getChannelId());
//        getChannelRecordDownloadReq.setStart(param.getStartTime());
//        getChannelRecordDownloadReq.setEnd(param.getEndTime());
//        getChannelRecordDownloadReq.setSsrc(ssrc);
//        ResponseDto responseDto = qxNodeReqUtil.sendRequest(QxNodeApiEnum.DOWNLOAD_PLAYBACK, node, getChannelRecordDownloadReq);
//        if (responseDto.getHttpCode() != 200) {
//            return DtoResult.error(QxNodeApiEnum.DOWNLOAD_PLAYBACK.getDes() + "失败");
//        }
//        GbDownloadPlaybackResp resp = JSON.parseObject(responseDto.getRes(), GbDownloadPlaybackResp.class);
//        if (resp == null) {
//            return DtoResult.error();
//        }
//        DownloadLocalPlaybackVo localPlaybackVo = new DownloadLocalPlaybackVo();
//        localPlaybackVo.setChannelId(resp.getChannelId());
//        localPlaybackVo.setEventUrl(resp.getEventUrl());
//        localPlaybackVo.setDownloadUrl(resp.getDownloadUrl());
//        return DtoResult.ok(localPlaybackVo);
//    }
//
//    @Override
//    public DtoResult<List<VlinkerConvergePreSetListResp>> preSetList(VideoNodeBaseParam param) {
//        // 根据设备ID查询信号节点信息
//        SignalNodeEntity node = signalNodeService.getById(param.getDeviceEntity().getNodeId());
//        if (node == null) {
//            return DtoResult.error("设备未绑定节点！");
//        }
//        DevicePreListReq devicePreListReq = new DevicePreListReq();
//        devicePreListReq.setDeviceId(param.getDeviceEntity().getDeviceCode());
//        devicePreListReq.setChannelId(param.getChannelId());
//        ResponseDto responseDto = qxNodeReqUtil.sendRequest(QxNodeApiEnum.PRE_SET_LIST, node, devicePreListReq);
//        if (responseDto.getHttpCode() != 200) {
//            return DtoResult.error(QxNodeApiEnum.PRE_SET_LIST.getDes() + "失败", responseDto.getMsg());
//        }
//        DevicePreListResp preSetRespList = JSON.parseObject(responseDto.getRes(), DevicePreListResp.class);
//        // 转换预置点位列表为响应对象列表
//        List<VlinkerConvergePreSetListResp> preSetListResp = new ArrayList<>();
//        if (preSetRespList.getTotal() == 0) {
//            return DtoResult.ok(preSetListResp);
//        }
//        List<DevicePreListResp.Item> items = preSetRespList.getItems();
//        if (CollectionUtil.isEmpty(items)) {
//            return DtoResult.ok(preSetListResp);
//        }
//        items.forEach(e -> {
//            VlinkerConvergePreSetListResp a = new VlinkerConvergePreSetListResp();
//            a.setIndex(e.getIndex());
//            a.setName(e.getName());
//            a.setEnabled(e.getEnabled());
//            preSetListResp.add(a);
//        });
//        // 返回成功响应，包含转换后的预置点位列表
//        return DtoResult.ok(preSetListResp);
//    }
//
//    @Override
//    public DtoResult<Void> preSet(PreSetParamVideoNode req) {
//
//        // 根据设备ID查询信号节点信息
//        SignalNodeEntity node = signalNodeService.getById(req.getDeviceEntity().getNodeId());
//        if (node == null) {
//            return DtoResult.error("设备未绑定节点！");
//        }
//        // 调用预置点位设置服务
//        DevicePreSetReq devicePreSetReq = new DevicePreSetReq();
//        devicePreSetReq.setDeviceId(req.getDeviceEntity().getDeviceCode());
//        devicePreSetReq.setChannelId(req.getChannelId());
//        devicePreSetReq.setIndex(req.getIndex());
//        devicePreSetReq.setName(req.getName());
//        ResponseDto responseDto = qxNodeReqUtil.sendRequest(QxNodeApiEnum.PRE_SET, node, devicePreSetReq);
//        if (responseDto.getHttpCode() != 200) {
//            return DtoResult.error(QxNodeApiEnum.PRE_SET.getDes() + "失败", responseDto.getMsg());
//        }
//        // 返回操作成功的结果
//        return DtoResult.ok();
//    }
//
//    @Override
//    public DtoResult<VlinkerConvergeGetPtzResp> getPtz(VideoNodeBaseParam videoNodeBaseParam) {
//        // 根据设备ID获取信号节点实体
//        SignalNodeEntity node = signalNodeService.getById(videoNodeBaseParam.getDeviceEntity().getNodeId());
//        if (node == null) {
//            return DtoResult.error("设备未绑定节点！");
//        }
//        // 调用QXNodeReqService获取PTZ精确位置信息
//        GetPtzPreciseReq getPtzPreciseReq = new GetPtzPreciseReq();
//        getPtzPreciseReq.setDeviceId(videoNodeBaseParam.getDeviceEntity().getDeviceCode());
//        getPtzPreciseReq.setChannelId(videoNodeBaseParam.getChannelId());
//        ResponseDto responseDto = qxNodeReqUtil.sendRequest(QxNodeApiEnum.GET_PTZ_PRECISE, node, getPtzPreciseReq);
//        if (responseDto.getHttpCode() != 200) {
//            return DtoResult.error(QxNodeApiEnum.GET_PTZ_PRECISE.getDes() + "失败");
//        }
//        GetPtzPreciseResp resp = JSON.parseObject(responseDto.getRes(), GetPtzPreciseResp.class);
//        VlinkerConvergeGetPtzResp VlinkerConvergeGetPtzResp = new VlinkerConvergeGetPtzResp();
//        VlinkerConvergeGetPtzResp.setPan(resp.getPan());
//        VlinkerConvergeGetPtzResp.setTilt(resp.getTilt());
//        VlinkerConvergeGetPtzResp.setZoom(resp.getZoom());
//        VlinkerConvergeGetPtzResp.setHorizontalFieldAngle(resp.getHorizontalFieldAngle());
//        VlinkerConvergeGetPtzResp.setVerticalFieldAngle(resp.getVerticalFieldAngle());
//        VlinkerConvergeGetPtzResp.setMaxViewDistance(resp.getMaxViewDistance());
//        return DtoResult.ok(VlinkerConvergeGetPtzResp);
//    }
//
//    @Override
//    public DtoResult<Void> setPtz(SetPTZParamVideoNode videoNodeBaseParam) {
//
//        // 根据设备ID获取信号节点实体
//        SignalNodeEntity node = signalNodeService.getById(videoNodeBaseParam.getDeviceEntity().getNodeId());
//        if (node == null) {
//            return DtoResult.error("设备未绑定节点！");
//        }
//
//        // 调用服务进行精确PTZ控制
//        CtrlPtzPreciseReq ctrlPtzPreciseReq = new CtrlPtzPreciseReq();
//        ctrlPtzPreciseReq.setDeviceId(videoNodeBaseParam.getDeviceEntity().getDeviceCode());
//        ctrlPtzPreciseReq.setChannelId(videoNodeBaseParam.getChannelId());
//        ctrlPtzPreciseReq.setP(videoNodeBaseParam.getPan());
//        ctrlPtzPreciseReq.setT(videoNodeBaseParam.getTilt());
//        ctrlPtzPreciseReq.setZ(videoNodeBaseParam.getZoom());
//        ResponseDto ctrlCruiseTrackListResponseDto = qxNodeReqUtil.sendRequest(QxNodeApiEnum.CTRL_PTZ_PRECISE, node, ctrlPtzPreciseReq);
//        if (ctrlCruiseTrackListResponseDto.getHttpCode() != 200) {
//            return DtoResult.error(QxNodeApiEnum.CTRL_PTZ_PRECISE.getDes() + "异常", ctrlCruiseTrackListResponseDto.getMsg());
//        }
//        // 返回操作成功的结果
//        return DtoResult.ok();
//    }
//
//    @Override
//    public DtoResult<VlinkerConvergeHomePositionResp> getHomePosition(CtrlHomePositionReq req) {
//        // 根据设备ID查询信号节点信息
//        SignalNodeEntity node = signalNodeService.getById(req.getDeviceEntity().getNodeId());
//        if (node == null) {
//            return DtoResult.error("设备未绑定节点！");
//        }
//        if (StringUtil.isEmpty(req.getChannelId())) {
//            return DtoResult.error("通道id不存在");
//        }
//        DevicePreListReq devicePreListReq = new DevicePreListReq();
//        devicePreListReq.setDeviceId(req.getDeviceCode());
//        devicePreListReq.setChannelId(req.getChannelId());
//
//        ResponseDto responseDtoHome = qxNodeReqUtil.sendRequest(QxNodeApiEnum.GET_HOME_POSITION, node, devicePreListReq);
//        if (responseDtoHome.getHttpCode() != 200) {
//            return DtoResult.error(QxNodeApiEnum.GET_HOME_POSITION.getDes() + "失败", responseDtoHome.getMsg());
//        }
//        HomePositionResp homePosition = JSON.parseObject(responseDtoHome.getRes(), HomePositionResp.class);
//        VlinkerConvergeHomePositionResp resp = new VlinkerConvergeHomePositionResp();
//        if (homePosition != null && homePosition.getHomePosition() != null) {
//            resp.setEnabled(homePosition.getHomePosition().getEnable());
//            resp.setResetTime(homePosition.getHomePosition().getResetTime());
//            resp.setPresetIndex(homePosition.getHomePosition().getPresetIndex());
//        }
//        return DtoResult.ok(resp);
//    }
//
//    @Override
//    public DtoResult<VlinkerConvergeHomePositionResp> setHomePosition(CtrlHomePositionReq req) {
//        if (req.getPresetIndex() == null) {
//            return DtoResult.error("预置点位必传");
//        }
//        // 根据设备ID查询信号节点信息
//        SignalNodeEntity node = signalNodeService.getById(req.getDeviceEntity().getNodeId());
//        if (node == null) {
//            return DtoResult.error("设备未绑定节点！");
//        }
//        if (StringUtil.isEmpty(req.getChannelId())) {
//            return DtoResult.error("通道id不存在");
//        }
//
//        com.saida.services.converge.qxNode.req.gb2022.CtrlHomePositionReq ctrlHomePositionReq = new com.saida.services.converge.qxNode.req.gb2022.CtrlHomePositionReq();
//        ctrlHomePositionReq.setDeviceId(req.getDeviceCode());
//        ctrlHomePositionReq.setChannelId(req.getChannelId());
//        ctrlHomePositionReq.setEnabled(req.getEnabled());
//        ctrlHomePositionReq.setResetTime(req.getResetTime());
//        ctrlHomePositionReq.setPresetIndex(req.getPresetIndex());
//
//        ResponseDto responseDtoHome = qxNodeReqUtil.sendRequest(QxNodeApiEnum.CTRL_HOME_POSITION, node, ctrlHomePositionReq);
//        if (responseDtoHome.getHttpCode() != 200) {
//            return DtoResult.error(QxNodeApiEnum.GET_HOME_POSITION.getDes() + "失败", responseDtoHome.getMsg());
//        }
//        HomePositionResp homePosition = JSON.parseObject(responseDtoHome.getRes(), HomePositionResp.class);
//        VlinkerConvergeHomePositionResp resp = new VlinkerConvergeHomePositionResp();
//        if (homePosition != null && homePosition.getHomePosition() != null) {
//            resp.setEnabled(homePosition.getHomePosition().getEnable());
//            resp.setResetTime(homePosition.getHomePosition().getResetTime());
//            resp.setPresetIndex(homePosition.getHomePosition().getPresetIndex());
//        }
//        return DtoResult.ok(resp);
//    }
//
//    @Override
//    public DtoResult<Void> preSetJump(PreSetParamVideoNode req) {
//        // 根据设备ID获取关联的信号节点实体
//        SignalNodeEntity node = signalNodeService.getById(req.getDeviceEntity().getNodeId());
//        if (node == null) {
//            return DtoResult.error("设备未绑定节点！");
//        }
//        // 调用预置点位跳转服务
//
//        DevicePreSetJumpReq devicePreSetJumpReq = new DevicePreSetJumpReq();
//        devicePreSetJumpReq.setDeviceId(req.getDeviceEntity().getDeviceCode());
//        devicePreSetJumpReq.setChannelId(req.getChannelId());
//        devicePreSetJumpReq.setIndex(req.getIndex());
//        ResponseDto responseDto = qxNodeReqUtil.sendRequest(QxNodeApiEnum.PRE_SET_JUMP, node, devicePreSetJumpReq);
//        if (responseDto.getHttpCode() != 200) {
//            return DtoResult.error(QxNodeApiEnum.PRE_SET_JUMP.getDes() + "失败");
//        }
//        return DtoResult.ok();
//    }
//
//    @Override
//    public DtoResult<Void> preSetDelete(PreSetParamVideoNode req) {
//        return DtoResult.error("暂不支持！");
//    }
//
//    @Override
//    public DtoResult<Void> ptzCmd(PtzCmdParamVideoNode req) {
//        // 根据设备序列号获取信号节点信息
//        SignalNodeEntity node = signalNodeService.getById(req.getDeviceEntity().getNodeId());
//        if (node == null) {
//            return DtoResult.error("信令设备不存在");
//        }
//        // 根据请求的动作执行相应的云台控制操作
//        DevicePtzCmdReq devicePtzCmdReq = new DevicePtzCmdReq();
//
//        switch (req.getAction()) {
//            case 1:
//                // 启动云台控制
//                devicePtzCmdReq.setDeviceId(req.getDeviceEntity().getDeviceCode());
//                devicePtzCmdReq.setChannelId(req.getChannelId());
//                devicePtzCmdReq.setSpeed(req.getSpeed());
//                devicePtzCmdReq.setDirection(req.getDirection());
//                ResponseDto responseDto = qxNodeReqUtil.sendRequest(QxNodeApiEnum.PTZ_START, node, devicePtzCmdReq);
//                if (responseDto.getHttpCode() != 200) {
//                    return DtoResult.error(QxNodeApiEnum.PTZ_START.getDes() + "失败", responseDto.getMsg());
//                }
//                break;
//            case 0:
//                // 停止云台控制
//                devicePtzCmdReq.setDeviceId(req.getDeviceEntity().getDeviceCode());
//                devicePtzCmdReq.setChannelId(req.getChannelId());
//                devicePtzCmdReq.setSpeed(req.getSpeed());
//                devicePtzCmdReq.setDirection(req.getDirection());
//                responseDto = qxNodeReqUtil.sendRequest(QxNodeApiEnum.PTZ_STOP, node, devicePtzCmdReq);
//                if (responseDto.getHttpCode() != 200) {
//                    return DtoResult.error(QxNodeApiEnum.PTZ_STOP.getDes() + "失败", responseDto.getMsg());
//                }
//                break;
//        }
//        // 操作成功，返回空结果
//        return DtoResult.ok();
//    }
//
//    @Override
//    public Result getPtzPrecise(VideoNodeBaseParam param) {
//        return Result.error("暂不支持！");
//    }
//
//    @Override
//    public DtoResult<VlinkerConvergeCruiseTrackResp> getCruiseTrack(CruiseTrackNodeParam paramNode) {
//        // 根据设备ID查询信号节点信息
//        SignalNodeEntity node = signalNodeService.getById(paramNode.getDeviceEntity().getNodeId());
//        if (node == null) {
//            return DtoResult.error("设备未绑定节点！");
//        }
//        // 调用QXNodeReqService获取巡航轨迹响应
//        GetCruiseTrackReq getCruiseTrackReq = new GetCruiseTrackReq();
//        getCruiseTrackReq.setDeviceId(paramNode.getDeviceEntity().getDeviceCode());
//        getCruiseTrackReq.setChannelId(paramNode.getChannelId());
//        getCruiseTrackReq.setNumber(paramNode.getNumber());
//        ResponseDto responseDto = qxNodeReqUtil.sendRequest(QxNodeApiEnum.GET_CRUISE_TRACK, node, getCruiseTrackReq);
//        if (responseDto.getHttpCode() != 200) {
//            return DtoResult.error(QxNodeApiEnum.GET_CRUISE_TRACK.getDes() + "失败", responseDto.getMsg());
//        }
//        GetCruiseTrackResp resp = JSON.parseObject(responseDto.getRes(), GetCruiseTrackResp.class);
//        VlinkerConvergeCruiseTrackResp cruiseTrackVo = VlinkerConvergeCruiseTrackResp.builder()
//                .presetlist(resp.getCruisePoints().stream()
//                        .map(it -> VlinkerConvergeCruiseTrackResp.Preset.builder()
//                                .pauseTime(it.getStayTime())
//                                .preset(it.getPresetIndex())
//                                .speed(it.getSpeed())
//                                .build())
//                        .collect(Collectors.toList()))
//                .build();
//        // 返回处理后的响应结果
//        return DtoResult.ok(cruiseTrackVo);
//    }
//
//    @Override
//    public DtoResult<VlinkerConvergeCruiseTrackResp> getCruiseTrackList(VideoNodeBaseParam paramNode) {
//        // 根据设备ID查询信号节点信息
//        SignalNodeEntity node = signalNodeService.getById(paramNode.getDeviceEntity().getNodeId());
//        if (node == null) {
//            return DtoResult.error("设备未绑定节点！");
//        }
//        // 调用服务获取巡航轨迹列表
//        GetCruiseTrackListReq getCruiseTrackListReq = new GetCruiseTrackListReq();
//        getCruiseTrackListReq.setDeviceId(paramNode.getDeviceEntity().getDeviceCode());
//        getCruiseTrackListReq.setChannelId(paramNode.getChannelId());
//        ResponseDto responseDto = qxNodeReqUtil.sendRequest(QxNodeApiEnum.GET_CRUISE_TRACK_LIST, node, getCruiseTrackListReq);
//        if (responseDto.getHttpCode() != 200) {
//            return DtoResult.error(QxNodeApiEnum.GET_CRUISE_TRACK_LIST.getDes() + "失败", responseDto.getMsg());
//        }
//        GetCruiseTrackListResp resp = JSON.parseObject(responseDto.getRes(), GetCruiseTrackListResp.class);
//        if (resp.getCruiseTrackList() == null) {
//            resp.setCruiseTrackList(new ArrayList<>());
//        }
//        VlinkerConvergeCruiseTrackResp vlinkerConvergeCruiseTrackResp = VlinkerConvergeCruiseTrackResp.builder()
//                .presetlist(resp.getCruiseTrackList().stream()
//                        .map(it -> VlinkerConvergeCruiseTrackResp.Preset.builder()
//                                .name(it.getName())
//                                .preset(it.getNumber())
//                                .build())
//                        .collect(Collectors.toList()))
//                .build();
//        // 返回处理后的响应结果
//        return DtoResult.ok(vlinkerConvergeCruiseTrackResp);
//    }
//
//    @Override
//    public Result subscribeAlarm(SubscribeAlarmDto subscribeAlarmDto) {
//        return Result.error("暂不支持！");
//    }
//
//
//    @Override
//    public DtoResult<Void> ctrlCruiseTrack(CtrlCruiseTrackParamVideoNode param) {
//        return DtoResult.error("暂不支持！");
//    }
//
//    @Override
//    public DtoResult<Void> startCruiseTrack(CruiseTrackNodeParam param) {
//
//        // 根据设备ID查询信号节点信息
//        SignalNodeEntity node = signalNodeService.getById(param.getDeviceEntity().getNodeId());
//        if (node == null) {
//            return DtoResult.error("设备未绑定节点！");
//        }
//        // 控制巡航轨迹列表开始执行
//        CtrlCruiseTrackListReq ctrlCruiseTrackListReq = new CtrlCruiseTrackListReq();
//        ctrlCruiseTrackListReq.setDeviceId(param.getDeviceEntity().getDeviceCode());
//        ctrlCruiseTrackListReq.setChannelId(param.getChannelId());
//        ctrlCruiseTrackListReq.setNumber(param.getNumber());
//        ctrlCruiseTrackListReq.setCmd("START");
//        ResponseDto ctrlCruiseTrackListResponseDto = qxNodeReqUtil.sendRequest(QxNodeApiEnum.CTRL_CRUISE_TRACK_LIST, node, ctrlCruiseTrackListReq);
//        if (ctrlCruiseTrackListResponseDto.getHttpCode() != 200) {
//            return DtoResult.error(QxNodeApiEnum.CTRL_CRUISE_TRACK_LIST.getDes() + "异常 ", ctrlCruiseTrackListResponseDto.getMsg());
//        }
//
//        // 返回操作成功
//        return DtoResult.ok();
//    }
//
//    @Override
//    public DtoResult<Void> stopCruiseTrack(CruiseTrackNodeParam param) {
//        // 根据设备ID获取信号节点信息
//        SignalNodeEntity node = signalNodeService.getById(param.getDeviceEntity().getNodeId());
//        if (node == null) {
//            return DtoResult.error("设备未绑定节点！");
//        }
//        // 调用服务停止巡航轨迹
//        CtrlCruiseTrackListReq ctrlCruiseTrackListReq = new CtrlCruiseTrackListReq();
//        ctrlCruiseTrackListReq.setDeviceId(param.getDeviceEntity().getDeviceCode());
//        ctrlCruiseTrackListReq.setChannelId(param.getChannelId());
//        ctrlCruiseTrackListReq.setNumber(param.getNumber());
//        ctrlCruiseTrackListReq.setCmd("STOP");
//        ResponseDto ctrlCruiseTrackListResponseDto = qxNodeReqUtil.sendRequest(QxNodeApiEnum.CTRL_CRUISE_TRACK_LIST, node, ctrlCruiseTrackListReq);
//        if (ctrlCruiseTrackListResponseDto.getHttpCode() != 200) {
//            return DtoResult.error(QxNodeApiEnum.CTRL_CRUISE_TRACK_LIST.getDes() + "异常", ctrlCruiseTrackListResponseDto.getMsg());
//        }
//        // 返回操作成功的结果
//        return DtoResult.ok();
//    }
//
//    @Override
//    public DtoResult<Void> delCruiseTrack(CruiseTrackNodeParam param) {
//        // 根据设备ID获取信号节点信息
//        SignalNodeEntity node = signalNodeService.getById(param.getDeviceEntity().getNodeId());
//        if (node == null) {
//            return DtoResult.error("设备未绑定节点！");
//        }
//        CtrlCruiseTrackListReq ctrlCruiseTrackListReq = new CtrlCruiseTrackListReq();
//        ctrlCruiseTrackListReq.setDeviceId(param.getDeviceEntity().getDeviceCode());
//        ctrlCruiseTrackListReq.setChannelId(param.getChannelId());
//        ctrlCruiseTrackListReq.setNumber(param.getNumber());
//        ctrlCruiseTrackListReq.setCmd("DELETE");
//        ctrlCruiseTrackListReq.setPreset(0);
//        ResponseDto ctrlCruiseTrackListResponseDto = qxNodeReqUtil.sendRequest(QxNodeApiEnum.CTRL_CRUISE_TRACK_LIST, node, ctrlCruiseTrackListReq);
//        if (ctrlCruiseTrackListResponseDto.getHttpCode() != 200) {
//            return DtoResult.error(QxNodeApiEnum.CTRL_CRUISE_TRACK_LIST.getDes() + "异常", ctrlCruiseTrackListResponseDto.getMsg());
//        }
//        // 删除完成后，返回成功结果
//        return DtoResult.ok();
//    }
//
//    @Override
//    public DtoResult<SaveOrUpdateCruiseTrackResp> saveOrUpdateCruiseTrack(VideoNodeBaseParam videoNodeBaseParam, ConvSaveOrUpdateCruiseTrackReq param) {
//        // 根据设备ID查询信号节点信息
//        SignalNodeEntity node = signalNodeService.getById(videoNodeBaseParam.getDeviceEntity().getNodeId());
//        if (node == null) {
//            return DtoResult.error("设备未绑定节点！");
//        }
//        SaveOrUpdateCruiseTrackResp resp = new SaveOrUpdateCruiseTrackResp();
//
//        // 如果未指定巡航点数量，则先获取当前巡航点列表，并计算新的巡航点数量
//        if (param.getNumber() == null) {
//            GetCruiseTrackListReq getCruiseTrackListReq = new GetCruiseTrackListReq();
//            getCruiseTrackListReq.setDeviceId(videoNodeBaseParam.getDeviceEntity().getDeviceCode());
//            getCruiseTrackListReq.setChannelId(videoNodeBaseParam.getChannelId());
//            ResponseDto responseDto = qxNodeReqUtil.sendRequest(QxNodeApiEnum.GET_CRUISE_TRACK_LIST, node, getCruiseTrackListReq);
//            if (responseDto.getHttpCode() != 200) {
//                return DtoResult.error(QxNodeApiEnum.GET_CRUISE_TRACK_LIST.getDes() + "失败", responseDto.getMsg());
//            }
//            GetCruiseTrackListResp cruiseTrackList = JSON.parseObject(responseDto.getRes(), GetCruiseTrackListResp.class);
//            Integer number = 1;
//            if (cruiseTrackList != null && cruiseTrackList.getSumNum() != null) {
//                number = cruiseTrackList.getSumNum() + 1;
//            }
//            resp.setNumber(number);
//            // 添加新的巡航点
//            for (int i = 0; i < param.getItemList().size(); i++) {
//                CtrlCruiseTrackListReq ctrlCruiseTrackListReq = new CtrlCruiseTrackListReq();
//                ctrlCruiseTrackListReq.setDeviceId(videoNodeBaseParam.getDeviceEntity().getDeviceCode());
//                ctrlCruiseTrackListReq.setChannelId(videoNodeBaseParam.getChannelId());
//                ctrlCruiseTrackListReq.setNumber(number);
//                ctrlCruiseTrackListReq.setCmd("ADD");
//                ctrlCruiseTrackListReq.setPreset(param.getItemList().get(i).getPresetIndex());
//                ctrlCruiseTrackListReq.setSpeed(param.getItemList().get(i).getSpeed());
//                ctrlCruiseTrackListReq.setStayTime(param.getItemList().get(i).getStayTime());
//                ResponseDto ctrlCruiseTrackListResponseDto = qxNodeReqUtil.sendRequest(QxNodeApiEnum.CTRL_CRUISE_TRACK_LIST, node, ctrlCruiseTrackListReq);
//                if (ctrlCruiseTrackListResponseDto.getHttpCode() != 200) {
//                    return DtoResult.error(QxNodeApiEnum.CTRL_CRUISE_TRACK_LIST.getDes() + "异常 ", ctrlCruiseTrackListResponseDto.getMsg());
//                }
//            }
//        } else {
//            // 已指定巡航点数量，先删除指定的巡航点，然后添加新的巡航点
//            resp.setNumber(param.getNumber());
//
//            CtrlCruiseTrackListReq ctrlCruiseTrackListReqDel = new CtrlCruiseTrackListReq();
//            ctrlCruiseTrackListReqDel.setDeviceId(videoNodeBaseParam.getDeviceEntity().getDeviceCode());
//            ctrlCruiseTrackListReqDel.setChannelId(videoNodeBaseParam.getChannelId());
//            ctrlCruiseTrackListReqDel.setNumber(param.getNumber());
//            ctrlCruiseTrackListReqDel.setCmd("DELETE");
//            ctrlCruiseTrackListReqDel.setPreset(0);
//            ResponseDto ctrlCruiseTrackListResponseDtoDel = qxNodeReqUtil.sendRequest(QxNodeApiEnum.CTRL_CRUISE_TRACK_LIST, node, ctrlCruiseTrackListReqDel);
//            if (ctrlCruiseTrackListResponseDtoDel.getHttpCode() != 200) {
//                return DtoResult.error(QxNodeApiEnum.CTRL_CRUISE_TRACK_LIST.getDes() + "异常 ", ctrlCruiseTrackListResponseDtoDel.getMsg());
//            }
//            // 添加新的巡航点
//            for (int i = 0; i < param.getItemList().size(); i++) {
//                CtrlCruiseTrackListReq ctrlCruiseTrackListReq = new CtrlCruiseTrackListReq();
//                ctrlCruiseTrackListReq.setDeviceId(videoNodeBaseParam.getDeviceEntity().getDeviceCode());
//                ctrlCruiseTrackListReq.setChannelId(videoNodeBaseParam.getChannelId());
//                ctrlCruiseTrackListReq.setNumber(param.getNumber());
//                ctrlCruiseTrackListReq.setCmd("ADD");
//                ctrlCruiseTrackListReq.setPreset(param.getItemList().get(i).getPresetIndex());
//                ctrlCruiseTrackListReq.setSpeed(param.getItemList().get(i).getSpeed());
//                ctrlCruiseTrackListReq.setStayTime(param.getItemList().get(i).getStayTime());
//                ResponseDto ctrlCruiseTrackListResponseDto = qxNodeReqUtil.sendRequest(QxNodeApiEnum.CTRL_CRUISE_TRACK_LIST, node, ctrlCruiseTrackListReq);
//                if (ctrlCruiseTrackListResponseDto.getHttpCode() != 200) {
//                    return DtoResult.error(QxNodeApiEnum.CTRL_CRUISE_TRACK_LIST.getDes() + "异常 ", ctrlCruiseTrackListResponseDto.getMsg());
//                }
//            }
//        }
//        // 操作成功，返回ok
//        return DtoResult.ok(resp);
//    }
//
//    @Override
//    public Result ctrlPtzPrecise(CtrlPtzPreciseDto ctrlPtzPreciseDto) {
//        return Result.error("暂不支持！");
//    }
//
//    @Override
//    public DtoResult<Void> ctrlPanoramicCruise(CtrlPanoramicCruiseParamVideoNode param) {
//        return DtoResult.error("暂不支持！");
//    }
//
//    @Override
//    public DtoResult<PanoramicCruiseVo> getPanoramicCruise(VideoNodeBaseParam param) {
//        return DtoResult.error("暂不支持！");
//    }
//
//    @Override
//    public DtoResult<GimbalStatusVo> getGimbalStatus(VideoNodeBaseParam param) {
//        return DtoResult.error("暂不支持！");
//    }
//
//    @Override
//    public DtoResult<Void> gimbalLock(GimbalLockParamVideoNode param) {
//        return DtoResult.error("暂不支持！");
//    }
//
//    @Override
//    public DtoResult<Void> deviceControl(DeviceControlParamVideoNode param) {
//        return DtoResult.error("暂不支持！");
//    }
//
//    @Override
//    public DtoResult<VlinkerConvergeFlipVideoResp> pictureFlipControl(PictureFlipControlParamVideoNode param) {
//        // 根据设备ID获取关联的信号节点
//        SignalNodeEntity node = signalNodeService.getById(param.getDeviceEntity().getNodeId());
//        if (node == null) {
//            return DtoResult.error("设备未绑定节点！");
//        }
//        // 创建设备配置响应对象，并设置帧镜像状态
//        DeviceConfigResp deviceConfigResp = new DeviceConfigResp();
//        DeviceConfigResp.FrameMirror frameMirror = new DeviceConfigResp.FrameMirror();
//        frameMirror.setFrameMirror(param.getFrameMirror());
//        deviceConfigResp.setFrameMirror(frameMirror);
//        // 更新设备配置
//        SetDeviceConfigReq setDeviceConfigReq = new SetDeviceConfigReq();
//        setDeviceConfigReq.setDeviceId(param.getDeviceEntity().getDeviceCode());
//        setDeviceConfigReq.setChannelId(param.getChannelId());
//        setDeviceConfigReq.setVideoParamOpt(deviceConfigResp.getVideoParamOpt());
//        setDeviceConfigReq.setVideoParamAttribute(deviceConfigResp.getVideoParamAttribute());
//        setDeviceConfigReq.setVideoRecordPlan(deviceConfigResp.getVideoRecordPlan());
//        setDeviceConfigReq.setVideoRecordPlanConf(deviceConfigResp.getVideoRecordPlanConf());
//        setDeviceConfigReq.setFrameMirror(deviceConfigResp.getFrameMirror());
//        setDeviceConfigReq.setOsdConfig(deviceConfigResp.getOsdConfig());
//        ResponseDto responseDto = qxNodeReqUtil.sendRequest(QxNodeApiEnum.UPDATE_DEVICE_CONFIG, node, setDeviceConfigReq);
//        if (responseDto.getHttpCode() != 200) {
//            throw new BizRuntimeException(QxNodeApiEnum.UPDATE_DEVICE_CONFIG.getDes() + "失败");
//        }
//        // 返回操作成功的响应
//        return DtoResult.ok();
//    }
//
//    @Override
//    public DtoResult<VlinkerConvergeFlipVideoResp> getPictureFlipParam(PictureFlipControlParamVideoNode param) {
//        // 根据设备ID查询信号节点实体
//        SignalNodeEntity node = signalNodeService.getById(param.getDeviceEntity().getNodeId());
//        if (node == null) {
//            return DtoResult.error("设备未绑定节点！");
//        }
//        // 请求获取设备的OSD配置
//        GetDeviceConfigReq getChannelsReq = new GetDeviceConfigReq();
//        getChannelsReq.setDeviceId(param.getDeviceEntity().getDeviceCode());
//        getChannelsReq.setChannelId(param.getChannelId());
//        getChannelsReq.setConfigCmd("FrameMirror");
//        ResponseDto responseDto = qxNodeReqUtil.sendRequest(QxNodeApiEnum.GET_DEVICE_CONFIG, node, getChannelsReq);
//        if (responseDto.getHttpCode() != 200) {
//            return DtoResult.error(QxNodeApiEnum.GET_DEVICE_CONFIG.getDes() + "失败");
//        }
//        DeviceConfigResp resp = JSON.parseObject(responseDto.getRes(), DeviceConfigResp.class);
//        // 返回设备的OSD配置
//        VlinkerConvergeFlipVideoResp videoResp = new VlinkerConvergeFlipVideoResp();
//        videoResp.setFrameMirror(0);
//        if (resp != null && resp.getFrameMirror() != null) {
//            videoResp.setFrameMirror(resp.getFrameMirror().getFrameMirror());
//        }
//        return DtoResult.ok(videoResp);
//    }
//
//    @Override
//    public DtoResult<List<ConvergeVideoResp>> getVideoParameter(VideoNodeBaseParam param) {
//        return DtoResult.error("暂不支持！");
//    }
//
//    @Override
//    public DtoResult<Void> setVideoParameter(SetVideoParamVideoNode param) {
//        return DtoResult.error("暂不支持！");
//    }
//
//    @Override
//    public DtoResult<AudioParameterVo> getAudioParameter(VideoNodeBaseParam param) {
//        return DtoResult.error("暂不支持！");
//    }
//
//    @Override
//    public DtoResult<Void> setAudioParameter(SetAudioParamVideoNode param) {
//        return DtoResult.error("暂不支持！");
//    }
//
//    @Override
//    public DtoResult<OsdParameterVo> getOsdParameter(VideoNodeBaseParam param) {
//        return DtoResult.error("暂不支持！");
//    }
//
//    @Override
//    public DtoResult<Void> setOsdParameter(SetOsdParamVideoNode param) {
//        return DtoResult.error("暂不支持！");
//    }
//
//    @Override
//    public DtoResult<Void> setOsdTimeParameter(SetOsdParamVideoNode param) {
//        return DtoResult.error("暂不支持！");
//    }
//
//    @Override
//    public DtoResult<VolumeCommandVo> getVolumeCommand(VideoNodeBaseParam param) {
//        return DtoResult.error("暂不支持！");
//    }
//
//    @Override
//    public DtoResult<Void> setVolumeCommand(VolumeCommandParamVideoNode param) {
//        return DtoResult.error("暂不支持！");
//    }
//
//    @Override
//    public DtoResult<Void> deviceRestart(VideoNodeBaseParam param) {
//        // 检查设备类型，禁止重启虚拟设备
//        if (Objects.equals(param.getDeviceEntity().getAccessWay(), AccessWayType.VIRTUALLY.getType())) {
//            throw new BizRuntimeException("虚拟设备不能重启");
//        }
//        // 获取设备绑定的节点信息
//        SignalNodeEntity node = signalNodeService.getById(param.getDeviceEntity().getNodeId());
//        if (node == null) {
//            throw new BizRuntimeException("未绑定节点");
//        }
//        // 创建重启设备请求对象
//        DeviceRebootReq deviceRebootReq = new DeviceRebootReq();
//        deviceRebootReq.setDeviceId(param.getDeviceEntity().getDeviceCode());
//        // 发送重启设备请求
//        ResponseDto responseDto = qxNodeReqUtil.sendRequest(QxNodeApiEnum.DEVICE_REBOOT, node, deviceRebootReq);
//        // 处理请求结果
//        if (responseDto.getHttpCode() != 200) {
//            return DtoResult.error(QxNodeApiEnum.DEVICE_REBOOT.getDes() + "失败", responseDto.getRes());
//        }
//        return DtoResult.ok();
//    }
//
//    @Override
//    public DtoResult<CommonSetStatusLightResp> getLightingArg(VideoNodeBaseParam param) {
//        return DtoResult.error("暂不支持！");
//    }
//
//    @Override
//    public DtoResult<ConvergeLightingArgResp> setLightingArg(SetLightingArgParamVideoNode param) {
//        return DtoResult.error("暂不支持！");
//    }
//
//    @Override
//    public DtoResult<VlinkerConvergeFillLightStatusResp> setFillLightingArg(SetFillLightingArgParamVideoNode param) {
//        return DtoResult.error("暂不支持！");
//    }
//
//    @Override
//    public DtoResult<Void> stopPlayback(VideoNodeBaseParam param, CommonStopPlayBackReq playBackReq) {
//        // 根据设备ID查询信号节点信息
//        SignalNodeEntity node = signalNodeService.getById(param.getDeviceEntity().getNodeId());
//        if (node == null) {
//            return DtoResult.error("设备未绑定节点！");
//        }
//        qxNodeReqService.stopPlayback(node, param.getChannelId(), playBackReq.getUuid());
//        return DtoResult.ok();
//    }
//
//    @Override
//    public DtoResult<ConvergeSdCardCapacityResp> getSdCardCapacity(VideoNodeBaseParam videoNodeBaseParam) {
//        // 根据设备ID查询信号节点信息
//        SignalNodeEntity node = signalNodeService.getById(videoNodeBaseParam.getDeviceEntity().getNodeId());
//        if (node == null) {
//            return DtoResult.error("设备未绑定节点！");
//        }
//        // 调用QXNodeReqService获取SD卡信息，传入信号节点、设备序列号和通道ID
//        SDCardInfoReq getChannelsReq = new SDCardInfoReq();
//        getChannelsReq.setDeviceId(videoNodeBaseParam.getDeviceEntity().getDeviceCode());
//        ResponseDto responseDto = qxNodeReqUtil.sendRequest(QxNodeApiEnum.GET_SD_CARD_INFO, node, getChannelsReq);
//        if (responseDto.getHttpCode() != 200) {
//            return DtoResult.error(QxNodeApiEnum.GET_SD_CARD_INFO.getDes() + "失败");
//        }
//        SDCardInfoResp resp = JSON.parseObject(responseDto.getRes(), SDCardInfoResp.class);
//        ConvergeSdCardCapacityResp convergeSdCardCapacityResp = new ConvergeSdCardCapacityResp();
//        convergeSdCardCapacityResp.setNum(0);
//        convergeSdCardCapacityResp.setItems(new ArrayList<>());
//        if (resp != null) {
//            convergeSdCardCapacityResp.setNum(resp.getNum());
//            resp.getItems().forEach(item -> {
//                ConvergeSdCardCapacityResp.SDCardStatus sdCardStatusVo = new ConvergeSdCardCapacityResp.SDCardStatus();
//                sdCardStatusVo.setId(item.getId());
//                sdCardStatusVo.setCapacity(item.getCapacity());
//                sdCardStatusVo.setStatus(item.getStatus());
//                sdCardStatusVo.setFreeSpace(item.getFreeSpace());
//                sdCardStatusVo.setHddName(item.getHddName());
//                sdCardStatusVo.setFormatProgress(item.getFormatProgress());
//                convergeSdCardCapacityResp.getItems().add(sdCardStatusVo);
//            });
//        }
//        // 返回获取到的SD卡信息
//        return DtoResult.ok(convergeSdCardCapacityResp);
//    }
//
//    @Override
//    public DtoResult<Void> sdCardFormatting(VideoNodeBaseParam videoNodeBaseParam) {
//        return DtoResult.error("暂不支持！");
//    }
//
//    @Override
//    public DtoResult<SdCardCapacityVo> ntpTiming(VideoNodeBaseParam param) {
//        return DtoResult.error("暂不支持！");
//    }
//
//    @Override
//    public DtoResult<Void> firmwareUpgrade(FirmwareUpgradeParamVideoNode param) {
//        return DtoResult.error("暂不支持！");
//    }
//
//    @Override
//    public DtoResult<SnapshotVo> getSnapshot(VideoNodeBaseParam param) {
//        return DtoResult.error("暂不支持！");
//    }
//
//    @Override
//    public DtoResult<LogUriVo> getLogUri(VideoNodeBaseParam param, String httpUrl) {
//        return DtoResult.error("暂不支持！");
//    }
//
//    @Override
//    public DtoResult<LogUriVo> getLogDownloadUrl(LogDownloadUrlParam param) {
//        return DtoResult.error("暂不支持！");
//    }
//
//    @Override
//    public DtoResult<CommonGetDormancyResp> getDormancyParameter(VideoNodeBaseParam param) {
//        return DtoResult.error("暂不支持！");
//    }
//
//    @Override
//    public DtoResult<CommonSetDormancyResp> setDormancyParameter(VideoNodeBaseParam param, CommonSetDormancyReq setDormancyReq) {
//        return DtoResult.error("暂不支持！");
//    }
//
//    @Resource
//    private MqttProviderConfig mqttProviderConfig;
//
//    @Resource
//    private RedisUtil redisUtil;
//
//    @Override
//    public DtoResult<CommonGetSideAlgorithmResp> getAlarmParameter(VideoNodeBaseParam videoNodeBaseParam, CommonGetSideAlgorithmReq param) {
//        if (param.getDetectType() == null) {
//            return DtoResult.error("请选择一种告警查询！");
//        }
//        GetFaceAreaCfgRequest getFaceAreaCfgRequest = new GetFaceAreaCfgRequest();
//        getFaceAreaCfgRequest.setDeviceUUID(videoNodeBaseParam.getDeviceEntity().getSn());
//        String taskId = UUID.randomUUID().toString();
//        getFaceAreaCfgRequest.setTaskID(taskId);
//        /**
//         *    10：人脸检测
//         *  10000：电动车未戴头盔检测
//         *  10001：二轮车超载
//         *  10002：三轮车违规载人
//         */
//        if (param.getDetectType() == 5) {
//            getFaceAreaCfgRequest.setEventSort(10);
//        } else if (param.getDetectType() == 6) {
//            getFaceAreaCfgRequest.setEventSort(10000);
//        } else if (param.getDetectType() == 7) {
//            getFaceAreaCfgRequest.setEventSort(10001);
//        } else if (param.getDetectType() == 8) {
//            getFaceAreaCfgRequest.setEventSort(10002);
//        } else {
//            return DtoResult.error("暂不支持！");
//        }
//        GetAlgorithmCfgRequest getAlgorithmCfgRequest = new GetAlgorithmCfgRequest();
//        getAlgorithmCfgRequest.setTaskID(UUID.randomUUID().toString());
//        getAlgorithmCfgRequest.setDeviceUUID(videoNodeBaseParam.getDeviceEntity().getSn());
//        MqttMessage mqttMessageGetAlg = new MqttMessage(JSON.toJSONBytes(getAlgorithmCfgRequest));
//        mqttMessageGetAlg.setQos(1);
//        MqttMessage mqttMessage = new MqttMessage(JSON.toJSONBytes(getFaceAreaCfgRequest));
//        mqttMessage.setQos(1);
//        log.info("巨龙查询 查询告警信息 taskId:{} deviceUUID:{},EventSort:{}", taskId, getFaceAreaCfgRequest.getDeviceUUID(), getFaceAreaCfgRequest.getEventSort());
//        log.info("巨龙查询 查询告警状态信息 taskId:{} deviceUUID:{}", taskId, getAlgorithmCfgRequest.getDeviceUUID());
//        AtomicReference<Object> res = new AtomicReference<>();
//        AtomicReference<Object> res2 = new AtomicReference<>();
//        ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
//        String key = MqttProviderConfig.getRedisPrefix() + getFaceAreaCfgRequest.getAction().replace("Request", "") + ":" + getFaceAreaCfgRequest.getDeviceUUID();
//        String key2 = MqttProviderConfig.getRedisPrefix() + getAlgorithmCfgRequest.getAction().replace("Request", "") + ":" + getAlgorithmCfgRequest.getDeviceUUID();
//        Runnable checkRedisTask = () -> {
//            if (res.get() == null) {
//                res.set(redisUtil.get(key)); // 获取 Redis 中的数据
//            }
//            if (res2.get() == null) {
//                res2.set(redisUtil.get(key2)); // 获取 Redis 中的数据
//            }
//            if (res.get() != null) {
//                redisUtil.del(key);
//            }
//            if (res2.get() != null) {
//                redisUtil.del(key2);
//            }
//            if (res.get() != null && res2.get() != null) {
//                log.info("查询到 巨龙算法信息了(redis拿到数据了)");
//                scheduler.shutdown(); // 停止定时任务
//            }
//        };
//        // 启动定时任务，每1秒检查一次
//        scheduler.scheduleAtFixedRate(checkRedisTask, 0, 1, TimeUnit.SECONDS);
//        mqttProviderConfig.publish("mqtt/Configure/" + videoNodeBaseParam.getDeviceEntity().getSn(), mqttMessage);
//        mqttProviderConfig.publish("mqtt/Configure/" + videoNodeBaseParam.getDeviceEntity().getSn(), mqttMessageGetAlg);
//        // 等待获取数据，使用一个阻塞方式，不需要使用 Thread.sleep()
//        try {
//            // 等待直到任务完成或超时
//            if (!scheduler.awaitTermination(20, TimeUnit.SECONDS)) {
//                // 如果60秒内没有获取到数据，停止定时任务
//                scheduler.shutdownNow();
//            }
//        } catch (InterruptedException e) {
//            return DtoResult.error("获取失败！");
//        }
//        // 如果没有获取到数据，返回错误
//        if (res.get() == null && res2.get() == null) {
//            return DtoResult.error("获取失败！");
//        }
//        GetFaceAreaCfgResponse getFaceAreaCfgResponse = JSON.parseObject(res.toString(), GetFaceAreaCfgResponse.class);
//        GetAlgorithmCfgResponse getAlgorithmCfgResponse = JSON.parseObject(res2.toString(), GetAlgorithmCfgResponse.class);
//        log.info("巨龙查询 查询告警信息返回:{}和:{}", getFaceAreaCfgResponse, getAlgorithmCfgResponse);
//        CommonGetSideAlgorithmResp resp = new CommonGetSideAlgorithmResp();
//        List<CommonGetSideAlgorithmResp.AlarmParameterVo> list = new ArrayList<>();
//        if (getFaceAreaCfgResponse.getRet() == 0) {
//            CommonGetSideAlgorithmResp.AlarmParameterVo vo = new CommonGetSideAlgorithmResp.AlarmParameterVo();
//            vo.setDetectId(1);
//            vo.setDetectType(param.getDetectType());
//            vo.setDetectEnable(getFaceAreaCfgResponse.getEnabled());
//            if (getAlgorithmCfgResponse.getAlgorithmInfo() != null) {
//                vo.setDetectEnable(getAlgorithmCfgResponse.getAlgorithmInfo()
//                        .stream()
//                        .anyMatch(x -> Objects.equals(x.getEventSort(), getFaceAreaCfgResponse.getEventSort())
//                                && x.getEnabled() == 1) ? 1 : 0);
//            }
//            vo.setDetectSens(50);
//            // 假设的原始区域坐标
//            Integer AreaX1 = getFaceAreaCfgResponse.getAreaX1();
//            Integer AreaY1 = getFaceAreaCfgResponse.getAreaY1();
//            Integer AreaX2 = getFaceAreaCfgResponse.getAreaX2();
//            Integer AreaY2 = getFaceAreaCfgResponse.getAreaY2();
//            List<CommonGetSideAlgorithmResp.PointVo> points = new ArrayList<>();
//            // 直接映射为四个点，根据具体需求调整
//            points.add(new CommonGetSideAlgorithmResp.PointVo(AreaX1, AreaY1)); // 左上
//            points.add(new CommonGetSideAlgorithmResp.PointVo(AreaX1, AreaY2)); // 左下
//            points.add(new CommonGetSideAlgorithmResp.PointVo(AreaX2, AreaY1)); // 右上
//            points.add(new CommonGetSideAlgorithmResp.PointVo(AreaX2, AreaY2)); // 右下
//            vo.setDetectPoints(points);
//            list.add(vo);
//        }
//        resp.setAlarmParameterVos(list);
//        return DtoResult.ok(resp);
//    }
//
//    @Override
//    public DtoResult<CommonSetSideAlgorithmResp> setAlarmParameter(VideoNodeBaseParam videoNodeBaseParam, CommonSetSideAlgorithmReq param) {
//        SetFaceAreaCfgRequest setFaceAreaCfgRequest = new SetFaceAreaCfgRequest();
//        setFaceAreaCfgRequest.setDeviceUUID(videoNodeBaseParam.getDeviceEntity().getSn());
//        String taskId = UUID.randomUUID().toString();
//        setFaceAreaCfgRequest.setTaskID(taskId);
//        if (param.getDetectId() == null) {
//            return DtoResult.error("请输入告警区域编号！");
//        }
//        /**
//         *    10：人脸检测
//         *  10000：电动车未戴头盔检测
//         *  10001：二轮车超载
//         *  10002：三轮车违规载人
//         */
//        if (param.getDetectType() == 5) {
//            setFaceAreaCfgRequest.setEventSort(10);
//        } else if (param.getDetectType() == 6) {
//            setFaceAreaCfgRequest.setEventSort(10000);
//        } else if (param.getDetectType() == 7) {
//            setFaceAreaCfgRequest.setEventSort(10001);
//        } else if (param.getDetectType() == 8) {
//            setFaceAreaCfgRequest.setEventSort(10002);
//        } else {
//            return DtoResult.error("暂不支持！");
//        }
//        // 获取最小的 x 和 y
//        Integer minX = param.getDetectPoints().stream().mapToInt(CommonSetSideAlgorithmReq.PointVo::getX).min().orElse(0);
//        Integer minY = param.getDetectPoints().stream().mapToInt(CommonSetSideAlgorithmReq.PointVo::getY).min().orElse(0);
//        // 获取最大的 x 和 y
//        Integer maxX = param.getDetectPoints().stream().mapToInt(CommonSetSideAlgorithmReq.PointVo::getX).max().orElse(100);
//        Integer maxY = param.getDetectPoints().stream().mapToInt(CommonSetSideAlgorithmReq.PointVo::getY).max().orElse(100);
//        setFaceAreaCfgRequest.setAreaX1(minX);
//        setFaceAreaCfgRequest.setAreaY1(minY);
//        setFaceAreaCfgRequest.setAreaX2(maxX);
//        setFaceAreaCfgRequest.setAreaY2(maxY);
//
//        setFaceAreaCfgRequest.setEnabled(param.getDetectEnable());
//
//        SetAlgorithmCfgRequest setAlgorithmCfgRequest = new SetAlgorithmCfgRequest();
//        setAlgorithmCfgRequest.setTaskID(UUID.randomUUID().toString());
//        setAlgorithmCfgRequest.setDeviceUUID(videoNodeBaseParam.getDeviceEntity().getSn());
//        setAlgorithmCfgRequest.setAlgorithmCount(1);
//        SetAlgorithmCfgRequest.AlgorithmInfoDto build = SetAlgorithmCfgRequest.AlgorithmInfoDto
//                .builder()
//                .EventSort(setFaceAreaCfgRequest.getEventSort())
//                .Enabled(param.getDetectEnable())
//                .build();
//        List<SetAlgorithmCfgRequest.AlgorithmInfoDto> List = new ArrayList<>();
//        List.add(build);
//        setAlgorithmCfgRequest.setAlgorithmInfo(List);
//
//        MqttMessage mqttMessage = new MqttMessage(JSON.toJSONBytes(setFaceAreaCfgRequest));
//        mqttMessage.setQos(1);
//        log.info("巨龙查询 设置告警信息 taskId:{} deviceUUID:{},EventSort:{}", taskId, setFaceAreaCfgRequest.getDeviceUUID(), setFaceAreaCfgRequest.getEventSort());
//        MqttMessage mqttMessageBySetAlgorithm = new MqttMessage(JSON.toJSONBytes(setAlgorithmCfgRequest));
//        mqttMessageBySetAlgorithm.setQos(1);
//        log.info("巨龙查询 设置告警开关信息 taskId:{} deviceUUID:{},EventSort:{}", taskId, setAlgorithmCfgRequest.getDeviceUUID(), setAlgorithmCfgRequest.getAlgorithmCount());
//
//        AtomicReference<Object> res = new AtomicReference<>();
//        AtomicReference<Object> res2 = new AtomicReference<>();
//        ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
//        String key = MqttProviderConfig.getRedisPrefix() + setFaceAreaCfgRequest.getAction().replace("Request", "") + ":" + setFaceAreaCfgRequest.getTaskID();
//        String key2 = MqttProviderConfig.getRedisPrefix() + setAlgorithmCfgRequest.getAction().replace("Request", "") + ":" + setAlgorithmCfgRequest.getTaskID();
//        // 启动定时任务，每1秒检查一次
//        scheduler.scheduleAtFixedRate(() -> {
//            if (res.get() == null) {
//                res.set(redisUtil.get(key)); // 获取 Redis 中的数据
//            }
//            if (res2.get() == null) {
//                res2.set(redisUtil.get(key2)); // 获取 Redis 中的数据
//            }
//            if (res.get() != null) {
//                redisUtil.del(key);
//            }
//            if (res2.get() != null) {
//                redisUtil.del(key2);
//            }
//            if (res.get() != null && res2.get() != null) {
//                log.info("设置告警信息成功！(redis拿到数据了)");
//                scheduler.shutdown(); // 停止定时任务
//            }
//        }, 0, 1, TimeUnit.SECONDS);
//        mqttProviderConfig.publish("mqtt/Configure/" + videoNodeBaseParam.getDeviceEntity().getSn(), mqttMessage);
//        mqttProviderConfig.publish("mqtt/Configure/" + videoNodeBaseParam.getDeviceEntity().getSn(), mqttMessageBySetAlgorithm);
//
//        // 等待获取数据，使用一个阻塞方式，不需要使用 Thread.sleep()
//        try {
//            // 等待直到任务完成或超时
//            if (!scheduler.awaitTermination(20, TimeUnit.SECONDS)) {
//                // 如果60秒内没有获取到数据，停止定时任务
//                scheduler.shutdownNow();
//            }
//        } catch (InterruptedException e) {
//            return DtoResult.error("设置失败！");
//        }
//
//        // 如果没有获取到数据，返回错误
//        if (res.get() == null && res2.get() == null) {
//            return DtoResult.error("设置失败！");
//        }
//        CommonSetSideAlgorithmResp resp = new CommonSetSideAlgorithmResp();
//        SetFaceAreaCfgResponse setFaceAreaCfgResponse = JSON.parseObject(res.toString(), SetFaceAreaCfgResponse.class);
//        SetAlgorithmCfgResponse setAlgorithmCfgResponse = JSON.parseObject(res2.toString(), SetAlgorithmCfgResponse.class);
//        if (setFaceAreaCfgResponse.getRet() == 0 && setAlgorithmCfgResponse.getRet() == 0) {
//            return DtoResult.ok(resp);
//        }
//        return DtoResult.error("操作失败！");
//    }
//
//    @Override
//    public DtoResult<CommonGetNowDeviceVersionResp> deviceUpgrade(VideoNodeBaseParam videoNodeBaseParam, CommonDeviceUpgradeReq deviceUpgradeReq) {
//
//        SignalNodeEntity node = signalNodeService.getById(videoNodeBaseParam.getDeviceEntity().getNodeId());
//        if (node == null) {
//            return DtoResult.error("节点不存在");
//        }
//        // 调用服务进行设备升级，并更新设备信息
//        qxNodeReqService.deviceUpgrade(node, deviceUpgradeReq.getVersionNum(), deviceUpgradeReq.getFileUrl(), videoNodeBaseParam.getDeviceEntity().getDeviceCode());
//        return DtoResult.ok();
//    }
//
//    @Override
//    public DtoResult<CommonGetOsdInfoResp> getOsdInfo(VideoNodeBaseParam videoNodeBaseParam) {
//        return DtoResult.error("暂不支持！");
//    }
//
//    @Override
//    public DtoResult<CommonCallDeviceByVideoResp> callDeviceByVideo(VideoNodeBaseParam videoNodeBaseParam, CommonCallDeviceByVideoReq req) {
//        return DtoResult.error("暂不支持！");
//    }
//
//    @Override
//    public DtoResult<CommonGetOsdInfoResp> setOsdInfo(VideoNodeBaseParam videoNodeBaseParam, CommonSetOsdInfoReq commonSetOsdInfoReq) {
//        return DtoResult.error("暂不支持！");
//    }
//
//    @Override
//    public DtoResult<Void> setPlaybackPlan(PlaybackPlanParamVideoNode param) {
//        return DtoResult.error("暂不支持！");
//    }
//
//    @Override
//    public DtoResult<Void> setCloudPlaybackPlan(PlaybackPlanParamVideoNode param) {
//        SignalNodeEntity node = signalNodeService.getById(param.getDeviceEntity().getNodeId());
//        if (node == null) {
//            return DtoResult.error("节点不存在");
//        }
//        if (param.getRecordStreamType() == null) {
//            param.setRecordStreamType(1);
//        }
//        if (param.getRecordStreamType() != 1) {
//            return DtoResult.error("暂不支持此码流！");
//        }
//        if (param.getEnable() == 0) {
//            qxNodeReqService.channelRecordSwitch(node, param.getChannelId(), false);
//        } else {
//            ConvergeRecordPlansReq getChannelRecordTimeLineReq = new ConvergeRecordPlansReq();
//            getChannelRecordTimeLineReq.setChannelIds(new ArrayList<String>() {{
//                add(param.getChannel());
//            }});
//            getChannelRecordTimeLineReq.setPlans(param.getPlan());
//            getChannelRecordTimeLineReq.setEnabled(param.getType() == 2);
//            getChannelRecordTimeLineReq.setCloudEnabled(param.getType() == 1);
//            getChannelRecordTimeLineReq.setStorageId(String.valueOf(param.getStorageId()));
//            getChannelRecordTimeLineReq.setStrategyId(String.valueOf(param.getStorageStrategyId()));
//            ResponseDto responseDto = qxNodeReqUtil.sendRequest(QxNodeApiEnum.CHANNEL_RECORD_BATCH_SETTING, node, getChannelRecordTimeLineReq);
//            if (responseDto.getHttpCode() != 200) {
//                return DtoResult.error(QxNodeApiEnum.CHANNEL_RECORD_BATCH_SETTING.getDes() + "失败", responseDto.getMsg());
//            }
//        }
//        return DtoResult.ok();
//    }
//
//
//    @Override
//    public DtoResult<Void> cloudVoice(CloudVoiceParam param) {
//        return DtoResult.error("暂不支持！");
//    }
//
//    @Override
//    public DtoResult<Void> cloudVoiceStop(CloudVoiceParam param) {
//        return DtoResult.error("暂不支持！");
//    }
//
//    @Override
//    public DtoResult<TimingBroadcastVo> getTimingBroadcast(VideoNodeBaseParam param) {
//        return DtoResult.error("暂不支持！");
//    }
//
//    @Override
//    public DtoResult<CommonGetDeviceInfoResp> getDeviceInfo(VideoNodeBaseParam param) {
//        return DtoResult.error("暂不支持！");
//    }
//
//    @Override
//    public DtoResult<DeviceAbilityVo> getDeviceAbility(VideoNodeBaseParam param) {
//        return DtoResult.error("暂不支持！");
//    }
//}
