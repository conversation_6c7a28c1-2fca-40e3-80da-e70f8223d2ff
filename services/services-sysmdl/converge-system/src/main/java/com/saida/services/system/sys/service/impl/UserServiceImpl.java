package com.saida.services.system.sys.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.crypto.digest.BCrypt;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.saida.services.exception.BizRuntimeException;
import com.saida.services.common.tools.JwtUtil;
import com.saida.services.common.tools.SDNumberUtil;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.converge.entity.system.ConvSysOrgEntity;
import com.saida.services.system.sys.entity.*;
import com.saida.services.system.sys.mapper.RoleMapper;
import com.saida.services.system.sys.mapper.UserRoleMapper;
import com.saida.services.system.sys.service.*;
import com.saida.services.tools.attr.AttrUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.saida.services.system.sys.mapper.UserMapper;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;


@Service("userService")
public class UserServiceImpl extends ServiceImpl<UserMapper, UserEntity> implements UserService {

    @Autowired
    private UserRoleMapper userRoleMapper;

    @Autowired
    private ConvSysOrgService convSysOrgService;

    @Autowired
    private AttributeDetailService attributeDetailService;

    @Autowired
    private RoleMapper roleMapper;

    @Autowired
    private UserMsgService userMsgService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addOrUpdate(UserEntity entity) {
        if(entity.getOrgId() == null){
            throw new BizRuntimeException("组织必选");
        }
        ConvSysOrgEntity org = convSysOrgService.getById(entity.getOrgId());
        if(org == null){
            throw new BizRuntimeException("组织不存在");
        }

        if(entity.getId() == null){
            UserEntity tmp = getByAccountOrPhone(entity.getAccount(), entity.getPhone(),JwtUtil.getUserInfo().getPid());
            if(tmp != null){
                throw new BizRuntimeException("账号或手机号已存在");
            }
            if(StringUtil.isEmpty(entity.getPassword())){
                throw new BizRuntimeException("密码必填");
            }
            entity.setPassword(BCrypt.hashpw(entity.getPassword()));
            entity.setCreateUser(JwtUtil.getUserId());
            entity.setCreateTime(DateTime.now());
            save(entity);
            saveUserRole(entity);
        }else{
            UserEntity tmp = getByAccountOrPhone(entity.getAccount(), entity.getPhone(),JwtUtil.getUserInfo().getPid());
            if(tmp != null && !SDNumberUtil.equals(entity.getId(), tmp.getId())){
                throw new BizRuntimeException("账号或手机号已存在");
            }
            entity.setPassword(null);
            entity.setUpdateUser(JwtUtil.getUserId());
            entity.setUpdateTime(DateTime.now());
            updateById(entity);
            saveUserRole(entity);
        }
    }

    private void saveUserRole(UserEntity entity) {
        if(entity.getId() == null){
            return;
        }
        userRoleMapper.delete(new LambdaQueryWrapper<UserRoleEntity>().eq(UserRoleEntity::getUid, entity.getId()));
        if(StringUtil.isEmpty(entity.getRids())){
            return;
        }
        for(String rid : entity.getRids().split(",")){
            if(!NumberUtil.isNumber(rid)){
                continue;
            }
            UserRoleEntity ur = new UserRoleEntity();
            ur.setCreateUser(JwtUtil.getUserId());
            ur.setCreateTime(DateTime.now());
            ur.setUid(entity.getId());
            ur.setRid(Long.valueOf(rid));
            userRoleMapper.insert(ur);
        }
    }

    @Override
    public UserEntity getByAccountOrPhone(String username,Integer pid) {
        if(StringUtil.isEmpty(username)){
            throw new BizRuntimeException("账号或手机号必填");
        }
        UserEntity one = getOne(
                new LambdaQueryWrapper<UserEntity>()
                        .eq(UserEntity::getPid, pid)
                        .eq(UserEntity::getPhone, username)
                , false);
        if (one == null){
            one = getOne(
                    new LambdaQueryWrapper<UserEntity>()
                            .eq(UserEntity::getPid, pid)
                            .eq(UserEntity::getAccount, username)
                    , false);
        }
        return one;
    }

    @Override
    public UserEntity getByAccountOrPhone(String account, String phone,Integer pid) {
        if(StringUtil.isEmpty(account) && StringUtil.isEmpty(phone)){
            throw new BizRuntimeException("账号或手机号必选一项");
        }
        UserEntity one = getOne(
                new LambdaQueryWrapper<UserEntity>()
                        .eq(UserEntity::getPid, pid)
                        .eq(UserEntity::getPhone, phone)
                , false);
        if (one == null){
            one = getOne(
                    new LambdaQueryWrapper<UserEntity>()
                            .eq(UserEntity::getPid, pid)
                            .eq(UserEntity::getAccount, account)
                    , false);
        }
        return one;
    }

    @Override
    public IPage<UserEntity> listPage(UserEntity entity) {
        if(entity.getOrgId() == null){
            throw new BizRuntimeException("组织必选");
        }
        List<Long> orgIds = convSysOrgService.getChildIds(entity.getOrgId(),entity.getSubLevel(), entity.getOrgName());
        if(orgIds == null || orgIds.isEmpty()){
            return new Page<UserEntity>(entity.getPageNum(), entity.getPageSize());
        }
        entity.setCreateTime(DateTime.now());//过滤续费时间条件
        IPage<UserEntity> page = baseMapper.listPage(new Page<UserEntity>(entity.getPageNum(), entity.getPageSize()), entity, orgIds);
        if(page == null || page.getRecords() == null || page.getRecords().isEmpty()){
            return page;
        }
        fillAttr(page.getRecords());
        return page;
    }

    @Override
    public UserEntity getInfo(Long id) {
        UserEntity user = getById(id);
        if(user == null){
            return user;
        }
        return fillAttr(new ArrayList<UserEntity>(){{add(user);}}).get(0);
    }

    @Override
    @Transactional
    public void deleteById(Long id) {
        removeById(id);
        userRoleMapper.delete(new LambdaQueryWrapper<UserRoleEntity>().eq(UserRoleEntity::getUid, id));
    }

    @Override
    public void resetPassword(Long id, String password) {
        update(new LambdaUpdateWrapper<UserEntity>().eq(UserEntity::getId, id)
                .set(UserEntity::getPassword, BCrypt.hashpw(password))
                .set(UserEntity::getLastModifyPasswordTime, new Date()));
    }

    private List<UserEntity> fillAttr(List<UserEntity> records) {
        if(records == null || records.isEmpty()){
            return records;
        }
        Map<Object, Object> dicMap = new HashMap<>();
        dicMap.putAll(attributeDetailService.getAllIdNameMap());
        Map<Long, String> orgMap = convSysOrgService.getIdNameMap(records.stream().map(UserEntity::getOrgId).collect(Collectors.toSet()));
        if(orgMap != null && !orgMap.isEmpty()){
            dicMap.putAll(orgMap);
        }
        records.replaceAll(o -> AttrUtil.putAttr(o, dicMap));

        List<UserRoleEntity> urs = userRoleMapper.selectList(new LambdaQueryWrapper<UserRoleEntity>().in(UserRoleEntity::getUid, records.stream().map(UserEntity::getId).collect(Collectors.toSet())));
        if(urs != null && !urs.isEmpty()){
            List<Long> rids = urs.stream().map(UserRoleEntity::getRid).distinct().collect(Collectors.toList());
            List<RoleEntity> roles = roleMapper.selectBatchIds(rids);
            if(roles != null && !roles.isEmpty()){
                records.forEach(u -> {
                    List<Long> roleids = urs.stream().filter(ur -> SDNumberUtil.equals(u.getId(), ur.getUid())).map(UserRoleEntity::getRid).distinct().collect(Collectors.toList());
                    u.setRoles(roles.stream().filter( r -> roleids.contains(r.getId())).collect(Collectors.toList()));
                    if(u.getRoles() != null && !u.getRoles().isEmpty()){
                        u.setRids(u.getRoles().stream().map(RoleEntity::getId).map(String::valueOf).collect(Collectors.joining(",")));
                    }
                });
            }
        }
        return records;
    }
}