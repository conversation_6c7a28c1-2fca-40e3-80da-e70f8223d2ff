package com.saida.services.system.task;

import cn.hutool.core.date.DateUtil;
import com.saida.services.converge.entity.OpsDeviceCruiseRecordEntity;
import com.saida.services.open.req.VlinkerJumpPreSetReq;
import com.saida.services.system.api.service.ApiDeviceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
public class OpsDeviceCruiseThread extends Thread {

    private final Long id;
    private final List<OpsDeviceCruiseRecordEntity> list;

    private boolean isRun = true;

    private final ApiDeviceService apiDeviceService;

    private final RedisTemplate<String, Object> redisTemplate;

    public OpsDeviceCruiseThread(Long id, List<OpsDeviceCruiseRecordEntity> list, ApiDeviceService apiDeviceService, RedisTemplate<String, Object> redisTemplate) {
        this.id = id;
        this.list = list;
        this.apiDeviceService = apiDeviceService;
        this.redisTemplate = redisTemplate;
    }

    public void stopRun() {
        isRun = false;
    }

    /**
     * 运行方法，用于设备巡航线程的执行。
     * 通过Redis的键值对来标记任务是否正在执行，避免重复执行相同任务。
     * 巡航过程中，根据配置的设备和位置信息，逐个跳转到指定的预设位，并在每个预设位停留指定的时间。
     */
    @Override
    public void run() {
        this.setName("alg-巡航-" + id);
        // 构建任务的Redis键，用于标记任务状态和避免重复执行
        String key = "ALG:TASK:OpsDeviceCruiseThread:" + id;
        // 从Redis中获取当前任务的状态
        Object redisTask = redisTemplate.opsForValue().get(key);
        // 如果任务已经在执行，则直接返回，避免重复执行
        if (redisTask != null) {
            log.info("任务已经在别的服务执行，跳过执行");
            return;
        }
        log.info("开始执行任务");
        // 初始化索引，用于遍历设备巡航记录列表
        int i = 0;
        // 当任务应该继续运行时，循环执行巡航过程
        while (isRun) {
            // 更新Redis中的任务状态，表示任务正在执行
            redisTemplate.opsForValue().set(key, DateUtil.now(), 60, TimeUnit.SECONDS);
            // 获取当前要处理的设备巡航记录
            OpsDeviceCruiseRecordEntity opsDeviceCruiseRecordEntity = list.get(i);
            // 构建预设位跳转请求
            VlinkerJumpPreSetReq req = new VlinkerJumpPreSetReq();
            req.setIndex(opsDeviceCruiseRecordEntity.getPosition());
            req.setDeviceId(opsDeviceCruiseRecordEntity.getDeviceId());
            // 日志记录当前的巡航请求
            log.info("巡航->req:{}", req);
            // 发送预设位跳转请求
            apiDeviceService.preSetJump(req);
            // 判断是否是最后一个预设位，如果是，则重新开始，否则继续下一个预设位
            if (i == list.size() - 1) {
                i = 0;
            } else {
                i = i + 1;
            }
            // 在当前预设位停留指定的时间
            try {
                Thread.sleep(opsDeviceCruiseRecordEntity.getTimeS() * 1000);
            } catch (InterruptedException e) {
                // 记录线程休眠过程中的异常
                log.error("线程休眠异常", e);
            }
        }
        // 任务结束，删除Redis中的任务标记
        redisTemplate.delete(key);
    }

}
