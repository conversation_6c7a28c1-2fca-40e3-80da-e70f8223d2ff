package com.saida.services.system.alarm.pojo.vo;


import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.ALWAYS)
public class AlgorithmTypeCountVo {


    /*
     * 今日事件
     */
    private Long todayCount;

    /*
     * 近7天时间
     */
    private Long weekCount;


    private Long algorithmId;

    /*
     * 算法名称
     */
    private String algorithmName;
}
