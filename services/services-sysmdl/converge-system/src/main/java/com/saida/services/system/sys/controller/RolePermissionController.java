package com.saida.services.system.sys.controller;

import cn.hutool.core.lang.tree.Tree;
import com.saida.services.common.base.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.saida.services.system.sys.entity.RolePermissionEntity;
import com.saida.services.system.sys.service.RolePermissionService;

import java.util.List;


/**
 * 角色-资源
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-10-07 09:28:38
 */
@RestController
@RequestMapping("sys/rolepermission")
public class RolePermissionController {
    @Autowired
    private RolePermissionService rolePermissionService;

    @PostMapping("save")
    public Result save(RolePermissionEntity entity){
        if(entity.getRid() == null){
            return Result.error("角色ID必传");
        }
        rolePermissionService.addOrUpdate(entity);
        return Result.ok();
    }

    @GetMapping("getByRole")
    public Result getByRole(RolePermissionEntity entity){
        if(entity.getRid() == null){
            return Result.error("角色必传");
        }
        List<Tree<Long>> list = rolePermissionService.getByRole(entity.getRid());
        return Result.ok(list);
    }
}
