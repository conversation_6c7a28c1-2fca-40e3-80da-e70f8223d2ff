package com.saida.services.system.mqtt;

import cn.hutool.core.util.StrUtil;
import com.xxl.job.core.handler.annotation.VlinkerXxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class MqttSubscribe {

    private Map<String, Event> errorSubscribes = new ConcurrentHashMap<>();

    private Map<String, Event> okSubscribes = new ConcurrentHashMap<>();

    private Map<String, Instant> okTimeSubscribes = new ConcurrentHashMap<>();

    private Map<String, Instant> errorTimeSubscribes = new ConcurrentHashMap<>();

//    @Scheduled(cron = "0 0/2 * * * ?")   //每2分钟执行一次
    @VlinkerXxlJob(
            value = "MqttSubscribe",
            cron = "0 0/2 * * * ?",
            desc = "清理过期的订阅信息"
    )
    public void execute() {
        Instant instant = Instant.now().minusMillis(TimeUnit.MINUTES.toMillis(2));
        for (String key : okTimeSubscribes.keySet()) {
            if (okTimeSubscribes.get(key).isBefore(instant)) {
                okSubscribes.remove(key);
                okTimeSubscribes.remove(key);
            }
        }
        for (String key : errorTimeSubscribes.keySet()) {
            if (errorTimeSubscribes.get(key).isBefore(instant)) {
                errorSubscribes.remove(key);
                errorTimeSubscribes.remove(key);
            }
        }
        log.info("[定时任务] 清理过期的订阅信息 okTimeSubscribes{},okSubscribes:{},errorTimeSubscribes:{},errorSubscribes:{}"
                , okTimeSubscribes.size(), okSubscribes.size(), errorTimeSubscribes.size(), errorSubscribes.size());
    }

    public interface Event {
        void response(EventResult eventResult);
    }

    public enum EventResultType {
        /**
         * 查询边缘微服务器下的摄像头
         */
        camera_find("camera_find"),

        /**
         * 算法任务查询
         */
        algo_task_find("algo_task_find"),

        /**
         * 告警数据推送
         */
        push_data("push_data");

        public final String name;

        EventResultType(String name) {
            this.name = name;
        }

        public static EventResultType getEventType(String name) {
            if (StrUtil.isEmpty(name)) {
                return null;
            }
            for (EventResultType e : values()) {
                if (name.equals(e.name)) {
                    return e;
                }
            }
            return null;
        }
    }

    public static class EventResult {
        public EventResultType eventType;
        public Object data;

        public EventResult() {
        }

        public EventResult(EventResultType eventType, Object data) {
            this.eventType = eventType;
            this.data = data;
        }
    }

    public void addErrorSubscribe(String key, Event event) {
        errorSubscribes.put(key, event);
        errorTimeSubscribes.put(key, Instant.now());
    }

    public void addOkSubscribe(String key, Event event) {
        okSubscribes.put(key, event);
        okTimeSubscribes.put(key, Instant.now());
    }

    public Event getErrorSubscribe(String key) {
        return errorSubscribes.get(key);
    }

    public void removeErrorSubscribe(String key) {
        if (key == null) {
            return;
        }
        errorSubscribes.remove(key);
        errorTimeSubscribes.remove(key);
    }

    public Event getOkSubscribe(String key) {
        return okSubscribes.get(key);
    }

    public void removeOkSubscribe(String key) {
        if (key == null) {
            return;
        }
        okSubscribes.remove(key);
        okTimeSubscribes.remove(key);
    }

    public int getErrorSubscribesSize() {
        return errorSubscribes.size();
    }

    public int getOkSubscribesSize() {
        return okSubscribes.size();
    }
}
