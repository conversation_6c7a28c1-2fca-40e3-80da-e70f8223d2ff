package com.saida.services.system.algorithm.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.saida.services.algorithm.entity.ImageRecognitionEntity;
import com.saida.services.common.base.Result;
import org.springframework.web.context.request.async.DeferredResult;

import java.util.List;

public interface ImageRecognitionService extends IService<ImageRecognitionEntity> {


    DeferredResult<Result> insertEntity(ImageRecognitionEntity entity);

    IPage<ImageRecognitionEntity> listPage(ImageRecognitionEntity entity);

    void batchDelete(String ids);

    List<String> queryAlgorithmList();
}
