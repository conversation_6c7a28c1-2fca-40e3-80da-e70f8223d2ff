package com.saida.services.system.ops.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.saida.services.common.base.Result;
import com.saida.services.converge.entity.PlatformInsert1400Entity;

/**
 * 1400平台接入
 *
 * <AUTHOR>
 * email ${email}
 * date 2023-10-10 15:31:54
 */
public interface PlatformInsert1400Service extends IService<PlatformInsert1400Entity> {

    Result listPage(PlatformInsert1400Entity entity);

    Result info(PlatformInsert1400Entity entity);

    Result add(PlatformInsert1400Entity entity);

    Result edit(PlatformInsert1400Entity entity);

    Result delete(PlatformInsert1400Entity entity);


}

