package com.saida.services.system.alarm.mapper;

import com.saida.services.system.alarm.pojo.dto.EventCountDto;
import com.saida.services.system.alarm.pojo.vo.AlarmDevicePointTypeCountVo;
import com.saida.services.system.alarm.pojo.vo.AlarmEventTypeCountVo;
import com.saida.services.system.alarm.pojo.vo.AlarmTypeCountVo;
import com.saida.services.system.alarm.pojo.vo.AlgorithmTypeCountVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface AlarmStatisticsMapper {

    List<AlarmTypeCountVo> getAlarmDayLastCount(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("dto") EventCountDto dto, @Param("algIds") List<Long> algIds);

    List<AlarmEventTypeCountVo> getAlarmEventLastCount(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("dto") EventCountDto dto, @Param("algIds") List<Long> algIds);

    List<AlarmTypeCountVo> getAlarmDayCount(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("dto") EventCountDto dto, @Param("algIds") List<Long> algIds);

    List<String> getResourceJson(@Param("devicId") Long devicId, @Param("algorithmId") Long algorithmId);

    List<AlarmEventTypeCountVo> getAlarmEventTypeCount(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("dto") EventCountDto dto, @Param("algIds") List<Long> algIds);

    List<AlarmDevicePointTypeCountVo> getAlarmDevicePointTypeCount(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("dto") EventCountDto dto, @Param("algIds") List<Long> algIds);

    List<AlarmDevicePointTypeCountVo> selectAllAlarmDevicePointTypeCount(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("dto") EventCountDto dto, @Param("algIds") List<Long> algIds);

    List<AlgorithmTypeCountVo> getAlgorithmTypeCount(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("dto") EventCountDto dto);
}
