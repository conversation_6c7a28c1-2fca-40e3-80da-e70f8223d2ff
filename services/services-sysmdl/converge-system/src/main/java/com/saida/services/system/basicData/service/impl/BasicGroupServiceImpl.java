package com.saida.services.system.basicData.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.saida.services.common.entity.BasePageInfoEntity;
import com.saida.services.entities.base.BaseRequest;
import com.saida.services.system.basicData.dto.FacePageQryDto;
import com.saida.services.system.basicData.entity.BasicGroupEntity;
import com.saida.services.system.basicData.entity.BasicPeoplePhotoEntity;
import com.saida.services.system.basicData.mapper.BasicGroupMapper;
import com.saida.services.system.basicData.mapper.BasicPeoplePhotoMapper;
import com.saida.services.system.basicData.service.BasicGroupService;
import com.saida.services.system.basicData.service.BasicPeopleFaceFeatureService;
import com.saida.services.system.basicData.vo.PeoplePhotoGroupVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 分组表(BasicGroupEntity)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-05 15:20:01
 */
@Slf4j
@Service("basicGroupService")
public class BasicGroupServiceImpl extends ServiceImpl<BasicGroupMapper, BasicGroupEntity> implements BasicGroupService {
    @Resource
    private BasicPeoplePhotoMapper basicPeoplePhotoMapper;
    @Autowired
    private BasicPeopleFaceFeatureService basicPeopleFaceFeatureService;

    /**
     * 分页查询
     *
     * @param basicGroupEntity 筛选条件
     * @param baseRequest      分页对象
     * @return 查询结果
     */
    @Override
    public BasePageInfoEntity<BasicGroupEntity> queryByPage(BasicGroupEntity basicGroupEntity, BaseRequest baseRequest) {
        try (Page<BasicGroupEntity> page = PageHelper.startPage(baseRequest.getPageNum(), baseRequest.getPageSize())) {
            List<BasicGroupEntity> list = this.baseMapper.selectList(new LambdaUpdateWrapper<BasicGroupEntity>()
                    .eq(BasicGroupEntity::getAppId, basicGroupEntity.getAppId())
                    .like(StringUtils.isNotBlank(basicGroupEntity.getName()), BasicGroupEntity::getName, basicGroupEntity.getName())
                    .orderByDesc(BasicGroupEntity::getCreateTime));
            return new BasePageInfoEntity<>(page);
        }
    }

    /**
     * 新增数据
     *
     * @param basicGroupEntity 实例对象
     * @return 实例对象
     */
    @Override
    public int insert(BasicGroupEntity basicGroupEntity) {
        return this.baseMapper.insert(basicGroupEntity);
    }

    /**
     * 修改数据
     *
     * @param basicGroupEntity 实例对象
     * @return 实例对象
     */
    @Override
    public int update(BasicGroupEntity basicGroupEntity) {
        return this.baseMapper.updateById(basicGroupEntity);
    }

    /**
     * 通过主键删除数据
     *
     * @param ids 主键
     * @return 是否成功
     */
    @Override
    public int deleteById(List<String> ids) {
        return this.baseMapper.deleteBatchIds(ids);
    }

    @Override
    public Map<String, List<BasicGroupEntity>> getAllGroups() {
        List<BasicGroupEntity> groupEntityList = baseMapper.selectList(null);
        Map<String, List<BasicGroupEntity>> collect = groupEntityList.stream().collect(Collectors.groupingBy(BasicGroupEntity::getAppId));
        //排序
        collect.forEach((appId, groupEntityList1) -> groupEntityList1.sort(Comparator.comparing(BasicGroupEntity::getCreateTime).reversed()));
        return collect;
    }

    @Override
    public BasePageInfoEntity<BasicPeoplePhotoEntity> queryByDefaultPhotoPage(FacePageQryDto dto, BaseRequest baseRequest) {
        try (Page<BasicPeoplePhotoEntity> page = PageHelper.startPage(baseRequest.getPageNum(), baseRequest.getPageSize())) {
            List<BasicPeoplePhotoEntity> list = this.baseMapper.queryByDefaultPhotoPage(dto);
            return new BasePageInfoEntity<>(page);
        }
    }

    @Override
    public Integer deleteDefaultPhoto(List<String> ids) {
        List<PeoplePhotoGroupVo> list = basicPeoplePhotoMapper.getPhotoAndGroupIdByPhotoId(ids);
        list.forEach(vo -> {
            try {
                basicPeopleFaceFeatureService.delFace(vo.getGroupId(), vo.getPhotoId());
            } catch (Exception e) {
                log.debug("删除人脸图片失败", e);
            }
        });
        return basicPeoplePhotoMapper.deleteBatchIds(ids);
    }

}
