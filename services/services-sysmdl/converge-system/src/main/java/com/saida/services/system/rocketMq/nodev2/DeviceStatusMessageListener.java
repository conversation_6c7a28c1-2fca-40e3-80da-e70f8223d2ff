package com.saida.services.system.rocketMq.nodev2;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.protobuf.InvalidProtocolBufferException;
import com.saida.services.common.base.DtoResult;
import com.saida.services.common.config.ThreadPoolConfig;
import com.saida.services.common.rocketMq.VLinkerRocketMqMessageListener;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.converge.dto.FeignUpdateDeviceOnlineDto;
import com.saida.services.converge.entity.*;
import com.saida.services.deviceApi.resp.CommonGetDeviceInfoResp;
import com.saida.services.deviceApi.resp.CommonGetScreenPropertiesResp;
import com.saida.services.feign.open.system.IFeignOpenSystemApiController;
import com.saida.services.open.dto.DeviceCapacityDto;
import com.saida.services.open.dto.SyncIncreDeviceChannelDataFromConvDto;
import com.saida.services.open.enums.DeviceCapacityEnum;
import com.saida.services.system.ops.service.*;
import com.saida.services.system.pb.OpenCommonEnum;
import com.saida.services.system.pb.OpenSunMessage;
import com.saida.services.system.video.param.VideoNodeBaseParam;
import com.saida.services.system.video.service.VideoCommonService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Executor;

@Slf4j
@Component
@ConditionalOnProperty(prefix = "rocketmq", name = "enable", havingValue = "true")
public class DeviceStatusMessageListener implements VLinkerRocketMqMessageListener {

    @Resource
    private DeviceService deviceService;
    @Resource
    private OpsDeviceChannelService opsDeviceChannelService;
    @Lazy
    @Autowired
    private IFeignOpenSystemApiController iFeignOpenSystemApiController;
    @Lazy
    @Resource(name = ThreadPoolConfig.TASK_EXECUTOR_BEAN_NAME)
    private Executor taskExecutor;

    @Override
    public String getTopic() {
        return "node_v2_device_status";
    }


    @Override
    public int getReadQueueNums() {
        return 1;
    }

    @Override
    public int getWriteQueueNums() {
        return 1;
    }

    @Override
    public boolean isOrder() {
        return true;
    }

    /**
     */
    public void onMessage(MessageExt message) {
        byte[] body = message.getBody();
        OpenSunMessage.DeviceStatusSubscribeBody deviceStatus = null;
        try {
            deviceStatus = OpenSunMessage.DeviceStatusSubscribeBody.parseFrom(body);
        } catch (InvalidProtocolBufferException e) {
            log.error("mqDeviceStatusSubscribeBody parse error", e);
            return;
        }
        if (StringUtil.isEmpty(message.getTags())) {
            log.error("参数错误");
            return;
        }
        handleDeviceStatusPb(message.getKeys(), message.getTags(), deviceStatus);
    }

    @Resource
    private OpsDeviceOnlineRecordService opsDeviceOnlineRecordService;

    public void handleDeviceStatusPb(String key, String tags, OpenSunMessage.DeviceStatusSubscribeBody deviceStatus) {
        // 将 Timestamp 转换为 Instant
        Instant instant = Instant.ofEpochSecond(deviceStatus.getGenerationTime().getSeconds(), deviceStatus.getGenerationTime().getNanos());
        log.info("[mq:node_v2_device_status]收到消息：key:{},tag:{}，xid:{},status;{},time:{}"
                , key, tags, deviceStatus.getXId(), deviceStatus.getStatus(), instant.toEpochMilli());
        List<SyncIncreDeviceChannelDataFromConvDto.DeviceChannelInfo> deleteList = new ArrayList<>();
        List<SyncIncreDeviceChannelDataFromConvDto.DeviceChannelInfo> saveOrUpdateList = new ArrayList<>();
        // 代表所在节点离线,需要把所有相关设备通道都离线
        if (deviceStatus.getXId() == 0xfffffff0 && deviceStatus.getParentId() == 0xffffffef && StringUtil.isNotEmpty(deviceStatus.getPlanetTag())) {
            log.error("[mq:node_v2_device_status] 水星「{}」节点离线,需要把所有相关设备通道都离线,key:{},tag:{}，xid:{}", deviceStatus.getPlanetTag(), key, tags, deviceStatus.getXId());
            List<DeviceEntity> list = deviceService.list(new LambdaQueryWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getNodeId, tags)
                    .eq(DeviceEntity::getPlanetTag, deviceStatus.getPlanetTag()));
            for (DeviceEntity deviceEntity : list) {
                deviceService.update(new LambdaUpdateWrapper<DeviceEntity>()
                        .eq(DeviceEntity::getId, deviceEntity.getId())
                        .set(DeviceEntity::getStatus, 0)
                        .set(DeviceEntity::getNodeUpdateTime, instant.toEpochMilli()));

                DeviceModelEntity deviceModelEntity = deviceModelService.getOne(new LambdaQueryWrapper<DeviceModelEntity>()
                        .eq(DeviceModelEntity::getModel, deviceEntity.getModel()));
                DeviceModelVersionEntity deviceModelVersionEntity;
                if (deviceModelEntity != null) {
                    deviceModelVersionEntity = deviceModelVersionService.getOne(new LambdaQueryWrapper<DeviceModelVersionEntity>()
                            .eq(DeviceModelVersionEntity::getModelId, deviceModelEntity.getId())
                            .eq(DeviceModelVersionEntity::getVersionNum, deviceEntity.getVersion()));
                } else {
                    deviceModelVersionEntity = null;
                }
                List<OpsDeviceChannelEntity> channelList = opsDeviceChannelService.list(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                        .eq(OpsDeviceChannelEntity::getDeviceId, deviceEntity.getId()));

                for (OpsDeviceChannelEntity opsDeviceChannelEntity : channelList) {
                    LambdaUpdateWrapper<OpsDeviceChannelEntity> channelWrapper = new LambdaUpdateWrapper<OpsDeviceChannelEntity>()
                            .eq(OpsDeviceChannelEntity::getId, opsDeviceChannelEntity.getId())
                            .set(OpsDeviceChannelEntity::getStatus, 0)
                            .set(OpsDeviceChannelEntity::getUpdateTime, LocalDateTime.now());
                    channelWrapper.set(OpsDeviceChannelEntity::getNodeUpdateTime, instant.toEpochMilli());
                    opsDeviceChannelService.update(channelWrapper);

                    SyncIncreDeviceChannelDataFromConvDto.DeviceChannelInfo deviceChannelInfo = new SyncIncreDeviceChannelDataFromConvDto.DeviceChannelInfo();
                    deviceChannelInfo.setDeviceCode(deviceEntity.getDeviceCode());
                    deviceChannelInfo.setDeviceName(deviceEntity.getName());
                    deviceChannelInfo.setChannelName(opsDeviceChannelEntity.getChannelName());
                    deviceChannelInfo.setChannelId(opsDeviceChannelEntity.getChannelId());
                    deviceChannelInfo.setOrgId(opsDeviceChannelEntity.getOrgId());
                    deviceChannelInfo.setOnline(0);
                    if (deviceModelVersionEntity != null) {
                        deviceChannelInfo.setDeviceCapacity(deviceModelVersionEntity.getDeviceCapacity());
                    }
                    saveOrUpdateList.add(deviceChannelInfo);
                }
            }
        }
        if (deviceStatus.getEntryType() == 1) {

        } else if (deviceStatus.getEntryType() == 2) {
            boolean isStatusUpdate = false;
            //设备
            DeviceEntity deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getNodeXId, deviceStatus.getXId())
                    .eq(DeviceEntity::getNodeId, tags)
                    .last(" limit 1 "));
            if (deviceEntity == null) {
                log.error("设备不存在,{},{}", deviceStatus.getXId(), tags);
                return;
            }
            if (deviceStatus.getStatus() == OpenCommonEnum.CatalogStatus.CATALOG_STATUS_ONLINE) {
                isStatusUpdate = deviceEntity.getStatus() != 1;
                deviceEntity.setStatus(1);
                LambdaUpdateWrapper<DeviceEntity> updateWrapper = new LambdaUpdateWrapper<DeviceEntity>()
                        .eq(DeviceEntity::getId, deviceEntity.getId())
                        .set(DeviceEntity::getStatus, deviceEntity.getStatus())
                        .set(DeviceEntity::getEndSideName, deviceStatus.getName())
                        .set(DeviceEntity::getEip, deviceStatus.getDeviceAddr())
                        .set(DeviceEntity::getPlanetTag, deviceStatus.getPlanetTag());
                if (deviceEntity.getNodeUpdateTime() != null && deviceEntity.getNodeUpdateTime() > instant.toEpochMilli()) {
                    log.error("[mq:{}] 收到一个老的数据,key:{},tag:{}，xid:{},t1:{},t2:{}"
                            , this.getTopic(), key, tags, deviceStatus.getXId(), deviceEntity.getNodeUpdateTime(), instant.toEpochMilli());
                    //收到了一个老的数据
                    return;
                }
                if (deviceEntity.getTurnOnTime() == null) {
                    updateWrapper.set(DeviceEntity::getTurnOnTime, new Date());
                }
                updateWrapper.set(DeviceEntity::getNodeUpdateTime, instant.toEpochMilli());
                deviceService.update(updateWrapper);
                initDeviceInfo(deviceEntity);
            } else {
                isStatusUpdate = deviceEntity.getStatus() != 0;
                deviceEntity.setStatus(0);
                LambdaUpdateWrapper<DeviceEntity> updateWrapper = new LambdaUpdateWrapper<DeviceEntity>()
                        .eq(DeviceEntity::getId, deviceEntity.getId())
                        .set(DeviceEntity::getStatus, deviceEntity.getStatus())
                        .set(DeviceEntity::getEndSideName, deviceStatus.getName())
                        .set(DeviceEntity::getEip, deviceStatus.getDeviceAddr())
                        .set(DeviceEntity::getPlanetTag, deviceStatus.getPlanetTag());
                if (deviceEntity.getNodeUpdateTime() != null && deviceEntity.getNodeUpdateTime() > instant.toEpochMilli()) {
                    //收到了一个老的数据
                    log.error("[mq:{}] 收到一个老的数据,key:{},tag:{}，xid:{},t1:{},t2:{}"
                            , this.getTopic(), key, tags, deviceStatus.getXId(), deviceEntity.getNodeUpdateTime(), instant.toEpochMilli());
                    return;
                }
                updateWrapper.set(DeviceEntity::getNodeUpdateTime, instant.toEpochMilli());
                deviceService.update(updateWrapper);
                // 设备离线了 通道也要离线
                List<OpsDeviceChannelEntity> channelList = opsDeviceChannelService.list(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                        .eq(OpsDeviceChannelEntity::getDeviceId, deviceEntity.getId())
                        .eq(OpsDeviceChannelEntity::getDeleteFlag, 1));
                if (!channelList.isEmpty()) {
                    for (OpsDeviceChannelEntity deviceChannel : channelList) {
                        opsDeviceChannelService.update(new LambdaUpdateWrapper<OpsDeviceChannelEntity>()
                                .eq(OpsDeviceChannelEntity::getId, deviceChannel.getId())
                                .set(OpsDeviceChannelEntity::getStatus, 0));
                        SyncIncreDeviceChannelDataFromConvDto.DeviceChannelInfo deviceChannelInfo = new SyncIncreDeviceChannelDataFromConvDto.DeviceChannelInfo();
                        deviceChannelInfo.setDeviceCode(deviceEntity.getDeviceCode());
                        deviceChannelInfo.setDeviceName(deviceEntity.getName());
                        deviceChannelInfo.setChannelName(deviceChannel.getChannelName());
                        deviceChannelInfo.setChannelId(deviceChannel.getChannelId());
                        deviceChannelInfo.setOrgId(deviceChannel.getOrgId());
                        deviceChannelInfo.setOnline(0);
                        saveOrUpdateList.add(deviceChannelInfo);
                    }
                }
            }

            if (isStatusUpdate) {
                OpsDeviceOnlineRecordEntity deviceOnlineRecord = new OpsDeviceOnlineRecordEntity();
                deviceOnlineRecord.setDeviceId(deviceEntity.getId());
                deviceOnlineRecord.setDeviceSn(deviceEntity.getSn());
                deviceOnlineRecord.setContent(deviceEntity.getStatus() == 1 ? "设备上线" : "设备下线");
                deviceOnlineRecord.setCreateTime(LocalDateTime.now());
                deviceOnlineRecord.setUpdateTime(LocalDateTime.now());
                opsDeviceOnlineRecordService.save(deviceOnlineRecord);
            }
        } else if (deviceStatus.getEntryType() == 3) {
            //设备
            DeviceEntity deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getNodeXId, deviceStatus.getParentId())
                    .eq(DeviceEntity::getNodeId, tags));
            if (deviceEntity == null) {
                log.error("设备不存在,{},{}", deviceStatus.getXId(), tags);
                return;
            }
            //通道
            OpsDeviceChannelEntity deviceChannel = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                    .eq(OpsDeviceChannelEntity::getNodeXId, deviceStatus.getXId())
                    .eq(OpsDeviceChannelEntity::getDeviceId, deviceEntity.getId()));
            if (deviceChannel == null) {
                deviceChannel = new OpsDeviceChannelEntity();
                deviceChannel.setNodeId(deviceEntity.getNodeId());
                deviceChannel.setDeviceId(deviceEntity.getId());
                deviceChannel.setChannelId(deviceEntity.getId() + "_" + deviceStatus.getXId());
                deviceChannel.setChannelName(deviceStatus.getName());
                if (deviceStatus.getStatus() == OpenCommonEnum.CatalogStatus.CATALOG_STATUS_ONLINE) {
                    deviceChannel.setStatus(1);
                } else {
                    deviceChannel.setStatus(0);
                }
                deviceChannel.setDeviceSn(deviceEntity.getDeviceCode());
                deviceChannel.setBid(String.valueOf(deviceStatus.getXId()));
                deviceChannel.setOrgId(deviceEntity.getOrgId());
                deviceChannel.setNodeXId(deviceStatus.getXId());
                deviceChannel.setPlanetTag(deviceStatus.getPlanetTag());
                deviceChannel.setCreateTime(LocalDateTime.now());
                deviceChannel.setUpdateTime(LocalDateTime.now());
                opsDeviceChannelService.save(deviceChannel);
                SyncIncreDeviceChannelDataFromConvDto.DeviceChannelInfo deviceChannelInfo = new SyncIncreDeviceChannelDataFromConvDto.DeviceChannelInfo();
                deviceChannelInfo.setDeviceCode(deviceEntity.getDeviceCode());
                deviceChannelInfo.setDeviceName(deviceEntity.getName());
                deviceChannelInfo.setChannelName(deviceChannel.getChannelName());
                deviceChannelInfo.setChannelId(deviceChannel.getChannelId());
                deviceChannelInfo.setOrgId(deviceChannel.getOrgId());
                deviceChannelInfo.setOnline(deviceChannel.getStatus());
                saveOrUpdateList.add(deviceChannelInfo);
            } else {
                if (deviceStatus.getStatus() == OpenCommonEnum.CatalogStatus.CATALOG_STATUS_DELETED) {
                    opsDeviceChannelService.update(new LambdaUpdateWrapper<OpsDeviceChannelEntity>()
                            .eq(OpsDeviceChannelEntity::getId, deviceChannel.getId())
                            .set(OpsDeviceChannelEntity::getUpdateTime, LocalDateTime.now())
                            .set(StringUtil.isNotEmpty(deviceStatus.getPlanetTag()), OpsDeviceChannelEntity::getPlanetTag, deviceStatus.getPlanetTag())
                            .set(OpsDeviceChannelEntity::getDeleteFlag, 2));
                    SyncIncreDeviceChannelDataFromConvDto.DeviceChannelInfo deviceChannelInfo = new SyncIncreDeviceChannelDataFromConvDto.DeviceChannelInfo();
                    deviceChannelInfo.setDeviceCode(deviceEntity.getDeviceCode());
                    deviceChannelInfo.setDeviceName(deviceEntity.getName());
                    deviceChannelInfo.setChannelName(deviceChannel.getChannelName());
                    deviceChannelInfo.setChannelId(deviceChannel.getChannelId());
                    deviceChannelInfo.setOrgId(deviceChannel.getOrgId());
                    deviceChannelInfo.setOnline(deviceChannel.getStatus());
                    deleteList.add(deviceChannelInfo);
                } else {
                    deviceChannel.setChannelName(deviceStatus.getName());
                    if (deviceStatus.getStatus() == OpenCommonEnum.CatalogStatus.CATALOG_STATUS_ONLINE) {
                        deviceChannel.setStatus(1);
                    } else {
                        deviceChannel.setStatus(0);
                    }
                    LambdaUpdateWrapper<OpsDeviceChannelEntity> channelWrapper = new LambdaUpdateWrapper<OpsDeviceChannelEntity>()
                            .eq(OpsDeviceChannelEntity::getId, deviceChannel.getId())
                            .set(OpsDeviceChannelEntity::getPlanetTag, deviceStatus.getPlanetTag())
                            .set(OpsDeviceChannelEntity::getChannelName, deviceStatus.getName())
                            .set(OpsDeviceChannelEntity::getStatus, deviceChannel.getStatus())
                            .set(OpsDeviceChannelEntity::getUpdateTime, LocalDateTime.now())
                            .set(OpsDeviceChannelEntity::getDeleteFlag, 1);
                    if (deviceChannel.getNodeUpdateTime() != null && deviceChannel.getNodeUpdateTime() > instant.toEpochMilli()) {
                        //收到了一个老的数据
                        log.error("[mq:{}] 收到一个老的数据,key:{},tag:{}，xid:{},t1:{},t2:{}"
                                , this.getTopic(), key, tags, deviceStatus.getXId(), deviceChannel.getNodeUpdateTime(), instant.toEpochMilli());
                        return;
                    }
                    channelWrapper.set(OpsDeviceChannelEntity::getNodeUpdateTime, instant.toEpochMilli());
                    opsDeviceChannelService.update(channelWrapper);
                    SyncIncreDeviceChannelDataFromConvDto.DeviceChannelInfo deviceChannelInfo = new SyncIncreDeviceChannelDataFromConvDto.DeviceChannelInfo();
                    deviceChannelInfo.setDeviceCode(deviceEntity.getDeviceCode());
                    deviceChannelInfo.setDeviceName(deviceEntity.getName());
                    deviceChannelInfo.setChannelName(deviceChannel.getChannelName());
                    deviceChannelInfo.setChannelId(deviceChannel.getChannelId());
                    deviceChannelInfo.setOrgId(deviceChannel.getOrgId());
                    deviceChannelInfo.setOnline(deviceChannel.getStatus());
                    saveOrUpdateList.add(deviceChannelInfo);
                }
            }
        }
        // 通知开放平台
        SyncIncreDeviceChannelDataFromConvDto syncIncreDeviceChannelDataFromConvDto = new SyncIncreDeviceChannelDataFromConvDto();
        syncIncreDeviceChannelDataFromConvDto.setDeleteList(deleteList);
        syncIncreDeviceChannelDataFromConvDto.setSaveOrUpdateList(saveOrUpdateList);
        if (!saveOrUpdateList.isEmpty()) {
            saveOrUpdateList.forEach(deviceChannelInfo -> {
                FeignUpdateDeviceOnlineDto feignUpdateDeviceOnlineDto = new FeignUpdateDeviceOnlineDto();
                feignUpdateDeviceOnlineDto.setDeviceSn(deviceChannelInfo.getDeviceCode());
                feignUpdateDeviceOnlineDto.setChannelId(deviceChannelInfo.getChannelId());
                feignUpdateDeviceOnlineDto.setOnline(deviceChannelInfo.getOnline());
                try {
                    DtoResult<Void> voidDtoResult = iFeignOpenSystemApiController.updateDeviceOnline(feignUpdateDeviceOnlineDto);
                    log.info("刷新通道.feign 更新设备在线状态...deviceSn={}, channelId={}, dtoResult={}", feignUpdateDeviceOnlineDto.getDeviceSn()
                            , feignUpdateDeviceOnlineDto.getChannelId(), JSON.toJSON(voidDtoResult));
                } catch (Exception e) {
                    log.error("刷新通道.feign 更新设备在线状态异常...deviceSn={}, msg={}", feignUpdateDeviceOnlineDto.getDeviceSn(), e.getMessage());
                }
            });

        }
        try {
            if (deleteList.isEmpty() && saveOrUpdateList.isEmpty()) {
                return;
            }
            DtoResult<Void> voidDtoResult = iFeignOpenSystemApiController.syncIncreDeviceChannelDataFromConv(syncIncreDeviceChannelDataFromConvDto);
            log.info("同步设备通道数据到open系统...params={}, dtoResult={}", JSON.toJSON(syncIncreDeviceChannelDataFromConvDto), JSON.toJSON(voidDtoResult));
        } catch (Exception e) {
            log.info("同步设备通道数据到open系统.异常...params={}, msg={}", JSON.toJSON(syncIncreDeviceChannelDataFromConvDto), e.getMessage());
        }

    }

    @Resource
    private VideoCommonService videoCommonService;
    @Resource
    private DeviceModelVersionService deviceModelVersionService;
    @Resource
    private DeviceModelService deviceModelService;

    @Async
    public void initDeviceInfo(DeviceEntity deviceEntity) {
        VideoNodeBaseParam param = new VideoNodeBaseParam();
        param.setDeviceId(deviceEntity.getId());
        param.setDeviceCode(deviceEntity.getDeviceCode());
        param.setDeviceEntity(deviceEntity);
        DtoResult<CommonGetDeviceInfoResp> deviceInfo = videoCommonService.getDeviceInfo(param);
        log.info("initDeviceInfo.deviceInfo={}", JSON.toJSONString(deviceInfo));
        if (deviceInfo.success()) {
            CommonGetDeviceInfoResp data = deviceInfo.getData();
            if (StringUtil.isEmpty(data.getModel()) && StringUtil.isEmpty(data.getVersion())) {
                return;
            }
            LambdaUpdateWrapper<DeviceEntity> deviceEntityLambdaUpdateWrapper = new LambdaUpdateWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getId, deviceEntity.getId())
                    .set(DeviceEntity::getModel, data.getModel())
                    .set(DeviceEntity::getVersion, data.getVersion())
                    .set(DeviceEntity::getMac, data.getMac())
                    .set(DeviceEntity::getIip, data.getIip());
            try {
                DeviceModelEntity model = deviceModelService.getOne(new LambdaQueryWrapper<DeviceModelEntity>()
                        .eq(DeviceModelEntity::getModel, data.getModel()).last(" limit 1"));
                Long modelId;
                if (model != null) {
                    deviceEntityLambdaUpdateWrapper.set(DeviceEntity::getManufactor, model.getManufactor());
                    modelId = model.getId();
                } else {
                    DeviceModelEntity entity = new DeviceModelEntity();
                    entity.setModel(data.getModel());
                    entity.setDeviceType(1L);
                    entity.setCreateTime(new Date());
                    entity.setUpdateTime(new Date());
                    deviceModelService.save(entity);
                    modelId = entity.getId();
                }

                DeviceModelVersionEntity nowVersion = deviceModelVersionService.getOne(
                        new LambdaQueryWrapper<DeviceModelVersionEntity>()
                                .eq(DeviceModelVersionEntity::getModelId, modelId)
                                .eq(DeviceModelVersionEntity::getVersionNum, data.getVersion()));
                // 版本不存在 构建一个版本出来
                if (nowVersion == null) {
                    nowVersion = new DeviceModelVersionEntity();
                    nowVersion.setModelId(modelId);
                    nowVersion.setModelStr(data.getModel());
                    nowVersion.setVersionNum(data.getVersion());
                    nowVersion.setMd5(data.getVersion());
                    nowVersion.setReleaseDate(new Date());
                    nowVersion.setCreateTime(new Date());
                    nowVersion.setUpdateTime(new Date());
                    DeviceModelVersionEntity finalNowVersion = nowVersion;
                    Arrays.stream(DeviceCapacityEnum.values()).forEach(e -> {
                        DeviceCapacityDto byField = new DeviceCapacityDto(e, 1);
                        finalNowVersion.addDeviceCapacity(byField);
                    });
                    // 查询最后的一个版本号
                    DeviceModelVersionEntity lastVersion = deviceModelVersionService.getOne(
                            new LambdaQueryWrapper<DeviceModelVersionEntity>()
                                    .eq(DeviceModelVersionEntity::getModelId, modelId)
                                    .orderByDesc(DeviceModelVersionEntity::getNumLong)
                                    .last(" limit 1 "));
                    if (lastVersion != null) {
                        nowVersion.setNumLong(lastVersion.getNumLong() == null ? 1 : lastVersion.getNumLong() + 1);
                        nowVersion.setResolvingPower(lastVersion.getResolvingPower());
                        nowVersion.setBitRate(lastVersion.getBitRate());
                        nowVersion.setFrameRate(lastVersion.getFrameRate());
                        nowVersion.setEncodingFormat(lastVersion.getEncodingFormat());
                        nowVersion.setAudioFormat(lastVersion.getAudioFormat());
                        nowVersion.setVideoAlarm(lastVersion.getVideoAlarm());
                        nowVersion.setDeviceAlarm(lastVersion.getDeviceAlarm());
                        nowVersion.setGpsAlarm(lastVersion.getGpsAlarm());
                        nowVersion.setFaultAlarm(lastVersion.getFaultAlarm());
                        nowVersion.setDeviceCapacity(lastVersion.getDeviceCapacity());
                        nowVersion.setDeviceBtnDic(lastVersion.getDeviceBtnDic());
                    }
                    deviceModelVersionService.save(nowVersion);
                }
                initDeviceInfoByScreen(deviceEntity);
            } catch (Exception e) {
                log.error("可能会因为线程安全问题导致model 重复  如果触发了唯一键冲突 不用理会", e);
            }
            deviceService.update(deviceEntityLambdaUpdateWrapper);
        }
    }

    @Resource
    private OpsDeviceScreenInfoService opsDeviceScreenInfoService;

    /**
     * 持久化屏幕信息
     */
    @Async
    public void initDeviceInfoByScreen(DeviceEntity deviceEntity) {
        VideoNodeBaseParam param = new VideoNodeBaseParam();
        param.setDeviceId(deviceEntity.getId());
        param.setDeviceCode(deviceEntity.getDeviceCode());
        param.setDeviceEntity(deviceEntity);
        DtoResult<CommonGetScreenPropertiesResp> screenInfo = videoCommonService.getScreenInfo(param);
        log.info("initDeviceInfoByScreen.screenInfo={}", JSON.toJSONString(screenInfo));
        if (screenInfo.success()) {
            OpsDeviceScreenInfoEntity opsDeviceScreenInfoEntity = new OpsDeviceScreenInfoEntity();
            opsDeviceScreenInfoEntity.setId(deviceEntity.getId());
            opsDeviceScreenInfoEntity.setScreenSize(screenInfo.getData().getDScreenSize());
            opsDeviceScreenInfoEntity.setUiWidth(screenInfo.getData().getUiWidth());
            opsDeviceScreenInfoEntity.setUiHeight(screenInfo.getData().getUiHeight());
            opsDeviceScreenInfoEntity.setUiDpi(screenInfo.getData().getUiDpi());
            opsDeviceScreenInfoEntity.setUiSupportTouchFlag(screenInfo.getData().getUiSupportTouchFlag());
            opsDeviceScreenInfoEntity.setUiScreenBrightness(screenInfo.getData().getUiScreenBrightness());
            opsDeviceScreenInfoEntity.setUiVideoEncAbility(screenInfo.getData().getUiVideoEncAbility());
            opsDeviceScreenInfoEntity.setUiMaxBitRate(screenInfo.getData().getUiMaxBitRate());
            opsDeviceScreenInfoEntity.setOffScreenTime(screenInfo.getData().getOffScreenTime());
            opsDeviceScreenInfoService.saveOrUpdate(opsDeviceScreenInfoEntity);
        }
    }
}

