package com.saida.services.system.peopleDeployControl.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.saida.services.algorithm.entity.AlgorithmManageEntity;
import com.saida.services.common.entity.BasePageInfoEntity;
import com.saida.services.common.tools.JwtUtil;
import com.saida.services.constant.RedisConstants;
import com.saida.services.entities.base.BaseRequest;
import com.saida.services.system.alarm.entity.AlarmEntity;
import com.saida.services.system.algorithm.service.AlgorithmManageService;
import com.saida.services.system.analyse.pojo.entity.TimeTemplateEntity;
import com.saida.services.system.analyse.service.TimeTemplateService;
import com.saida.services.system.basicData.dto.BasicBatchDeleteDto;
import com.saida.services.system.basicData.entity.BasicGroupEntity;
import com.saida.services.system.basicData.service.BasicGroupService;
import com.saida.services.system.device.entity.CameraEntity;
import com.saida.services.system.device.service.CameraService;
import com.saida.services.system.peopleDeployControl.dto.PeopleIdentifyDeployControlPageQryDto;
import com.saida.services.system.peopleDeployControl.entity.DeployControlAlgorithmRef;
import com.saida.services.system.peopleDeployControl.entity.DeployControlDeviceChannelRef;
import com.saida.services.system.peopleDeployControl.entity.DeployControlGroupRef;
import com.saida.services.system.peopleDeployControl.entity.PeopleIdentifyDeployControlEntity;
import com.saida.services.system.peopleDeployControl.mapper.DeployControlAlgorithmRefMapper;
import com.saida.services.system.peopleDeployControl.mapper.DeployControlDeviceChannelRefMapper;
import com.saida.services.system.peopleDeployControl.mapper.DeployControlGroupRefMapper;
import com.saida.services.system.peopleDeployControl.mapper.PeopleIdentifyDeployControlMapper;
import com.saida.services.system.peopleDeployControl.service.PeopleIdentifyDeployControlService;
import com.saida.services.system.peopleDeployControl.vo.PeopleIdentifyDeployControlAddDto;
import com.saida.services.system.peopleDeployControl.vo.PeopleIdentifyDeployControlEditDto;
import com.saida.services.system.peopleDeployControl.vo.PeopleIdentifyDeployControlPageQryVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 人员识别布控(PeopleIdentifyDeployControlEntity)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-17 09:42:38
 */
@Slf4j
@Service("peopleIdentifyDeployControlService")
public class PeopleIdentifyDeployControlServiceImpl extends ServiceImpl<PeopleIdentifyDeployControlMapper, PeopleIdentifyDeployControlEntity> implements PeopleIdentifyDeployControlService {
    @Autowired
    private BasicGroupService basicGroupService;
    @Autowired
    private CameraService cameraService;
    @Resource
    private AlgorithmManageService algorithmManageService;
    @Resource
    private TimeTemplateService timeTemplateService;
    @Resource
    private DeployControlGroupRefMapper deployControlGroupRefMapper;
    @Resource
    private DeployControlDeviceChannelRefMapper deployControlDeviceChannelRefMapper;
    @Resource
    private DeployControlAlgorithmRefMapper deployControlAlgorithmRefMapper;
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;


    @Override
    public BasePageInfoEntity<PeopleIdentifyDeployControlPageQryVo> listPage(PeopleIdentifyDeployControlPageQryDto peopleIdentifyDeployControlDto, BaseRequest baseRequest) {
        try (Page<PeopleIdentifyDeployControlPageQryVo> page = PageHelper.startPage(baseRequest.getPageNum(), baseRequest.getPageSize())) {
            List<PeopleIdentifyDeployControlPageQryVo> list = this.baseMapper.listPage(peopleIdentifyDeployControlDto);
            if (CollectionUtil.isEmpty(list)) {
                return new BasePageInfoEntity<>(new PageInfo<>(list));
            }
            fillAttr(list);
            return new BasePageInfoEntity<>(page);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean insert(PeopleIdentifyDeployControlAddDto dto) {
        PeopleIdentifyDeployControlEntity identifyDeployControlEntity = new PeopleIdentifyDeployControlEntity();
        BeanUtils.copyProperties(dto, identifyDeployControlEntity);
        identifyDeployControlEntity.setStatus(0);
        boolean b = this.saveOrUpdate(identifyDeployControlEntity);
        dto.getGroupIds().forEach(groupId -> {
            deployControlGroupRefMapper.insert(new DeployControlGroupRef(identifyDeployControlEntity.getId(), groupId));
        });
        dto.getChannelIds().forEach(channelId -> {
            deployControlDeviceChannelRefMapper.insert(new DeployControlDeviceChannelRef(identifyDeployControlEntity.getId(), channelId));
        });
        dto.getAlgorithmIds().forEach(algorithmId -> {
            deployControlAlgorithmRefMapper.insert(new DeployControlAlgorithmRef(identifyDeployControlEntity.getId(), algorithmId));
        });

        //删除缓存
        redisTemplate.delete(RedisConstants.CONV_PEOPLE_DEPLOY_CONTROL_RECORD);
        return b;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean update(PeopleIdentifyDeployControlEditDto dto) {
        PeopleIdentifyDeployControlEntity identifyDeployControlEntity = new PeopleIdentifyDeployControlEntity();
        BeanUtils.copyProperties(dto, identifyDeployControlEntity);
        identifyDeployControlEntity.setUpdateBy(JwtUtil.getUserId());
        identifyDeployControlEntity.setUpdateTime(LocalDateTime.now());
        int i = this.baseMapper.updateById(identifyDeployControlEntity);
        if (i > 0) {
            deployControlGroupRefMapper.delete(new LambdaQueryWrapper<DeployControlGroupRef>().eq(DeployControlGroupRef::getDeployControlId, identifyDeployControlEntity.getId()));
            dto.getGroupIds().forEach(groupId -> {
                deployControlGroupRefMapper.insert(new DeployControlGroupRef(identifyDeployControlEntity.getId(), groupId));
            });
            deployControlDeviceChannelRefMapper.delete(new LambdaQueryWrapper<DeployControlDeviceChannelRef>().eq(DeployControlDeviceChannelRef::getDeployControlId, identifyDeployControlEntity.getId()));
            dto.getChannelIds().forEach(channelId -> {
                deployControlDeviceChannelRefMapper.insert(new DeployControlDeviceChannelRef(identifyDeployControlEntity.getId(), channelId));
            });
            deployControlAlgorithmRefMapper.delete(new LambdaQueryWrapper<DeployControlAlgorithmRef>().eq(DeployControlAlgorithmRef::getDeployControlId, identifyDeployControlEntity.getId()));
            dto.getAlgorithmIds().forEach(algorithmId -> {
                deployControlAlgorithmRefMapper.insert(new DeployControlAlgorithmRef(identifyDeployControlEntity.getId(), algorithmId));
            });
        }
        return i > 0;
    }

    @Override
    public Boolean delete(BasicBatchDeleteDto dto) {
        //这里没有批量删除，直接取0
        String id = dto.getIds().get(0);
        boolean b = this.removeById(id);
        if (b) {
            deployControlGroupRefMapper.delete(new LambdaQueryWrapper<DeployControlGroupRef>().eq(DeployControlGroupRef::getDeployControlId, id));
            deployControlDeviceChannelRefMapper.delete(new LambdaQueryWrapper<DeployControlDeviceChannelRef>().eq(DeployControlDeviceChannelRef::getDeployControlId, id));
            deployControlAlgorithmRefMapper.delete(new LambdaQueryWrapper<DeployControlAlgorithmRef>().eq(DeployControlAlgorithmRef::getDeployControlId, id));
        }
        return b;
    }

    @Override
    public List<PeopleIdentifyDeployControlEntity> queryIsExist(AlarmEntity alarmEntity) {
        // 查询是否有布控.过滤设备id，云服务id，状态启用
        if (Objects.isNull(alarmEntity.getDeviceId()) || Objects.isNull(alarmEntity.getAlgorithmId())) {
            return null;
        }
        String redisKey = RedisConstants.CONV_PEOPLE_DEPLOY_CONTROL_RECORD;
        String hashKey = alarmEntity.getAlgorithmId() + "_" + alarmEntity.getDeviceId();
        ObjectMapper objectMapper = new ObjectMapper();
        List<PeopleIdentifyDeployControlEntity> list = null;
        if (Boolean.TRUE.equals(redisTemplate.hasKey(redisKey))) {
            Object cachedData = redisTemplate.opsForHash().get(redisKey, hashKey);
            if (cachedData != null) {
                String json;
                try {
                    json = objectMapper.writeValueAsString(cachedData);
                    list = objectMapper.readValue(json, objectMapper.getTypeFactory().constructCollectionType(List.class, PeopleIdentifyDeployControlEntity.class));
                } catch (JsonProcessingException e) {
                    log.error("人员布控-redis中配置反序列化数据失败！algorithmId:{},deviceId:{}", alarmEntity.getAlgorithmId(), alarmEntity.getDeviceId(), e);
                }
            }
        }else {
            redisTemplate.expire(redisKey, 1, TimeUnit.HOURS);
        }
        if (CollectionUtil.isEmpty(list)) {
            list = this.baseMapper.queryListByDeviceAndCloudServerId(alarmEntity.getDeviceId(), alarmEntity.getAlgorithmId());
            if (CollectionUtil.isEmpty(list)) {
                return null;
            }
            try {
                String json = objectMapper.writeValueAsString(list);
                redisTemplate.opsForHash().put(redisKey,hashKey, json);
            } catch (JsonProcessingException e) {
                log.error("人员布控-redis中配置序列化数据失败！algorithmId:{},deviceId:{}", alarmEntity.getAlgorithmId(), alarmEntity.getDeviceId(), e);
            }
        }
        List<TimeTemplateEntity> templateEntityList = timeTemplateService.listByIds(list.stream().map(PeopleIdentifyDeployControlEntity::getTimeTemplateId).filter(Objects::nonNull).collect(Collectors.toList()));
        if (CollectionUtil.isEmpty(templateEntityList)) {
            //没有配置时间模版的默认7*24h
            return list;
        }
        String alarmTime = alarmEntity.getAlarmTime();
        //转LocalDateTime
        LocalDateTime alarmDateTime;
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            alarmDateTime = LocalDateTime.parse(alarmTime, formatter);
        } catch (Exception e) {
            log.error("校验时间模版时间转换异常", e);
            return null;
        }
        LocalDateTime finalAlarmDateTime = alarmDateTime;
        List<TimeTemplateEntity> collect = templateEntityList.stream().filter(templateEntity -> timeTemplateService.isInTimeTemplate(templateEntity, finalAlarmDateTime)).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(collect)) {
            return null;
        }
        //过滤掉有时间模版切满足条件和没有时间模版的布控
        return list.stream().filter(control -> control.getTimeTemplateId() == null || collect.stream().anyMatch(template -> template.getId().equals(control.getTimeTemplateId()))).collect(Collectors.toList());
    }

    private void fillAttr(List<PeopleIdentifyDeployControlPageQryVo> list) {
        //分组名称
        List<Long> groupIds = list.stream().map(PeopleIdentifyDeployControlPageQryVo::getGroupIds).flatMap(List::stream).distinct().collect(Collectors.toList());
        Map<Long, String> mapGroup = basicGroupService.listByIds(groupIds).stream().collect(Collectors.toMap(BasicGroupEntity::getId, BasicGroupEntity::getName));
        //设备通道名称
        List<String> channelIds = list.stream().map(PeopleIdentifyDeployControlPageQryVo::getChannelIds).flatMap(List::stream).distinct().collect(Collectors.toList());
        Map<String, String> mapChannel = cameraService.list(new LambdaQueryWrapper<CameraEntity>().in(CameraEntity::getChannelId, channelIds)).stream().collect(Collectors.toMap(CameraEntity::getChannelId, CameraEntity::getChannelName));
        //算法名称
        List<Long> algorithmIds = list.stream().map(PeopleIdentifyDeployControlPageQryVo::getAlgorithmIds).flatMap(List::stream).distinct().collect(Collectors.toList());
        Map<Long, String> mapAlgorithm = algorithmManageService.listByIds(algorithmIds).stream().collect(Collectors.toMap(AlgorithmManageEntity::getId, AlgorithmManageEntity::getName));
        //时间模板名称
        List<Long> timeTemplateIds = list.stream().map(PeopleIdentifyDeployControlPageQryVo::getTimeTemplateId).collect(Collectors.toList());
        Map<Long, String> mapTimeTemplate = timeTemplateService.listByIds(timeTemplateIds).stream().collect(Collectors.toMap(TimeTemplateEntity::getId, TimeTemplateEntity::getName));
        list.forEach(vo -> {
            List<Long> groupIds1 = vo.getGroupIds();
            if (CollectionUtil.isNotEmpty(groupIds1)) {
                vo.setGroupNames(groupIds1.stream().map(mapGroup::get).collect(Collectors.joining(",")));
            }
            List<String> channelIds1 = vo.getChannelIds();
            if (CollectionUtil.isNotEmpty(channelIds1)) {
                vo.setChannelNames(channelIds1.stream().map(mapChannel::get).collect(Collectors.joining(",")));
            }
            Long timeTemplateId = vo.getTimeTemplateId();
            if (Objects.nonNull(timeTemplateId)) {
                vo.setTimeTemplateName(mapTimeTemplate.get(timeTemplateId));
            }
            List<Long> algorithmIds1 = vo.getAlgorithmIds();
            if (CollectionUtil.isNotEmpty(algorithmIds1)) {
                vo.setAlgorithmNames(algorithmIds1.stream().map(mapAlgorithm::get).collect(Collectors.joining(",")));
            }
        });
    }
}
