package com.saida.services.system;

import com.saida.services.common.feign.FeignConfiguration;
import com.saida.services.tools.PrintUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;
import org.springframework.core.env.Environment;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

@Slf4j
@EnableRetry
@ComponentScan(basePackages = {"com.saida.services.**", "com.saida.project.services.**"})
@EnableDiscoveryClient
@EnableScheduling
@EnableAsync
@EnableFeignClients(basePackages = "com.saida.services")
@Import(FeignConfiguration.class)
@SpringBootApplication
public class ConvergeSystemApplication {
    public static void main(String[] args) {
        ConfigurableApplicationContext configurableApplicationContext = SpringApplication.run(ConvergeSystemApplication.class, args);
        Environment environment = configurableApplicationContext.getBean(Environment.class);
        PrintUtil.print(environment.getProperty("server.servlet.context-path"), environment.getProperty("server.port"));
    }
}
