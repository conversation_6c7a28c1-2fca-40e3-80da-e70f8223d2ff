package com.saida.services.system.analyse.controller;

import com.saida.services.common.base.DtoResult;
import com.saida.services.common.entity.BasePageInfoEntity;
import com.saida.services.common.vo.BaseEnumVo;
import com.saida.services.entities.base.BaseRequest;
import com.saida.services.system.algorithm.dto.AddTaskByBatchDto;
import com.saida.services.system.analyse.pojo.entity.AnalyseTaskBatchEntity;
import com.saida.services.system.analyse.pojo.entity.AnalyseTaskBatchStatusEntity;
import com.saida.services.system.analyse.pojo.entity.AnalyseTaskBatchTaskEntity;
import com.saida.services.system.analyse.pojo.vo.AnalyseTaskListPageVo;
import com.saida.services.system.analyse.service.AnalyseTaskBatchService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;


@RestController
@RequestMapping("/analyseTaskBatch")
public class AnalyseTaskBatchController {

    @Resource
    private AnalyseTaskBatchService analyseTaskBatchService;

    @GetMapping("/list")
    public DtoResult<List<AnalyseTaskBatchEntity>> list(AnalyseTaskBatchEntity entity) {
        return analyseTaskBatchService.list(entity);
    }

    @PostMapping("/add")
    public DtoResult<Void> add(@RequestBody AnalyseTaskBatchEntity entity) {
        return analyseTaskBatchService.add(entity);
    }

    @PostMapping("/edit")
    public DtoResult<Void> edit(@RequestBody AnalyseTaskBatchEntity entity) {
        return analyseTaskBatchService.edit(entity);
    }

    @GetMapping("/getStatus")
    public DtoResult<Integer> getStatus() {
        return analyseTaskBatchService.getStatus();
    }

    @PostMapping("/editStatus")
    public DtoResult<Void> editStatus(@RequestBody AnalyseTaskBatchStatusEntity entity) {
        return analyseTaskBatchService.editStatus(entity);
    }

    @PostMapping("/delete")
    public DtoResult<Void> delete(@RequestBody AnalyseTaskBatchEntity entity) {
        return analyseTaskBatchService.delete(entity);
    }

    @GetMapping("/getNextSerialNumber")
    public DtoResult<Integer> getNextSerialNumber(AnalyseTaskBatchEntity entity) {
        return analyseTaskBatchService.getNextSerialNumber(entity);
    }

    @GetMapping("/getAnalyzeTimeList")
    public DtoResult<List<BaseEnumVo<Integer>>> getAnalyzeTimeList() {
        return analyseTaskBatchService.getAnalyzeTimeList();
    }

    @GetMapping("/getTaskListPage")
    public DtoResult<BasePageInfoEntity<AnalyseTaskListPageVo>> getTaskListPage(AnalyseTaskBatchEntity entity, BaseRequest baseRequest) {
        return analyseTaskBatchService.getTaskListPage(entity, baseRequest);
    }

    @GetMapping("/getTaskList")
    public DtoResult<List<AnalyseTaskListPageVo>> getTaskListPage(AnalyseTaskBatchEntity entity) {
        return analyseTaskBatchService.getTaskList(entity);
    }

    @PostMapping("/addTask")
    public DtoResult<Void> addTask(@RequestBody AddTaskByBatchDto dto) {
        return analyseTaskBatchService.addTask(dto);
    }

    @PostMapping("/deleteTask")
    public DtoResult<Void> deleteTask(@RequestBody AnalyseTaskBatchTaskEntity analyseTaskBatchTaskEntity) {
        return analyseTaskBatchService.deleteTask(analyseTaskBatchTaskEntity);
    }

    @PostMapping("/sendMq")
    public DtoResult<Void> sendMq() {
        return analyseTaskBatchService.sendMq();
    }
}