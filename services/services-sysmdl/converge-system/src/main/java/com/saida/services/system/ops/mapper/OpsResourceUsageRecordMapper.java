package com.saida.services.system.ops.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.saida.services.converge.entity.OpsResourceUsageRecordEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 资源使用率
 *
 * <AUTHOR>
 * email ${email}
 * date 2023-10-31 16:43:41
 */
@Mapper
public interface OpsResourceUsageRecordMapper extends BaseMapper<OpsResourceUsageRecordEntity> {


    List<OpsResourceUsageRecordEntity> getList(@Param("entity") OpsResourceUsageRecordEntity entity);

    OpsResourceUsageRecordEntity getInfoById(@Param("entity") OpsResourceUsageRecordEntity entity);

    List<OpsResourceUsageRecordEntity> getListByDays(@Param("entity") OpsResourceUsageRecordEntity entity);

}
