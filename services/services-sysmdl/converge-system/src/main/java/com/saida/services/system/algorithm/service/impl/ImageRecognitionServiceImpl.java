package com.saida.services.system.algorithm.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.saida.services.algorithm.entity.AlgorithmMappingEntity;
import com.saida.services.algorithm.entity.ImageRecognitionEntity;
import com.saida.services.common.base.DtoResult;
import com.saida.services.common.base.Result;
import com.saida.services.common.service.FileService;
import com.saida.services.common.tools.JwtUtil;
import com.saida.services.common.tools.RedisUtil;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.entities.pojo.FileModel;
import com.saida.services.system.algorithm.mapper.AlgorithmMappingMapper;
import com.saida.services.system.algorithm.mapper.ImageRecognitionMapper;
import com.saida.services.system.algorithm.service.ImageRecognitionService;
import com.saida.services.system.rocketMq.config.RocketMqTopic;
import com.saida.services.system.rocketMq.config.TaskMessageProducer;
import com.saida.services.system.rocketMq.message.TaskForImgReqMessage;
import com.saida.services.system.rocketMq.message.TaskForImgRespMessage;
import com.saida.services.system.utils.CommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.async.DeferredResult;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class ImageRecognitionServiceImpl extends ServiceImpl<ImageRecognitionMapper, ImageRecognitionEntity> implements ImageRecognitionService {


    @Resource
    private AlgorithmMappingMapper algorithmMappingMapper;
    @Resource
    private FileService fileService;
    @Resource
    private TaskMessageProducer taskMessageProducer;
    @Resource
    private RedisUtil redisUtil;

    @Override
    public DeferredResult<Result> insertEntity(ImageRecognitionEntity entity) {
        //最多等待15s
        DeferredResult<Result> result = new DeferredResult<>(15 * 1000L);
        result.onTimeout(() -> result.setResult(Result.error("任务执行超时")));
        result.onError(e -> {
            log.error("任务执行异常, e:", e);
            result.setResult(Result.error("任务执行异常"));
        });
        // 异步执行任务
        CompletableFuture.runAsync(() -> {
            try {
                Long uuid = IdWorker.getId();
                TaskForImgReqMessage taskForImgReqMessage = new TaskForImgReqMessage();
                taskForImgReqMessage.setUuid(uuid);
                taskForImgReqMessage.setUrl(entity.getBaseImgUrl());
                taskForImgReqMessage.setAlgorithmName(entity.getRecoAlgorithm());
                taskMessageProducer.sendSync(RocketMqTopic.TASK_FOR_IMG, taskForImgReqMessage);
                String redisKey = "algorithm:task_for_img_resp:" + uuid;
                //堵塞查询这个redis是否有值 如果有说明任务执行完成
                while (true) {
                    try {
                        Thread.sleep(500);
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                    Object o = redisUtil.get(redisKey);
                    if (o != null) {
                        //删掉这个key
                        redisUtil.del(redisKey);
                        //任务结束了
                        TaskForImgRespMessage taskForImgRespMessage = JSON.parseObject(String.valueOf(o), TaskForImgRespMessage.class);
                        if (taskForImgRespMessage.getType() == 1) {
                            String analysisImgBase64 = taskForImgRespMessage.getBase64();
                            String analysisImageUrl = null;
                            try {
                                DtoResult<FileModel> jpg = fileService.uploadBase64(analysisImgBase64, "jpg", "alarm/img-analysis");
                                if (jpg.success()) {
                                    analysisImageUrl = jpg.getData().getUrl();
                                }
                            } catch (Exception e) {
                                log.error("图片上传minio，错误...{}", e.getMessage(), e);
                            }
                            entity.setAnalysisImgUrl(analysisImageUrl);
                            entity.setStatus(1);

                            entity.setCreateUser(JwtUtil.getUserId());
                            entity.setCreateTime(DateTime.now());
                            //生成编号
                            String format = DateUtil.format(new Date(), "yyyyMMddHHmmss");
                            entity.setNumber("txjc" + format);
                            save(entity);
                            result.setResult(Result.ok("任务识别成功", entity));
                            return;
                        } else {
                            result.setResult(Result.error("任务识别失败"));
                            return;
                        }

                    }
                }

            } catch (Exception e) {
                log.info("识别异常, e:", e);
                entity.setStatus(0);
                result.setResult(Result.error("任务识别失败"));
            }
        });
        return result;
    }

    @Override
    public IPage<ImageRecognitionEntity> listPage(ImageRecognitionEntity entity) {
        List<String> recoAlgorithms = new ArrayList<>();
        if (StringUtil.isNotEmpty(entity.getRecoAlgorithm())) {
            recoAlgorithms = Arrays.asList(entity.getRecoAlgorithm().split(","));
        }
        IPage<ImageRecognitionEntity> page = baseMapper.selectPage(new Page<>(entity.getPageNum(), entity.getPageSize()),
                new LambdaQueryWrapper<ImageRecognitionEntity>()
                        .in(!recoAlgorithms.isEmpty(), ImageRecognitionEntity::getRecoAlgorithm, recoAlgorithms) // 使用 in 查询
                        .eq(entity.getStatus() != null, ImageRecognitionEntity::getStatus, entity.getStatus())
                        .ge(entity.getBegTime() != null, ImageRecognitionEntity::getCreateTime, entity.getBegTime()) // 开始时间
                        .le(entity.getEndTime() != null, ImageRecognitionEntity::getCreateTime, entity.getEndTime())     // 结束时间
                        .orderByDesc(ImageRecognitionEntity::getId)
        );
        if (page == null || page.getRecords() == null || page.getRecords().isEmpty()) {
            return page;
        }
        return page;
    }

    @Override
    public void batchDelete(String ids) {
        List<Long> list = CommonUtil.convertLongList(ids);
        this.removeByIds(list);
    }

    @Override
    public List<String> queryAlgorithmList() {
        List<AlgorithmMappingEntity> algorithmMappingEntities = algorithmMappingMapper.selectList(null);
        return algorithmMappingEntities.stream()
                .map(AlgorithmMappingEntity::getName) // 提取 name
                .distinct() // 去重
                .collect(Collectors.toList());
    }
}
