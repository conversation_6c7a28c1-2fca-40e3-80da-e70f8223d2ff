package com.saida.services.system.analyse.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.saida.services.common.base.DtoResult;
import com.saida.services.common.base.Result;
import com.saida.services.entities.base.BaseRequest;
import com.saida.services.entities.pojo.CountDto;
import com.saida.services.system.analyse.pojo.dto.AddAnalyseTaskDto;
import com.saida.services.system.analyse.pojo.dto.AnalyseTaskListPageDto;
import com.saida.services.system.analyse.pojo.dto.UpdateAnalyseTaskDto;
import com.saida.services.system.analyse.pojo.entity.AnalyseTaskEntity;
import com.saida.services.system.analyse.pojo.vo.AnalyseTaskByBatchIdVo;

import java.util.List;

public interface AnalyseTaskService extends IService<AnalyseTaskEntity> {

    Result listPage(AnalyseTaskListPageDto dto, BaseRequest baseRequest);

    Result list(AnalyseTaskListPageDto dto);

    Result info(AnalyseTaskEntity entity);

    Result addChannel(AddAnalyseTaskDto dto);

    Result add(AddAnalyseTaskDto dto);

    Result edit(UpdateAnalyseTaskDto dto);

    Result delete(AnalyseTaskEntity entity);

    Result enablePause(AnalyseTaskEntity entity);

    Result getSnapshotUrl(Long deviceId, Integer times);

    /**
     * 获取分析任务设备数量
     */
    Integer getAnalyseDeviceCount();

    /**
     * 获取分析灵敏度列表
     */
    Result getAnalyseSensitivityList();

    /**
     * 获取分析帧率列表
     */
    Result getAnalyzeFpsList();

    /**
     * 获取告警间隔列表
     */
    Result getAlarmIntervalList();

    /**
     * 获取分析任务数量
     *
     * @param algorithmSource 算法来源 可为空
     */
    List<CountDto> getAnalyseTaskCount(Long algorithmSource);

    /**
     * 获取云端分析任务数量
     *
     * @param cloudServerId 云服务ID 可为空
     */
    List<CountDto> getCloudAnalyseTaskCount(Long cloudServerId);

    List<CountDto> edgeTaskNum();

    Result getAlgorithmList(String sourceId);

    List<AnalyseTaskByBatchIdVo> getAnalyseTaskByBatchId(Long batchId, Integer enable);

    DtoResult<Void> batchUpdateTask(UpdateAnalyseTaskDto dto);
}