package com.saida.services.system.video.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName DormancyVo
 * @Desc
 * @Date 2024/11/14 16:39
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DormancyParameterVo {
    //是否使能，0关闭，1使能
    private Integer enable;
    //时间段["10:00-12:00",...]
    private List<String> sleepTimer;
}
