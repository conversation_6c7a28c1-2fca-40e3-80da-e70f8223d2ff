package com.saida.services.system.algorithm.controller;

import com.saida.services.algorithm.entity.AlgorithmBasicConfigEntity;
import com.saida.services.common.base.DtoResult;
import com.saida.services.log.LogOperation;
import com.saida.services.log.LogOperationEnum;
import com.saida.services.log.ModuleEnum;
import com.saida.services.system.algorithm.service.AlgorithmBasicConfigService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;


@RestController
@RequestMapping("/algorithm/basicConfig")
public class AlgorithmBasicConfigController {

    @Resource
    private AlgorithmBasicConfigService algorithmBasicConfigService;

    @PostMapping("/edit")
    @LogOperation(type = LogOperationEnum.ADDOREDIT, func = "算法基本配置编辑", module = ModuleEnum.ATTRIBUTE)
    public DtoResult<Void> edit(@RequestBody AlgorithmBasicConfigEntity entity) {
        return algorithmBasicConfigService.edit(entity);
    }

    @GetMapping("/info")
    public DtoResult<AlgorithmBasicConfigEntity> info(AlgorithmBasicConfigEntity entity) {
        return algorithmBasicConfigService.info(entity);
    }
}