package com.saida.services.system.analyse.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.saida.services.common.entity.BaseEntity;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@TableName("analyse_task_group")
@JsonInclude
public class AnalyseTaskGroupEntity extends BaseEntity<AnalyseTaskGroupEntity> {
    private static final long serialVersionUID = 1L;

    private String name;
}