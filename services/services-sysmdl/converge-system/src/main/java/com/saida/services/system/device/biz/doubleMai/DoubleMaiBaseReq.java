package com.saida.services.system.device.biz.doubleMai;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * DoubleMaiBaseReq类用于封装虚拟集群基础请求信息。
 * 该类实现了Serializable接口，使得对象可以被序列化，便于在网络传输中使用。
 */
@Getter
@Setter
public class DoubleMaiBaseReq implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * accessToken字段用于存储访问令牌。
     * 访问令牌用于验证请求的授权信息，确保请求的安全性。
     */
    private String accessToken;
}
