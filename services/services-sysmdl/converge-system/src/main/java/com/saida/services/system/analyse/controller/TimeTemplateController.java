package com.saida.services.system.analyse.controller;

import com.saida.services.common.base.DtoResult;
import com.saida.services.common.base.Result;
import com.saida.services.system.analyse.pojo.vo.TimeTemplateVO;
import com.saida.services.system.analyse.service.TimeTemplateService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;


@RestController
@RequestMapping("/time/plan")
public class TimeTemplateController {

    @Resource
    private TimeTemplateService timeTemplateService;

    /**
     * 获取时间模板列表
     *
     * @param entity TimeTemplateVO对象，包含查询条件
     * @return Result对象，包含时间模板列表及操作结果信息
     */
    @GetMapping("/list")
    public Result list(TimeTemplateVO entity) {
        return timeTemplateService.list(entity);
    }

    /**
     * 获取时间模板信息
     *
     * @param entity 时间模板查询条件对象
     * @return Result 返回结果对象，包含时间模板信息以及操作结果
     */
    @GetMapping("/info")
    public Result info(TimeTemplateVO entity) {
        return timeTemplateService.info(entity);
    }

    /**
     * 添加时间模板
     *
     * @param entity 时间模板对象，通过RequestBody注解接收前端传递的JSON数据，并将其反序列化为TimeTemplateVO对象
     * @return 返回操作结果，包括操作是否成功、错误信息等，封装在Result对象中
     */
    @PostMapping("/add")
    public DtoResult<Long> add(@RequestBody TimeTemplateVO entity) {
        return timeTemplateService.add(entity);
    }

    /**
     * 编辑时间模板
     *
     * @param entity 时间模板对象，通过RequestBody注解接收前端传递的JSON数据，并将其反序列化为TimeTemplateVO对象
     * @return Result对象，表示编辑时间模板的结果，包含操作是否成功、错误信息等
     */
    @PostMapping("/edit")
    public DtoResult<Long> edit(@RequestBody TimeTemplateVO entity) {
        return timeTemplateService.edit(entity);
    }

    /**
     * 删除时间模板
     *
     * @param entity 时间模板对象，包含要删除的时间模板的ID或其他标识信息
     * @return Result 返回删除操作的结果，包含操作是否成功、错误信息等
     */
    @PostMapping("/delete")
    public Result delete(@RequestBody TimeTemplateVO entity) {
        return timeTemplateService.delete(entity);
    }
}