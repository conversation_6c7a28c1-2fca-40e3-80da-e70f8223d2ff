package com.saida.services.system.sys.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.saida.services.algorithm.entity.ThirdPartyEntity;
import com.saida.services.system.device.entity.CameraEntity;
import com.saida.services.system.sys.dto.ThirdpartyDeviceReq;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 平台订阅管理
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2024-01-02 10:59:35
 */
@Mapper
public interface ThirdPartyMapper extends BaseMapper<ThirdPartyEntity> {

    IPage<CameraEntity> deviceBindListPage(Page<CameraEntity> cameraEntityPage, @Param("req") ThirdpartyDeviceReq req);

    IPage<CameraEntity> deviceUnBindListPage(Page<CameraEntity> cameraEntityPage, @Param("req") ThirdpartyDeviceReq req);
}
