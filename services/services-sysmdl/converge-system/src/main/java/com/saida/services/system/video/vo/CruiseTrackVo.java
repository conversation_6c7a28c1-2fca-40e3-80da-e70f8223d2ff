package com.saida.services.system.video.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName CruiseTrackVo
 * @Desc
 * @Date 2024/11/15 11:30
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CruiseTrackVo {
    //巡航速度
    private Integer speed;
    //0:关闭, 1:开启
    private Integer enable;

    private List<Preset> presetlist;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Preset {
        private String name;
        //为预制点索引的数组，表示要巡航的预制点
        private Integer preset;
        //预置位停留时间
        private Integer pauseTime;
    }
}
