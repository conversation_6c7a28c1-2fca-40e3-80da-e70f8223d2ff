package com.saida.services.system.basicData.service.impl;

import cn.hutool.core.util.ArrayUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.saida.services.algorithm.enums.CloudTypeEnum;
import com.saida.services.common.base.DtoResult;
import com.saida.services.system.basicData.dto.BasicBatchUpdateGroupDto;
import com.saida.services.system.basicData.entity.BasicPeopleFaceFeatureEntity;
import com.saida.services.system.basicData.mapper.BasicPeopleFaceFeatureMapper;
import com.saida.services.system.basicData.service.BasicPeopleFaceFeatureService;
import com.saida.services.system.client.nodev1.ResponseDto;
import com.saida.services.system.device.entity.CloudServerEntity;
import com.saida.services.system.device.service.CloudServerService;
import com.saida.services.system.face.CloudServerApiEnum;
import com.saida.services.system.face.CloudServerReqUtil;
import com.saida.services.system.face.CosineMatcher;
import com.saida.services.system.face.dto.FaceDto;
import com.saida.services.system.face.dto.MatchResult;
import com.saida.services.system.face.req.SaidaFaceFeatureReq;
import com.saida.services.system.face.resp.SaidaFaceFeatureResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 */
@Slf4j
@Service
public class BasicPeopleFaceFeaTureServiceImpl extends ServiceImpl<BasicPeopleFaceFeatureMapper, BasicPeopleFaceFeatureEntity> implements BasicPeopleFaceFeatureService {

    // 缓存
    private static final Map<Long, CosineMatcher> cosineMatcherMap = new HashMap<>();

    private DtoResult<CosineMatcher> getCosineMatcher(Long groupId) {
        if (!cosineMatcherMap.containsKey(groupId)) {
            List<BasicPeopleFaceFeatureEntity> list = super.list(new LambdaQueryWrapper<BasicPeopleFaceFeatureEntity>()
                    .eq(BasicPeopleFaceFeatureEntity::getGroupId, groupId));
            if (list == null || list.isEmpty()) {
                return DtoResult.error("没有可用的人脸特征数据");
            }
            FaceDto[] faceDtos = new FaceDto[list.size()];
            final int[] idx = {0};
            list.forEach((peopleFaceFeatureEntity) -> {
                String featureStr = peopleFaceFeatureEntity.getFeature();
                // 逗号拼接的文本转double[]
                double[] feature = Arrays.stream(featureStr.split(",")).mapToDouble(Double::parseDouble).toArray();
                faceDtos[idx[0]] = FaceDto.builder()
                        .groupId(peopleFaceFeatureEntity.getGroupId())
                        .peopleId(peopleFaceFeatureEntity.getPeopleId())
                        .faceId(peopleFaceFeatureEntity.getFaceId())
                        .feature(feature)
                        .build();
                idx[0]++;
            });
            CosineMatcher cosineMatcher = new CosineMatcher(faceDtos);
            cosineMatcherMap.put(groupId, cosineMatcher);
        }
        return DtoResult.ok(cosineMatcherMap.get(groupId));
    }

    @Override
    public DtoResult<MatchResult[]> faceComparison(Long groupId, FaceDto faceDto, int topK) {
        DtoResult<CosineMatcher> result = getCosineMatcher(groupId);
        if (!result.success()) {
            return DtoResult.error(result.getMessage());
        }
        CosineMatcher cosineMatcher = result.getData();
        MatchResult[] matchResults = cosineMatcher.matchTopK(faceDto.getFeature(), topK);
        return DtoResult.ok(matchResults);
    }

    @Override
    public DtoResult<MatchResult[]> faceComparisons(Long[] groupId, FaceDto faceDto, int topK) {
        FaceDto[] faceDtos = new FaceDto[0];
        for (Long aLong : groupId) {
            DtoResult<CosineMatcher> cosineMatcher = getCosineMatcher(aLong);
            if (!cosineMatcher.success()) {
                continue;
            }
            faceDtos = ArrayUtil.append(faceDtos, cosineMatcher.getData().getFaceDtos());
        }
        MatchResult[] matchResults = CosineMatcher.matchSelTopK(faceDto.getFeature(), faceDtos, topK);
        return DtoResult.ok(matchResults);
    }

    @Override
    public DtoResult<Double> faceComparisonOne(FaceDto faceDto1, FaceDto faceDto2) {
        double match = CosineMatcher.match(faceDto1.getFeature(), faceDto2.getFeature());
        return DtoResult.ok(match);
    }

    @Resource
    private CloudServerService cloudServerService;
    @Resource
    private CloudServerReqUtil cloudServerReqUtil;

    @Override
    public DtoResult<FaceDto> getFaceFeature(Long groupId, Long peopleId, Long faceId, String deviceId, String faceBase64) {
        DtoResult<FaceDto> saidaFaceFeatureRespDtoResult = saidaFaceFeature(deviceId, faceBase64);
        if (!saidaFaceFeatureRespDtoResult.success()) {
            return DtoResult.error(saidaFaceFeatureRespDtoResult.getMessage());
        }
        double[] feature = saidaFaceFeatureRespDtoResult.getData().getFeature();
        BasicPeopleFaceFeatureEntity basicPeopleFaceFeatureEntity = new BasicPeopleFaceFeatureEntity();
        // 逗号拼接
        String featureStr = Arrays.stream(feature).mapToObj(Double::toString).collect(Collectors.joining(","));
        basicPeopleFaceFeatureEntity.setFeature(featureStr);
        basicPeopleFaceFeatureEntity.setFaceId(faceId);
        basicPeopleFaceFeatureEntity.setPeopleId(peopleId);
        basicPeopleFaceFeatureEntity.setGroupId(groupId);
        basicPeopleFaceFeatureEntity.setSort(0);
        this.save(basicPeopleFaceFeatureEntity);
        if (cosineMatcherMap.containsKey(groupId)) {
            FaceDto faceDto = FaceDto.builder()
                    .groupId(groupId)
                    .peopleId(peopleId)
                    .faceId(faceId)
                    .feature(feature)
                    .build();
            cosineMatcherMap.get(groupId).addFaceDto(faceDto);
        }
        return DtoResult.ok(saidaFaceFeatureRespDtoResult.getData());
    }


    public DtoResult<FaceDto> saidaFaceFeature(String deviceId, String faceBase64) {
        if (faceBase64 == null || faceBase64.length() < 10) {
            return DtoResult.error("请上传图片");
        }
        List<CloudServerEntity> cloudServerList = cloudServerService.list(new LambdaQueryWrapper<CloudServerEntity>()
                .eq(CloudServerEntity::getCloudType, CloudTypeEnum.VLINKER.getType())
                .like(CloudServerEntity::getInterfaceUrl, "http"));
        if (cloudServerList == null || cloudServerList.isEmpty()) {
            return DtoResult.error("没有可用的人脸识别算法服务");
        }
        if (deviceId == null) {
            deviceId = "DEFAULT";
        }
        for (CloudServerEntity cloudServerEntity : cloudServerList) {
            ResponseDto responseDto = cloudServerReqUtil.sendRequest(CloudServerApiEnum.SAIDA_FACE_FEATURE, cloudServerEntity.getInterfaceUrl(),
                    SaidaFaceFeatureReq.builder()
                            .deviceId(deviceId)
                            .base64Image(faceBase64)
                            .algCode("N_EXT_FACE")
                            .build());
            if (responseDto.getHttpCode() == 200) {
                SaidaFaceFeatureResp saidaFaceFeatureResp = JSON.parseObject(responseDto.getRes(), SaidaFaceFeatureResp.class);
                if (saidaFaceFeatureResp.getSaidaRepose().getData().size() > 1) {
                    return DtoResult.error("识别到了多个人脸，请重新上传图片");
                }
                FaceDto faceDto = new FaceDto();
                List<Double> feature = saidaFaceFeatureResp.getSaidaRepose().getData()
                        .get(0).getFeature();
                if (feature == null || feature.isEmpty()) {
                    return DtoResult.error("识别结果为空");
                }
                double[] normalize = CosineMatcher.toPrimitive(feature);
                normalize = CosineMatcher.normalize(normalize);
                faceDto.setFeature(normalize);
                faceDto.setX(saidaFaceFeatureResp.getSaidaRepose().getData().get(0).getRect().getX());
                faceDto.setY(saidaFaceFeatureResp.getSaidaRepose().getData().get(0).getRect().getY());
                faceDto.setW(saidaFaceFeatureResp.getSaidaRepose().getData().get(0).getRect().getW());
                faceDto.setH(saidaFaceFeatureResp.getSaidaRepose().getData().get(0).getRect().getH());
                return DtoResult.ok(faceDto);
            }
        }
        return DtoResult.error("识别失败，所有服务无法识别到结果");
    }

    @Override
    public DtoResult<List<FaceDto>> saidaFaceFeatures(String deviceId, String faceBase64) {
        if (faceBase64 == null || faceBase64.length() < 10) {
            return DtoResult.error("请上传图片");
        }
        List<CloudServerEntity> cloudServerList = cloudServerService.list(new LambdaQueryWrapper<CloudServerEntity>()
                .eq(CloudServerEntity::getCloudType, CloudTypeEnum.VLINKER.getType())
                .like(CloudServerEntity::getInterfaceUrl, "http"));
        if (cloudServerList == null || cloudServerList.isEmpty()) {
            return DtoResult.error("没有可用的人脸识别算法服务");
        }
        if (deviceId == null) {
            deviceId = "DEFAULT";
        }

        for (CloudServerEntity cloudServerEntity : cloudServerList) {
            ResponseDto responseDto = cloudServerReqUtil.sendRequest(CloudServerApiEnum.SAIDA_FACE_FEATURE, cloudServerEntity.getInterfaceUrl(),
                    SaidaFaceFeatureReq.builder()
                            .deviceId(deviceId)
                            .base64Image(faceBase64)
                            .algCode("N_EXT_FACE")
                            .build());
            if (responseDto.getHttpCode() == 200) {
                SaidaFaceFeatureResp saidaFaceFeatureResp = JSON.parseObject(responseDto.getRes(), SaidaFaceFeatureResp.class);
                List<SaidaFaceFeatureResp.SaidaReposeDto.DataDto> faceList = saidaFaceFeatureResp.getSaidaRepose().getData();
                if (faceList.isEmpty()) {
                    return DtoResult.error("没有识别到人脸");
                }
                List<FaceDto> faceDtos = new ArrayList<>();
                for (SaidaFaceFeatureResp.SaidaReposeDto.DataDto dataDto : faceList) {
                    FaceDto faceDto = new FaceDto();
                    List<Double> feature = dataDto.getFeature();
                    if (feature == null || feature.isEmpty()) {
                        continue;
                    }
                    double[] normalize = CosineMatcher.toPrimitive(feature);
                    normalize = CosineMatcher.normalize(normalize);
                    faceDto.setFeature(normalize);
                    faceDto.setX(dataDto.getRect().getX());
                    faceDto.setY(dataDto.getRect().getY());
                    faceDto.setW(dataDto.getRect().getW());
                    faceDto.setH(dataDto.getRect().getH());
                    faceDtos.add(faceDto);
                }
                return DtoResult.ok(faceDtos);
            }
        }
        return DtoResult.error("识别失败，所有服务无法识别到结果");
    }

    @Override
    public DtoResult<Void> delFace(Long groupId, Long faceId) {
        super.remove(new LambdaQueryWrapper<BasicPeopleFaceFeatureEntity>()
                .eq(BasicPeopleFaceFeatureEntity::getFaceId, faceId));
        if (cosineMatcherMap.containsKey(groupId)) {
            cosineMatcherMap.get(groupId).deleteFaceDto(faceId);
        }
        return DtoResult.ok();
    }

    @Override
    public boolean updateGroup(BasicBatchUpdateGroupDto dto) {
        cosineMatcherMap.remove(dto.getGroupId());
        return this.update(new LambdaUpdateWrapper<BasicPeopleFaceFeatureEntity>().set(BasicPeopleFaceFeatureEntity::getGroupId, dto.getGroupId()).in(BasicPeopleFaceFeatureEntity::getPeopleId, dto.getPeopleIds()));
    }
}
