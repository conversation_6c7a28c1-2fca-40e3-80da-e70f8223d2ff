package com.saida.services.system.ops.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.saida.services.converge.entity.CloudStorageEntity;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

/**
 * 云存服务
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-10-24 17:11:13
 */
public interface CloudStorageService extends IService<CloudStorageEntity> {

    @Getter
    @AllArgsConstructor
    enum ActionEnum{
        /**
         * 新增或修改
         */
        EDIT("EDIT"),

        /**
         * 删除
         */
        DEL("DEL");

        private final String action;
    }

    /**
     * 添加或修改云存服务配置
     * @param entity
     */
    void addOrUpdate(CloudStorageEntity entity);

    /**
     * 分页查询云存服务配置
     * @param entity
     * @return
     */
    IPage<CloudStorageEntity> listPage(CloudStorageEntity entity);

    /**
     * 查询云存服务配置列表
     * @param entity
     * @return
     */
    List<CloudStorageEntity> getList(CloudStorageEntity entity);

    /**
     * 查询云存服务配置详情
     * @param id
     * @return
     */
    CloudStorageEntity getInfo(Long id);

    /**
     * 删除云存配置
     * @param id
     */
    void delete(Long id);

    void syncMq(Long id, ActionEnum action);

    void addCloudStorageRule(CloudStorageEntity entity);
}

