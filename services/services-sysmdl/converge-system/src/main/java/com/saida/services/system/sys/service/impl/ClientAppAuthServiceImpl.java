package com.saida.services.system.sys.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.saida.services.common.config.ThreadPoolConfig;
import com.saida.services.common.tools.IdGenerateUtil;
import com.saida.services.common.tools.JwtUtil;
import com.saida.services.common.tools.RedisUtil;
import com.saida.services.constant.RedisConstants;
import com.saida.services.system.sys.entity.ClientAppAuthEntity;
import com.saida.services.system.sys.mapper.ClientAppAuthMapper;
import com.saida.services.system.sys.service.ClientAppAuthService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


@Service("clientAppAuthService")
public class ClientAppAuthServiceImpl extends ServiceImpl<ClientAppAuthMapper, ClientAppAuthEntity> implements ClientAppAuthService {

    @Autowired
    private RedisUtil redisUtil;

    @Override
    public void addOrUpdate(ClientAppAuthEntity entity) {
        if (entity.getId() == null) {
            entity.setCreateUser(JwtUtil.getUserId());
            entity.setCreateTime(DateTime.now());

            entity.setAppKey(String.format("%s%s%s", RandomUtil.randomString(3), IdGenerateUtil.generateInviteCode62(), RandomUtil.randomString(3)));
            entity.setAppSecret(IdUtil.fastSimpleUUID());

            save(entity);
            cacheInfo(entity.getId());
        } else {
            entity.setUpdateUser(JwtUtil.getUserId());
            entity.setUpdateTime(DateTime.now());

            entity.setAppKey(null);
            entity.setAppSecret(null);

            updateById(entity);
            cacheInfo(entity.getId());
        }
    }

    @Override
    public IPage<ClientAppAuthEntity> listPage(ClientAppAuthEntity entity) {
        return page(new Page<ClientAppAuthEntity>(entity.getPageNum(), entity.getPageSize()));
    }

    @Override
    public void delete(Long id) {
        ClientAppAuthEntity clientApp = getById(id);
        if (clientApp == null) {
            return;
        }
        redisUtil.del(String.format(RedisConstants.CLIENT_APP, clientApp.getAppKey()));
    }

    private void cacheInfo(Long id) {
        ClientAppAuthEntity clientApp = getById(id);
        if (clientApp == null) {
            return;
        }
        redisUtil.set(String.format(RedisConstants.CLIENT_APP, clientApp.getAppKey()), JSON.toJSONString(clientApp));
    }


    @Resource
    private ThreadPoolConfig threadPoolConfig;


    @EventListener(ApplicationReadyEvent.class)
    public void clientAppAuthInit() {
        threadPoolConfig.taskRunner(() -> {
            list().forEach(e -> {
                cacheInfo(e.getId());
            });
        });
    }
}