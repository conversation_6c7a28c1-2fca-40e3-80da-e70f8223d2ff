package com.saida.services.system.ops.vo;

import com.saida.services.converge.entity.OpsPlatformEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @ClassName OpsPlatformVo
 * @Desc
 * @Date 2024/10/22 11:12
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OpsPlatformVo extends OpsPlatformEntity {
    /**
     * 在线状态1:在线 0:离线
     */
    private String onlineStatus;

    /**
     * 网络连接状态 1:在线 0:离线
     */
    private String networkStatus;

    private String nodeName;

    private String virtualOrgName;
}
