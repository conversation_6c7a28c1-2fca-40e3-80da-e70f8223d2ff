package com.saida.services.system.ops.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.saida.services.common.base.Result;
import com.saida.services.converge.entity.DeviceModelEntity;
import com.saida.services.converge.entity.DeviceModelVersionEntity;

import java.util.List;

/**
 * 设备型号版本
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-10-27 14:29:26
 */
public interface DeviceModelVersionService extends IService<DeviceModelVersionEntity> {

    /**
     * 添加或更新设备模型版本信息
     * @param entity 设备模型版本实体对象，包含版本信息
     */
    void addOrUpdate(DeviceModelVersionEntity entity);

    /**
     * 分页查询设备模型版本信息
     * @param entity 查询条件实体对象，包含设备模型版本的相关查询条件
     * @return 返回分页查询结果，包含设备模型版本的信息
     */
    IPage<DeviceModelVersionEntity> listPage(DeviceModelVersionEntity entity);

    /**
     * 查询设备模型版本列表
     * @param entity 查询条件实体对象，包含设备模型版本的相关查询条件
     * @return 返回设备模型版本列表
     */
    List<DeviceModelVersionEntity> getList(DeviceModelVersionEntity entity);

    /**
     * 根据ID查询设备模型版本信息
     * @param id 设备模型版本的ID
     * @return 返回设备模型版本的详细信息
     */
    DeviceModelVersionEntity getInfo(Long id);
    /**
     * 根据设备id查询设备模型版本信息
     * @param id 设备模型版本的ID
     * @return 返回设备模型版本的详细信息
     */
    DeviceModelVersionEntity getInfoByDeviceId(Long id);

    /**
     * 根据ID删除设备模型版本信息
     * @param id 设备模型版本的ID
     */
    void delete(Long id);

    /**
     * 根据设备型号和版本号获取安装包下载地址
     * @param model 设备型号
     * @param versionNum 版本号
     * @return 安装包的下载地址
     */
    String getDownloadUrl(String model, String versionNum);

    /**
     * 根据ID获取安装包下载地址
     * @param id 设备模型版本的ID
     * @return 安装包的下载地址
     */
    String getDownloadUrl(Long id);

    /**
     * 更新设备的版本信息
     * @param versionId 设备模型版本ID
     * @param devIds 设备ID列表，用逗号分隔
     * @return 返回更新结果
     */
    Result updateDevice(Long versionId, String devIds);

    /**
     * 查询设备型号版本
     * @param model
     * @return
     */
    DeviceModelVersionEntity getInfoByModelAndVersion(String model, String version);


    DeviceModelEntity buildDeviceVersion(String modelStr, String version);
}

