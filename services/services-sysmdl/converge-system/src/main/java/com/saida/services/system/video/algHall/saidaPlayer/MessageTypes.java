package com.saida.services.system.video.algHall.saidaPlayer;

public class MessageTypes {
    // MajorType 使用 byte 类型
    public static final byte MajorTypeHandshake = 1;    // 服务端会解析头，做一些处理的消息
    public static final byte MajorTypeSignaling = 2;
    public static final byte MajorTypeAlarm = 3;
    public static final byte MajorTypeFile = 4;
    public static final byte MajorTypeData = 5;

    public static final byte MajorTypeMedia = (byte) 0xff;  // 服务端不会拆包，不会解析消息头，直接转发的内容
    public static final byte MajorTypeMediaAddonData = (byte) 0xfe;
    public static final byte MajorTypeMediaControl = (byte) 0xfd;
    public static final byte MajorTypeWebRTC = (byte) 0xfc;

    // MinorType 使用 int 类型（模拟 uint16）
    public static final int MinorTypeHandshakeForSignaling = 0xe1;
    public static final int MinorTypeHandshakeForWebRTC = 0xe2;
    public static final int MinorTypeHandshakeForMedia = 0xe3;

    public static final int MinorTypeSignalingJoinRoomToReceiveMedia = 0x100; // 假设 pb.SignalingType 值
    public static final int MinorTypeSignalingChannelOnline = 0x200;
    public static final int MinorTypeSignalingChannelOffline = 0x201;
    public static final int MinorTypeSignalingUploadChannel = 0x202;
    public static final int MinorTypeSignalingPlay = 0x203;
    public static final int MinorTypeSignalingStopPlay = 0x204;
    public static final int MinorTypeSignalingFileDownload = 0x205;
    public static final int MinorTypeSignalingNewWebRTCConnection = 0x206;

    public static final int MinorTypeDataJPEGSnap = 1;
    public static final int MinorTypeDataPNGSnap = 2;

    public static final int MinorTypeMediaRawAudio = 1;
    public static final int MinorTypeMediaRawVideo = 2;
    public static final int MinorTypeMediaProgramStream = 3;

    public static final int MinorTypeMediaControlSpeed = 1;      // body 1个有符号小端序int 表示2的N次方，比如-1表示 0.5倍速,-2表示0.25倍速,1表示2倍速,0还原为1倍速
    public static final int MinorTypeMediaControlJump = 2;       // body 1个无符号小端序uint64 秒为单位 回放开始时间,秒
    public static final int MinorTypeMediaControlPause = 3;      // 无body 暂停
    public static final int MinorTypeMediaControlContinue = 4;   // 无body 继续播放
    public static final int MinorTypeMediaControlKeyFrame = 5;

    public static final int MinorTypeMediaAddonDataMediaInfoProtoBufferData = 1;
    public static final int MinorTypeMediaAddonDataQuantumCryptographyKeyID = 2;
    public static final int MinorTypeMediaAddonDataAIResult = 3;

    public static final int MinorTypeWebRTCICEServers = 1;
    public static final int MinorTypeWebRTCICECandidate = 2;
    public static final int MinorTypeWebRTCSDP = 3;
}
