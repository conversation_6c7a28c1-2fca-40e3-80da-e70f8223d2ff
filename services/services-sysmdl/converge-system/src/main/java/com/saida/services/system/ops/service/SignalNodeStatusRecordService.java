package com.saida.services.system.ops.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.saida.services.common.base.Result;
import com.saida.services.converge.entity.SignalNodeStatusRecordEntity;

/**
 * 信令节点状态历史记录
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-11-03 15:02:59
 */
public interface SignalNodeStatusRecordService extends IService<SignalNodeStatusRecordEntity> {

    Result recordListPage(SignalNodeStatusRecordEntity entity);
}

