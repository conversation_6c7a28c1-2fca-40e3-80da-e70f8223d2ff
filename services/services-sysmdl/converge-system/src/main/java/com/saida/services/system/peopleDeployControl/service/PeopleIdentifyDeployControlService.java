package com.saida.services.system.peopleDeployControl.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.saida.services.common.entity.BasePageInfoEntity;
import com.saida.services.entities.base.BaseRequest;
import com.saida.services.system.alarm.entity.AlarmEntity;
import com.saida.services.system.basicData.dto.BasicBatchDeleteDto;
import com.saida.services.system.peopleDeployControl.dto.PeopleIdentifyDeployControlPageQryDto;
import com.saida.services.system.peopleDeployControl.entity.PeopleIdentifyDeployControlEntity;
import com.saida.services.system.peopleDeployControl.vo.PeopleIdentifyDeployControlAddDto;
import com.saida.services.system.peopleDeployControl.vo.PeopleIdentifyDeployControlEditDto;
import com.saida.services.system.peopleDeployControl.vo.PeopleIdentifyDeployControlPageQryVo;

import java.util.List;

/**
 * 人员识别布控(PeopleIdentifyDeployControlEntity)表服务接口
 *
 * <AUTHOR>
 * @since 2025-06-17 09:42:37
 */
public interface PeopleIdentifyDeployControlService extends IService<PeopleIdentifyDeployControlEntity> {


    BasePageInfoEntity<PeopleIdentifyDeployControlPageQryVo> listPage(PeopleIdentifyDeployControlPageQryDto peopleIdentifyDeployControlDto, BaseRequest request);

    Boolean insert(PeopleIdentifyDeployControlAddDto dto);

    Boolean update(PeopleIdentifyDeployControlEditDto dto);

    Boolean delete(BasicBatchDeleteDto dto);

    /**
     * 查询是否存在满足条件的布控数据
     *
     * @return
     */
    List<PeopleIdentifyDeployControlEntity> queryIsExist(AlarmEntity alarmEntity);
}
