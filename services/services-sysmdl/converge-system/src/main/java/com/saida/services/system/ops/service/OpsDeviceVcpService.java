package com.saida.services.system.ops.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.saida.services.common.entity.BasePageInfoEntity;
import com.saida.services.converge.entity.OpsDeviceVcpEntity;
import com.saida.services.converge.entity.dto.OpsDeviceVcpDto;
import com.saida.services.entities.base.BaseRequest;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2024年04月12日11:54:14
 */
public interface OpsDeviceVcpService extends IService<OpsDeviceVcpEntity> {


    BasePageInfoEntity<OpsDeviceVcpDto> listPage(OpsDeviceVcpDto entity, BaseRequest baseRequest);

    List<OpsDeviceVcpDto> getListByDeviceCodes(List<String> deviceCodes);
}

