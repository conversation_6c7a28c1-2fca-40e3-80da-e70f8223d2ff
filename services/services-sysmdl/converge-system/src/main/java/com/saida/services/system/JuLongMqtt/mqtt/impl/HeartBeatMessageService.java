package com.saida.services.system.JuLongMqtt.mqtt.impl;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.saida.services.converge.entity.DeviceEntity;
import com.saida.services.system.JuLongMqtt.dto.message.req.HeartBeatReq;
import com.saida.services.system.JuLongMqtt.dto.message.resp.HeartBeatResp;
import com.saida.services.system.JuLongMqtt.mqtt.MqttProviderConfig;
import com.saida.services.system.JuLongMqtt.mqtt.OnMessageService;
import com.saida.services.system.ops.service.DeviceService;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


@Slf4j
@Component("JuLongMqttMessage_HeartBeat")
public class HeartBeatMessageService implements OnMessageService {

    @Resource
    private DeviceService deviceService;

    @Override
    public void onMessage(MqttProviderConfig mqttProviderConfig,String topic, MqttMessage message) {
        String msg = new String(message.getPayload());

        HeartBeatReq heartBeatReq = JSON.parseObject(msg, HeartBeatReq.class);
        log.info("heartbeat message:{}", heartBeatReq);

        DeviceEntity device = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                .eq(DeviceEntity::getSn, heartBeatReq.getDeviceUUID()), false);
        HeartBeatResp heartBeatResp = new HeartBeatResp();
        heartBeatResp.setDeviceUUID(heartBeatReq.getDeviceUUID());
        heartBeatResp.setTaskID(heartBeatReq.getTaskID());
        if (device != null) {
            heartBeatResp.setRet(0);
        } else {
            heartBeatResp.setRet(1);
        }
        mqttProviderConfig.publish("mqtt/HeartBeatAck/" + heartBeatReq.getDeviceUUID(), message);
    }
}
