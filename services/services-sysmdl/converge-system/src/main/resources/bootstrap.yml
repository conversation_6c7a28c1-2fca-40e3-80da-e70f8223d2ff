nacos_addr: 127.0.0.1:8848
nacos_name_space: v-linker-v2
spring:
  mvc:
    async:
      request-timeout: 86400000
  application:
    name: converge-system-server
  profiles:
    active: test
    include: dic
  cloud:
    nacos:
      discovery:
        server-addr: ${nacos_addr}
        namespace: ${nacos_name_space}
        fail-fast: false
        heartbeat-interval: 5000      # 心跳间隔（默认 5 秒）
        heart-beat-timeout: 15000     # 心跳超时（默认 15 秒）
      config:
        server-addr: ${nacos_addr}
        namespace: ${nacos_name_space}
        file-extension: yml
        shared-configs:
          - data-id: shared-common.yml
          - data-id: shared-mybatis.yml
          - data-id: shared-redis.yml
          - data-id: shared-minio.yml
          - data-id: shared-s3.yml
          - data-id: shared-xxljob.yml
          - data-id: shared-rocketmq.yml
          - data-id: shared-mqtt.yml
          - data-id: shared-network.yml
          - data-id: alg-saida-label-name.yml
            refresh: true  # 启用自动刷新