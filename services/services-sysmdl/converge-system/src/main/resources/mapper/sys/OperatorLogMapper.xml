<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.saida.services.system.sys.mapper.OperatorLogMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.saida.services.system.sys.entity.OperatorLogEntity" id="operatorLogMap">
        <result property="id" column="id"/>
        <result property="orgId" column="org_id"/>
        <result property="operatorTime" column="operator_time"/>
        <result property="userName" column="user_name"/>
        <result property="account" column="account"/>
        <result property="userId" column="user_id"/>
        <result property="source" column="source"/>
        <result property="ipAddress" column="ip_address"/>
        <result property="func" column="func"/>
        <result property="type" column="type"/>
        <result property="module" column="module"/>
        <result property="des" column="des"/>
        <result property="result" column="result"/>
        <result property="url" column="url"/>
        <result property="parameterMap" column="parameter_map"/>
        <result property="postBody" column="post_body"/>
    </resultMap>

</mapper>