<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.saida.services.system.gis.mapper.GisLayerInfoMapper">


    <select id="getList" resultType="com.saida.services.system.gis.entity.GisLayerInfoEntity">

        SELECT
        t1.*
        FROM
        gis_layer_info t1
        left join gis_group_info t2 on t1.group_id = t2.id
        WHERE 1 = 1
        <if test="param.subLevel != null and param.subLevel">
            <if test="param.groupId != null ">
                and t2.id_chain like concat(#{param.groupIdChain}, '%')
            </if>
        </if>
        <if test="param.subLevel != null and !param.subLevel">
            <if test="param.groupId != null ">
                and t1.group_id = #{param.groupId}
            </if>
        </if>
        <if test="param.name != null and param.name != ''">
            and t1.name like concat('%', #{param.name}, '%')
        </if>
        <if test="param.dataType != null ">
            and t1.data_type = #{param.dataType}
        </if>
        <if test="param.serverType != null ">
            and t1.server_type = #{param.serverType}
        </if>
        <if test="param.dataPermissions != null ">
            and t1.data_permissions = #{param.dataPermissions}
        </if>
        ORDER BY t1.id desc
    </select>
</mapper>