<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.saida.services.system.sys.mapper.ClientAppMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.saida.services.system.sys.entity.ClientAppEntity" id="clientAppMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="industry" column="industry"/>
        <result property="orderId" column="order_id"/>
        <result property="appKey" column="app_key"/>
        <result property="secretKey" column="secret_key"/>
        <result property="status" column="status"/>
        <result property="briefing" column="briefing"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="getListByDevice" resultType="com.saida.services.system.sys.entity.ClientAppEntity">
        SELECT
            sca.*
        FROM
            device_info di
            LEFT JOIN sys_client_device scd ON di.id = scd.device_id
            LEFT JOIN sys_client_app sca ON sca.id = scd.client_id
        WHERE
             di.deleted = 0 and sca.id IS NOT NULL
            <if test="deviceInfoEntity.id != null and deviceInfoEntity.id != ''">
                AND di.id = #{deviceInfoEntity.id}
            </if>
            <if test="deviceInfoEntity.tripartiteSn != null and deviceInfoEntity.tripartiteSn != ''">
                AND di.tripartite_sn = #{deviceInfoEntity.tripartiteSn}
            </if>
    </select>


</mapper>