<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.saida.services.system.sys.mapper.CapabilityParamMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.saida.services.system.sys.entity.CapabilityParamEntity" id="capabilityParamMap">
        <result property="id" column="id"/>
        <result property="capabilityId" column="capability_id"/>
        <result property="fieldName" column="field_name"/>
        <result property="remark" column="remark"/>
        <result property="type" column="type"/>
        <result property="required" column="required"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>


</mapper>