server:
  port: 7003
  servlet:
    context-path: /open-system

spring:
  servlet:
    multipart:
      enabled: true
      max-request-size: 1024MB
      max-file-size: 1024MB
  main:
    allow-bean-definition-overriding: true
  # 数据源配置
  datasource:
    url: *************************************************************************************************************************************
    username: zhangjinchao
    password: 4%e#NzfNVQBdpPCD%MW
    driver-class-name: com.mysql.cj.jdbc.Driver

    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      # 客户端等待连接池连接的最大毫秒数
      connection-timeout: 30000
      # 池中维护的最小空闲连接数 minIdle<0 或者 minIdle>maxPoolSize，则被重置为maxPoolSize
      minimum-idle: 10
      # 配置最大池大小
      maximum-pool-size: 65
      # 允许连接在连接池中空闲的最长时间（以毫秒为单位）
      idle-timeout: 60000
      # 池中连接关闭后的最长生命周期（以毫秒为单位）
      max-lifetime: 500000
      # 每5分钟发送一次 ping
      keepalive-time: 300000
      # 配置从池返回的连接的默认自动提交行为。默认值为true。
      auto-commit: true
      # 连接池的名称
      pool-name: VlinkerHikariCP
      # 开启连接监测泄露
      leak-detection-threshold: 5000
      # 测试连接数据库
      connection-test-query: SELECT 1

  redis:
    host: *********
    port: 6379
    database: 6
    password: xXU5SBWUbYD8ReBi.s
    timeout: 10000
    jedis:
      pool:
        min-idle: 10
        max-active: 30

  jackson:
    default-property-inclusion: non_null
    generator:
      write-numbers-as-strings: true

  sleuth:
    enabled: true

mybatis-plus:
  type-aliases-package: com.saida.services.system.**.entity
  # xxxMapper.xml 路径
  mapper-locations: classpath:/mapper/**/*Mapper.xml
  global-config:
    # 主键类型  0:"数据库ID自增", 1:"用户输入ID",2:"全局唯一ID (数字类型唯一ID)", 3:"全局唯一ID UUID";
    id-type: 0
    # 字段策略 0:"忽略判断",1:"非 NULL 判断",2:"非空判断"
    field-strategy: 1
    # 驼峰下划线转换
    db-column-underline: true

sysconfig:
  uploadPath: v_file

minio:
  enable: true
  endpoint: **********
  port: 9000
  accessKey: SaidaMinio
  secretKey: Saida@Minio123
  secure: false
  bucketDefaultName: v-linker-open
  returnPoint: https://dev-minio-api.sdccx.cn:58801

xxl:
  job:
    accessToken: default_token
    admin:
      addresses: http://127.0.0.1:12003/xxl-job-admin
    enabled: false
    executor:
      address: ''
      appname: ${spring.application.name}
      ip: ''
      logpath: logs/console
      logretentiondays: 7
      port: 0
    i18n: zh_CN
    logretentiondays: 30
    triggerpool:
      fast:
        max: 200
      slow:
        max: 100

## 天翼视联授权信息
ty:
  visual:
    host: https://vcp.21cn.com
    appId: 6807674319
    appSecret: 99bcb2772bc945b09e530ec0b66f0fe4
    version: v1.0
    clintType: 3
    privateKey: MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAIkYPPVQP9rbNdVdhHBP+L8KhdpqOPRQORk/PlRcwGl7OdYOX9pvC1qTu1oq0eYV1kNc7xxVUdysDcaiHKAPN84qDvGge34EyW/P1SqaeyykJE7uXmzIQqj8EbFsaHy8gUur13/ittytOEYy6B6tIQwsC3shG/L4ZglV6f/ruY9hAgMBAAECgYBzZMZyidClq5vdczC1xGqQiw/trzkvU5MsEBKrucJvRryA02qVFqDM5cpt89rnOvw1iTTueVZFU0DpL6y+gPCryUo8QEOze5uOwRILKdb0gS+IunitRbi58jQOCKNdssjE2bL3Ju7fFQmdBlO5mQYMfYj5uSkzy0ZkF4pBbUOGnQJBAL5k6B7Feit0xDol7n/HEwa1PWPRkwHK/QksdzBGe4YqMCuXHgeu5xT551Aa/IPrWjyPtJXTSlK52IUewSAzNlsCQQC4Vao/Wj/K6V+bfbdpIxGu5tC1lkb+lEww59piqdky24UrbY5wrY6BK8izbxQwT9IBWl/kzhdN++djqgISjpXzAkBTEo4hFwL2QQ15XaGRiRfpHl0513BwINSFEyyyGJ6+7Ft2OYp1yb+z7FIcJpo1KRSHRiCCGt3b+JDbjI6xcQbvAkAe3claPmR8N8PYp550rnihjujX30gYDQENF2XMK6DBEV0lPn0hhQ5dttUupqT8ygDiVR3FlHT2i/3dBHTAYrvdAkEAjxQtb5sxNZDi8Jt3iQRl315bsD4Y46UBnXNOhHpen0cjpB1A5/yzx7cmnlYEf+AbbykATfO7D0YOTXUCgdDRwg==
    enterpriseUser: 1809565483
    callbackUrl: https://dev-vlinker-open-man.sdccx.cn:58801
    callbackPath: /#/vcpDlife


#南向推送 固定一个应用进行使用
vcp:
  card:
    appId: 1744297454889578498

qly:
  video:
    version: 1.0.0

sms:
  signature: 【云视界】
