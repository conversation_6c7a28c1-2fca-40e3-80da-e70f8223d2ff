package com.saida.services.system.vcpDlifeAlarm.service.Impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.saida.services.open.entity.DeviceInfoVcpSubscriptionEntity;
import com.saida.services.system.vcpDlifeAlarm.mapper.DeviceInfoVcpSubscriptionMapper;
import com.saida.services.system.vcpDlifeAlarm.service.DeviceInfoVcpSubscriptionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class DeviceInfoVcpSubscriptionServiceImpl extends ServiceImpl<DeviceInfoVcpSubscriptionMapper, DeviceInfoVcpSubscriptionEntity> implements DeviceInfoVcpSubscriptionService {

}