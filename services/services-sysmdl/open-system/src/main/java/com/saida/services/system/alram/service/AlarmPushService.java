package com.saida.services.system.alram.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.saida.services.algorithm.dto.PushAlarmDto;
import com.saida.services.common.base.DtoResult;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.converge.enums.BaseVideoAlarmTypeEnum;
import com.saida.services.open.entity.AlgorithmManageEntity;
import com.saida.services.open.entity.AlgorithmMappingEntity;
import com.saida.services.open.entity.DeviceInfoEntity;
import com.saida.services.open.enums.OpenAlgorithmSourceEnum;
import com.saida.services.system.sys.service.AlgorithmManageService;
import com.saida.services.system.sys.service.AlgorithmMappingService;
import com.saida.services.system.sys.service.DeviceInfoService;
import com.saida.services.system.thread.AlarmHandleThread;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Component
public class AlarmPushService {

    @Value("${spring.profiles.active:}")
    private String activeProfile;

    @Resource
    private AlarmHandleThread alarmHandleThread;
    @Resource
    private DeviceInfoService deviceInfoService;
    @Resource
    private AlgorithmMappingService algorithmMappingService;
    @Resource
    private AlgorithmManageService algorithmManageService;


    /**
     * 接收V-LINKER算法中台推送的告警数据。
     *
     * @return 返回处理结果的DTO。
     */
    public DtoResult<Void> receiveData(PushAlarmDto pushAlarmDto) {
        log.info("V-LINKER能力开放平台.接收V-LINKER算法中台告警推送...data={}", JSON.toJSON(pushAlarmDto));
        if(Objects.equals("dev",activeProfile) && Objects.equals("alertTimeTest",pushAlarmDto.getAlertTime())){
            pushAlarmDto.setAlertTime(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        }
        // 根据设备码和通道ID查询设备信息
        DeviceInfoEntity deviceInfoEntity = deviceInfoService.getOne(new LambdaUpdateWrapper<DeviceInfoEntity>()
                .eq(DeviceInfoEntity::getTripartiteSn, pushAlarmDto.getDeviceCode())
                .eq(StringUtil.isNotEmpty(pushAlarmDto.getChannelId()), DeviceInfoEntity::getChannelId, pushAlarmDto.getChannelId())
        );
        // 检查设备信息是否存在
        if (null == deviceInfoEntity) {
            log.error("V-LINKER能力开放平台.接收V-LINKER算法中台告警推送..设备信息不存在，程序结束...data={}", JSON.toJSON(pushAlarmDto));
            return DtoResult.error();
        }

        // 设置设备码为设备ID
        pushAlarmDto.setDeviceId(deviceInfoEntity.getId());
        pushAlarmDto.setDeviceCode(deviceInfoEntity.getId());

        // 根据算法来源和告警类型查询算法映射实体列表
        List<AlgorithmMappingEntity> algorithmMappingEntityList = algorithmMappingService.list(new LambdaQueryWrapper<AlgorithmMappingEntity>()
                .eq(AlgorithmMappingEntity::getSourceId, OpenAlgorithmSourceEnum.VLINKER_ALGORITHM.getCode())
                .eq(AlgorithmMappingEntity::getCode, pushAlarmDto.getAlertType())
        );
        // 检查是否存在对应的算法映射
        if (CollectionUtil.isEmpty(algorithmMappingEntityList)) {
            log.error("V-LINKER能力开放平台.接收V-LINKER算法中台告警推送，算法不存在..程序结束...data={}", JSON.toJSON(pushAlarmDto));
            return DtoResult.ok();
        }

        // 查询所有启用的算法管理实体
        List<AlgorithmManageEntity> algorithmManageEntityList = algorithmManageService.list(new LambdaQueryWrapper<AlgorithmManageEntity>()
                .eq(AlgorithmManageEntity::getEnable, 1));
        // 遍历算法映射实体列表，处理每个算法的告警
        PushAlarmDto openPushPushAlarmDto;
        for (AlgorithmMappingEntity algorithmMappingEntity : algorithmMappingEntityList) {
            openPushPushAlarmDto = new PushAlarmDto();
            BeanUtils.copyProperties(pushAlarmDto, openPushPushAlarmDto);

            // 根据算法ID查询对应的算法管理实体
            Optional<AlgorithmManageEntity> optional = algorithmManageEntityList.stream()
                    .filter(t1 -> Objects.equals(t1.getId(), algorithmMappingEntity.getAlgorithmId()))
                    .findFirst();
            // 检查算法管理实体是否存在
            if (!optional.isPresent()) {
                log.error("V-LINKER能力开放平台.接收V-LINKER算法中台告警推送，算法不存在..程序结束...data={}", JSON.toJSON(pushAlarmDto));
                continue;
            }

            AlgorithmManageEntity algorithmManageEntity = optional.get();
            // 设置告警类型为算法ID，告警类型名称为算法名称
            openPushPushAlarmDto.setAlertTypeCode(algorithmManageEntity.getCode());
            openPushPushAlarmDto.setAlertType(String.valueOf(algorithmManageEntity.getId()));
            openPushPushAlarmDto.setAlertTypeName(algorithmManageEntity.getName());

            // 判断是否需要订阅该告警
            boolean needSubscribe = !BaseVideoAlarmTypeEnum.RYCRGJ.getCode().equals(algorithmManageEntity.getCode()) && !BaseVideoAlarmTypeEnum.CLCRGJ.getCode().equals(algorithmManageEntity.getCode());
            log.info("V-LINKER能力开放平台.接收V-LINKER算法中台告警推送，算法详情...algorithmId={}, algorithmName={}, algorithmCode={}, algorithmEnable={}, needSubscribe={}",
                    algorithmManageEntity.getId(), algorithmManageEntity.getName(), algorithmManageEntity.getCode(), algorithmManageEntity.getEnable(), needSubscribe);
            openPushPushAlarmDto.setNeedSubscribe(needSubscribe);
            // 处理告警
            alarmHandleThread.handleAlarm(openPushPushAlarmDto);
        }
        // 返回处理成功的DTO
        return DtoResult.ok();
    }
}