package com.saida.services.system.biz.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.digest.MD5;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.dahuatech.icc.oauth.model.v202010.GeneralResponse;
import com.saida.services.annotction.ThirdPartyPlatformsBizAnno;
import com.saida.services.common.base.DtoResult;
import com.saida.services.common.entity.BasePageInfoEntity;
import com.saida.services.common.tools.CustomerListUtil;
import com.saida.services.common.tools.DateUtil;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.constant.ThirdPartyPlatformsBizComponent;
import com.saida.services.deviceApi.resp.CommonGetRecordMonthResp;
import com.saida.services.entities.base.BaseRequest;
import com.saida.services.enums.BasePtzControlCmdEnum;
import com.saida.services.enums.BaseVideoBackProtocolEnum;
import com.saida.services.enums.BaseVideoProtocolEnum;
import com.saida.services.open.biz.req.*;
import com.saida.services.open.biz.req.pre.ThirdPartyPlatformsBaseDeletePrePointReq;
import com.saida.services.open.biz.req.pre.ThirdPartyPlatformsBaseJumpPrePointReq;
import com.saida.services.open.biz.req.pre.ThirdPartyPlatformsBasePrePointListReq;
import com.saida.services.open.biz.req.pre.ThirdPartyPlatformsBaseSetPrePointReq;
import com.saida.services.open.biz.req.rtc.ThirdPartyPlatformsBaseRtcConnectReq;
import com.saida.services.open.biz.req.rtc.ThirdPartyPlatformsBaseRtcUnConnectReq;
import com.saida.services.open.biz.resp.*;
import com.saida.services.open.deviceApi.req.OpenGetRecordMonthReq;
import com.saida.services.open.dto.DeviceCapacityDto;
import com.saida.services.open.entity.AlgorithmMappingEntity;
import com.saida.services.open.entity.ThirdPartyPlatformsAuthEntity;
import com.saida.services.open.enums.DeviceCapacityEnum;
import com.saida.services.open.enums.OpenAlgorithmSourceEnum;
import com.saida.services.open.enums.OpenThirdPartyPlatformsTypeEnum;
import com.saida.services.system.biz.service.ThirdPartyAlgorithmPlatformsService;
import com.saida.services.system.biz.service.ThirdPartyVideoPlatformsService;
import com.saida.services.system.biz.service.dahuaIcc.DaHuaRedisConstant;
import com.saida.services.system.biz.service.dahuaIcc.biz.DaHuaIccBiz;
import com.saida.services.system.biz.service.dahuaIcc.enums.DaHuaIccApiEnum;
import com.saida.services.system.biz.service.dahuaIcc.enums.DaHuaVideoProtocolEnums;
import com.saida.services.system.biz.service.dahuaIcc.enums.EventCategoryEnums;
import com.saida.services.system.biz.service.dahuaIcc.model.DaHuaCommonIccResponse;
import com.saida.services.open.dahua.resp.DevicePageResponse;
import com.saida.services.system.biz.service.dahuaIcc.model.event.eventSubcribe.SubscribeRequest;
import com.saida.services.system.biz.service.dahuaIcc.model.video.ptzControl.*;
import com.saida.services.system.biz.service.dahuaIcc.model.video.realTimePreview.*;
import com.saida.services.system.biz.service.dahuaIcc.model.video.videoReplay.*;
import com.saida.services.system.sys.entity.OpenThirdPartyPlatformsDeviceEntity;
import com.saida.services.system.sys.entity.ThirdPartyPlatformsTreeEntity;
import com.saida.services.system.sys.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

@Slf4j
@ThirdPartyPlatformsBizAnno(
        componentValue = ThirdPartyPlatformsBizComponent.VideoBizComponent.daHuaBizComponentName,
        componentName = "大华ICC开放平台",
        componentDes = "告警推送地址: 「接口地址+/open-system/dahua/alarmReceive/receiveData」",
        platformType = {OpenThirdPartyPlatformsTypeEnum.VIDEO, OpenThirdPartyPlatformsTypeEnum.ALGORITHM}
)
@Component(ThirdPartyPlatformsBizComponent.VideoBizComponent.daHuaBizComponentName)
public class DaHuaServiceImpl implements ThirdPartyVideoPlatformsService, ThirdPartyAlgorithmPlatformsService {
    @Resource
    private ThirdPartyPlatformsTreeService thirdPartyPlatformsTreeService;
    @Resource
    private ThirdPartyPlatformsAuthService thirdPartyPlatformsAuthService;
    @Resource
    private ThirdPartyPlatformsDeviceService thirdPartyPlatformsDeviceService;
    @Resource
    private DaHuaIccBiz daHuaIccBiz;
    @Lazy
    @Resource
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;
    @Resource
    private RedisTemplate<String, String> redisTemplate;
    @Resource
    private AlgorithmMappingService algorithmMappingService;
    @Resource
    private DeviceInfoService deviceInfoService;
    //大华数据订阅传入的名称前缀，固定取值线上环境能开IP+端口
    private final String SUBSCRIBE_NAME = "192.168.0.118_7003";

    private void tokenTest() {

    }

    //    @VlinkerXxlJob(
//            value = "daHuaRefreshToken",
//            cron = "0 0 */1 * * ?",
//            desc = "大华刷新token"
//    )
    @Scheduled(cron = "0 0 */1 * * ?")
    public void daHuaRefreshToken() {
        List<ThirdPartyPlatformsAuthEntity> list = thirdPartyPlatformsAuthService.list(new LambdaQueryWrapper<ThirdPartyPlatformsAuthEntity>().isNotNull(ThirdPartyPlatformsAuthEntity::getUserName).isNotNull(ThirdPartyPlatformsAuthEntity::getPassword));
        list.forEach(thirdPartyPlatformsAuthEntity -> {
            daHuaIccBiz.getAccessToken(thirdPartyPlatformsAuthEntity);
        });
    }

    @Override
    public DtoResult<List<Tree<String>>> getDeviceTreeList(ThirdPartyPlatformsBaseDeviceTreeReq req) {
        try {
            LambdaQueryWrapper<ThirdPartyPlatformsTreeEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(ThirdPartyPlatformsTreeEntity::getPlatformId, req.getThirdPartyPlatformsAuthEntity().getId());
            List<ThirdPartyPlatformsTreeEntity> list = thirdPartyPlatformsTreeService.list(lambdaQueryWrapper);

            if (CollectionUtil.isEmpty(list)) {
                ThirdPartyPlatformsTreeEntity thirdPartyPlatformsTreeEntity = new ThirdPartyPlatformsTreeEntity();
                thirdPartyPlatformsTreeEntity.setPlatformId(req.getThirdPartyPlatformsAuthEntity().getId());
                thirdPartyPlatformsTreeEntity.setTreeId("0");
                thirdPartyPlatformsTreeEntity.setName("大华ICC开放平台");
                thirdPartyPlatformsTreeEntity.setHasChild(0);
                thirdPartyPlatformsTreeEntity.setTreeIdChain("0");
                thirdPartyPlatformsTreeEntity.setParentTreeId("0");
                thirdPartyPlatformsTreeEntity.setRemark("大华ICC开放平台根节点");
                thirdPartyPlatformsTreeService.save(thirdPartyPlatformsTreeEntity);
            }
            list = thirdPartyPlatformsTreeService.list(lambdaQueryWrapper);
            String rootId = StringUtil.isNotEmpty(req.getParentId()) ? req.getParentId() : "0";
            // 转换器
            List<Tree<String>> treeNodeList = TreeUtil.build(list, rootId, this.getTreeNodeConfig(),
                    (treeNode, tree) -> {
                        tree.setId(String.valueOf(treeNode.getTreeId()));
                        tree.setParentId(treeNode.getParentTreeId());
                        tree.setName(treeNode.getName());
                        tree.put("hasChild", treeNode.getHasChild() == 1);
                    });
            return DtoResult.ok(treeNodeList);
        } catch (Exception e) {
            log.error("获取设备树出错...msg={}", e.getMessage(), e);
        }
        return DtoResult.error();
    }

    @Override
    public DtoResult<BasePageInfoEntity<ThirdPartyPlatformsDeviceListResp>> getDeviceList(ThirdPartyPlatformsBaseDeviceListReq req) {
        BaseRequest baseRequest = new BaseRequest();
        baseRequest.setPageNum(req.getPageNum());
        baseRequest.setPageSize(req.getPageSize());

        String treeIdChain = null;
        if (StringUtil.isNotEmpty(req.getDeviceTreeId())) {
            ThirdPartyPlatformsTreeEntity thirdPartyPlatformsTreeEntity = thirdPartyPlatformsTreeService.getOne(new LambdaQueryWrapper<ThirdPartyPlatformsTreeEntity>()
                    .eq(ThirdPartyPlatformsTreeEntity::getPlatformId, req.getThirdPartyPlatformsAuthEntity().getId())
                    .eq(StringUtil.isNotEmpty(req.getDeviceTreeId()), ThirdPartyPlatformsTreeEntity::getTreeId, req.getDeviceTreeId()), false);
            if (thirdPartyPlatformsTreeEntity == null) {
                return DtoResult.error("设备树不存在");
            }
            treeIdChain = thirdPartyPlatformsTreeEntity.getTreeIdChain();
        }
        BasePageInfoEntity<OpenThirdPartyPlatformsDeviceEntity> deviceListByDeviceTreeId = thirdPartyPlatformsDeviceService.getDeviceListByDeviceTreeId(baseRequest,
                req.getThirdPartyPlatformsAuthEntity().getId(),
                treeIdChain, req.getDeviceCode(),
                req.getSnCode(),
                req.getSubRegion());
        BasePageInfoEntity<ThirdPartyPlatformsDeviceListResp> basePageInfoEntity = new BasePageInfoEntity<>();
        basePageInfoEntity.setTotal(deviceListByDeviceTreeId.getTotal());
        basePageInfoEntity.setCurrent(req.getPageNum());
        basePageInfoEntity.setSize(deviceListByDeviceTreeId.getSize());
        basePageInfoEntity.setPages(deviceListByDeviceTreeId.getPages());
        basePageInfoEntity.setRecords(deviceListByDeviceTreeId.getRecords().stream().map(t1 -> {
            ThirdPartyPlatformsDeviceListResp thirdPartyPlatformsDeviceListResp = new ThirdPartyPlatformsDeviceListResp();
            thirdPartyPlatformsDeviceListResp.setId(String.valueOf(t1.getSnCode()));
            thirdPartyPlatformsDeviceListResp.setPlatformUniqueCode(t1.getSnCode());
            thirdPartyPlatformsDeviceListResp.setName(t1.getName());
            thirdPartyPlatformsDeviceListResp.setDeviceCode(t1.getSnCode());
            thirdPartyPlatformsDeviceListResp.setSnCode(t1.getSnCode());
            thirdPartyPlatformsDeviceListResp.setChannelId(t1.getChannelId());
            thirdPartyPlatformsDeviceListResp.setChannelName(t1.getChannelName());
            if (StringUtil.isNotEmpty(t1.getDeviceModel())) {
                thirdPartyPlatformsDeviceListResp.setDeviceModel(t1.getDeviceModel());
            }

            if (Objects.nonNull(t1.getIsOnline())) {
                thirdPartyPlatformsDeviceListResp.setIsOnline(String.valueOf(t1.getIsOnline()));
            }
            if (StringUtil.isNotEmpty(t1.getVerCode())) {
                thirdPartyPlatformsDeviceListResp.setDeviceVerificationCode(t1.getVerCode());
            }
            //设备能力处理
            thirdPartyPlatformsDeviceListResp.setDeviceCapacity(t1.getDeviceCapacity());
            //大华设备分配61
            thirdPartyPlatformsDeviceListResp.setAccessWay(61);
            return thirdPartyPlatformsDeviceListResp;
        }).collect(Collectors.toList()));
        return DtoResult.ok(basePageInfoEntity);
    }

    @Override
    public void syncTreeAndDeviceJob(ThirdPartyPlatformsAuthEntity thirdPartyPlatformsAuthEntity) {
        thirdPartyPlatformsDeviceService.remove(new LambdaUpdateWrapper<OpenThirdPartyPlatformsDeviceEntity>()
                .eq(OpenThirdPartyPlatformsDeviceEntity::getPlatformId, thirdPartyPlatformsAuthEntity.getId()));
        syncTree(null, null, thirdPartyPlatformsAuthEntity);
    }

    @Override
    public DtoResult<ThirdPartyPlatformsVideoLiveUrlResp> getVideoLiveUrl(ThirdPartyPlatformsBaseVideoLiveUrlReq baseVideoLiveUrlReq) {
        ThirdPartyPlatformsAuthEntity thirdPartyPlatformsAuthEntity = baseVideoLiveUrlReq.getThirdPartyPlatformsAuthEntity();

        ThirdPartyPlatformsVideoLiveUrlResp resp = new ThirdPartyPlatformsVideoLiveUrlResp();
        for (int i = 0; i < baseVideoLiveUrlReq.getProtocol().length; i++) {
            if (baseVideoLiveUrlReq.getProtocol()[i] == BaseVideoProtocolEnum.RTSP) {
                RtspUrlRequest rtspUrlRequest = new RtspUrlRequest();
                RtspUrlRequest.Data data = new RtspUrlRequest.Data();
                data.setChannelId(baseVideoLiveUrlReq.getChannelId());
                data.setDataType("1");
                data.setStreamType("1");
                rtspUrlRequest.setData(data);
                RtspUrlResponse response = daHuaIccBiz.getRtspUrl(thirdPartyPlatformsAuthEntity, rtspUrlRequest);
                if ("1000".equals(response.getCode())) {
                    //获取完整的rtsp流地址
                    String rtspUrl = response.getData().getUrl() + "?token=" + response.getData().getToken();
                    log.info("大华icc-完整的rtsp流地址：{}", rtspUrl);
                    resp.setUrl(rtspUrl);
                    resp.setProtocolType(BaseVideoProtocolEnum.RTSP.getResProtocol());
                    return DtoResult.ok(resp);
                }
                return DtoResult.error("大华rtsp直播流获取失败");

            }
            if (baseVideoLiveUrlReq.getProtocol()[i] == BaseVideoProtocolEnum.HLS
                    || baseVideoLiveUrlReq.getProtocol()[i] == BaseVideoProtocolEnum.HTTPSFLV
                    || baseVideoLiveUrlReq.getProtocol()[i] == BaseVideoProtocolEnum.RTMP
                    || baseVideoLiveUrlReq.getProtocol()[i] == BaseVideoProtocolEnum.WS_FLV) {
                String accessToken = redisTemplate.opsForValue().get(DaHuaRedisConstant.DA_HUA_TOKEN_KEY + thirdPartyPlatformsAuthEntity.getAppKey());
                if (StringUtil.isBlank(accessToken)) {
                    return DtoResult.error("大华token不存在");
                }
                HlsUrlRequest hlsUrlRequest = new HlsUrlRequest();
                HlsUrlRequest.Data data = new HlsUrlRequest.Data();
                data.setChannelId(baseVideoLiveUrlReq.getChannelId());
                data.setStreamType("1");
                if (baseVideoLiveUrlReq.getProtocol()[i] == BaseVideoProtocolEnum.HLS) {
                    data.setType(DaHuaVideoProtocolEnums.hls.getCode());
                    resp.setProtocolType(BaseVideoProtocolEnum.HLS.getResProtocol());
                } else if (baseVideoLiveUrlReq.getProtocol()[i] == BaseVideoProtocolEnum.HTTPSFLV) {
                    data.setType(DaHuaVideoProtocolEnums.flvs.getCode());
                    resp.setProtocolType(BaseVideoProtocolEnum.HTTPSFLV.getResProtocol());
                } else if (baseVideoLiveUrlReq.getProtocol()[i] == BaseVideoProtocolEnum.RTMP) {
                    data.setType(DaHuaVideoProtocolEnums.rtmp.getCode());
                    resp.setProtocolType(BaseVideoProtocolEnum.RTMP.getResProtocol());
                } else if (baseVideoLiveUrlReq.getProtocol()[i] == BaseVideoProtocolEnum.WS_FLV) {
                    data.setType(DaHuaVideoProtocolEnums.ws_flv.getCode());
                    resp.setProtocolType(BaseVideoProtocolEnum.WS_FLV.getResProtocol());
                }
                hlsUrlRequest.setData(data);
                HlsUrlResponse response = daHuaIccBiz.getOtherStreamUrl(thirdPartyPlatformsAuthEntity, hlsUrlRequest);
                if ("1000".equals(response.getCode())) {
                    //获取完整的流地址
                    String videoUrl;
                    if (baseVideoLiveUrlReq.getProtocol()[i] == BaseVideoProtocolEnum.RTMP) {
                        //rtmp后面跟分号
                        videoUrl = response.getData().getUrl() + ";token=" + accessToken;
                    } else {
                        videoUrl = response.getData().getUrl() + "?token=" + accessToken;
                    }
                    log.info("大华icc-完整的videoUrl流地址：{}", videoUrl);
                    resp.setUrl(videoUrl);
                    return DtoResult.ok(resp);
                }
                return DtoResult.error("大华HLS、FLV、RTMP实时预览接口方式直播流获取失败");
            }
        }
        return DtoResult.error("暂不支持该协议");
    }

    @Override
    public DtoResult<ThirdPartyPlatformsRtcConnectResp> rtcConnect(ThirdPartyPlatformsBaseRtcConnectReq req) {
        ThirdPartyPlatformsAuthEntity thirdPartyPlatformsAuthEntity = req.getThirdPartyPlatformsAuthEntity();
        ThirdPartyPlatformsRtcConnectResp rtcConnectResp = new ThirdPartyPlatformsRtcConnectResp();
        StartTalkRequest startTalkRequest = new StartTalkRequest();
        StartTalkRequest.Data data = new StartTalkRequest.Data();
        //req.getChannelId() = "100001$1$0$0"
        String channelId = req.getChannelId();
        //channelId第一个$之前的内容
        data.setDeviceCode(channelId.split("\\$")[0]);
        //对讲类型：1=设备, 2=通道, 3=国标设备广播  固定传2
        data.setTalkType("2");
        //对讲模式：0=对讲（默认模式）, 1=广播 固定0
        data.setTalkmode("0");
        //channelId最后一位字符
        data.setChannelSeq(channelId.substring(channelId.length() - 1));
        startTalkRequest.setData(data);
        StartTalkResponse startTalkResponse = daHuaIccBiz.rtcConnect(thirdPartyPlatformsAuthEntity, startTalkRequest);
        if ("1000".equals(startTalkResponse.getCode())) {
            StartTalkResponse.StartTalkData startTalkResponseData = startTalkResponse.getData();
            rtcConnectResp.setSessionId(startTalkResponseData.getSession());
            rtcConnectResp.setWsUrl(startTalkResponseData.getUrl() + "?token=" + startTalkResponseData.getToken());
            rtcConnectResp.setToken(startTalkResponseData.getToken());
            rtcConnectResp.setType(5);
        }
        return DtoResult.ok(rtcConnectResp);
    }

    @Override
    public DtoResult<Void> rtcUnConnect(ThirdPartyPlatformsBaseRtcUnConnectReq req) {
        ThirdPartyPlatformsAuthEntity thirdPartyPlatformsAuthEntity = req.getThirdPartyPlatformsAuthEntity();
        StopTalkRequest startTalkRequest = new StopTalkRequest();
        StopTalkRequest.Data data = new StopTalkRequest.Data();
        data.setSession(req.getSessionId());
        data.setDeviceCode(req.getChannelId().split("\\$")[0]);
        data.setTalkType("2");
        data.setChannelSeq(req.getChannelId().substring(req.getChannelId().length() - 1));
        startTalkRequest.setData(data);
        DaHuaCommonIccResponse iccResponse = daHuaIccBiz.rtcUnConnect(thirdPartyPlatformsAuthEntity, startTalkRequest);
        if ("1000".equals(iccResponse.getCode())) {
            log.info("大华icc-断开对讲成功");
            return DtoResult.ok();
        } else {
            log.info("大华icc-断开对讲失败");
            return DtoResult.error("大华icc-断开对讲失败" + iccResponse.getDesc());
        }
    }

    @Override
    public DtoResult<CommonGetRecordMonthResp> getRecordMonth(OpenGetRecordMonthReq req) {
        CommonGetRecordMonthResp resp = new CommonGetRecordMonthResp();
        ThirdPartyPlatformsAuthEntity thirdPartyPlatformsAuthEntity = req.getThirdPartyPlatformsAuthEntity();
        ChannelMonthRecordStatusRequest channelMonthRecordStatusRequest = new ChannelMonthRecordStatusRequest();
        ChannelMonthRecordStatusRequest.Data data = new ChannelMonthRecordStatusRequest.Data();
        data.setChannelId(req.getChannelCode());
        data.setMonth(req.getMonth());
        //云存（CLOUD）或本地存储（LOCAL）
        data.setRecordSource(BaseVideoBackProtocolEnum.CLOUD.getVlinkerConvergeProtocol().equals(req.getSource()) ? "3" : "2");
        channelMonthRecordStatusRequest.setData(data);
        ChannelMonthRecordStatusResponse monthRecordStatusResponse = daHuaIccBiz.getRecordMonthStatus(thirdPartyPlatformsAuthEntity, channelMonthRecordStatusRequest);
        if ("1000".equals(monthRecordStatusResponse.getCode())) {
            LinkedHashMap<String, Integer> dateList = new LinkedHashMap<>();
            //处理一下每天的数据
            String days = monthRecordStatusResponse.getData().getDays();
            //处理一下对应每天的值
            String[] daysArr = days.split(",");
            for (int i = 0; i < daysArr.length; i++) {
                String yearMonth = data.getMonth();
                String formattedDate = String.format("%s-%s-%02d", yearMonth.substring(0, 4), yearMonth.substring(4, 6), i + 1);
                dateList.put(formattedDate, Integer.parseInt(daysArr[i]));
            }
            resp.setDateList(dateList);
            return DtoResult.ok(resp);
        }
        return DtoResult.error("大华icc-获取月份录像录像存在状态失败");
    }

    /**
     * 获取录像下载地址和获取录像地址兼容融合平台共用入口
     * 手动拼接hls(s)录像地址方式
     * 下面那个api获取的出参有问题，大华建议手动拼接
     *
     * @param baseVideoBackUrlReq
     * @return com.saida.services.common.base.DtoResult<com.saida.services.open.biz.resp.ThirdPartyPlatformsVideoBackUrlResp>
     * <AUTHOR>
     * @since 2025/03/20 10:29:26
     */
    @Override
    public DtoResult<ThirdPartyPlatformsVideoBackUrlResp> getVideoBackUrl(ThirdPartyPlatformsBaseVideoBackUrlReq baseVideoBackUrlReq) {
        // 由于融合平台接口下载和获取录像api共用，所以需要根据参数单独判断一下
        // 不为空表示查询本地录像
        if (ObjectUtil.isNotNull(baseVideoBackUrlReq.getIsHttps())) {
            ThirdPartyPlatformsAuthEntity platformsAuthEntity = baseVideoBackUrlReq.getThirdPartyPlatformsAuthEntity();
            String accessToken = redisTemplate.opsForValue().get(DaHuaRedisConstant.DA_HUA_TOKEN_KEY + platformsAuthEntity.getAppKey());
            if (StringUtil.isBlank(accessToken)) {
                return DtoResult.error("大华token不存在");
            }
            ThirdPartyPlatformsVideoBackUrlResp resp = new ThirdPartyPlatformsVideoBackUrlResp();
            StringBuilder hlsUrlManual = new StringBuilder();
            String channelId = baseVideoBackUrlReq.getChannelId();
            String ip = platformsAuthEntity.getHost().replace("https://", "").replace("http://", "").split(":")[0];
            String port;
            String protocol;
            String recordType = "0";
            if (baseVideoBackUrlReq.getIsHttps()) {
                protocol = "https";
                port = "7096";
            } else {
                protocol = "http";
                port = "7086";
            }
            hlsUrlManual.append(protocol + "://" + ip + ":" + port);
            if (BaseVideoBackProtocolEnum.LOCAL.getVlinkerConvergeProtocol().equals(baseVideoBackUrlReq.getBaseVideoBackProtocolEnum().getVlinkerConvergeProtocol())) {
                //录像为设备录像
                hlsUrlManual.append("/vod/device");
            } else {
                //录像为中心录像
                hlsUrlManual.append("/vod/center");
            }
            String deviceCode = channelId.substring(0, channelId.indexOf("$"));
            String channelSeq = channelId.substring(channelId.lastIndexOf("$") + 1);
            hlsUrlManual.append("/cameraid/" + deviceCode + "%24" + channelSeq);
            //默认主码流1
            hlsUrlManual.append("/substream/1");
            hlsUrlManual.append("/recordtype/" + recordType);
            long totallength = (baseVideoBackUrlReq.getEnd() - baseVideoBackUrlReq.getStart()) / 1000;
            hlsUrlManual.append("/totallength/" + totallength + "/begintime/" + baseVideoBackUrlReq.getStart() / 1000 + "/endtime/" + baseVideoBackUrlReq.getEnd() / 1000 + ".m3u8");
            log.info("拼接的录像回放hls流地址:{}", hlsUrlManual);
            resp.setUrl(hlsUrlManual + "?token=" + accessToken);
            return DtoResult.ok(resp);
        } else {
            //否则表示下载录像
            if (baseVideoBackUrlReq.getBaseVideoBackProtocolEnum().equals(BaseVideoBackProtocolEnum.CLOUD)) {
                return this.downloadCloudPlaybackUrl(baseVideoBackUrlReq);
            } else {
                return this.downloadDevicePlaybackUrl(baseVideoBackUrlReq);
            }
        }
    }

//    @Override
//    public DtoResult<ThirdPartyPlatformsVideoBackUrlResp> getVideoBackUrl(ThirdPartyPlatformsBaseVideoBackUrlReq baseVideoBackUrlReq) {
//        ThirdPartyPlatformsAuthEntity platformsAuthEntity = baseVideoBackUrlReq.getThirdPartyPlatformsAuthEntity();
//        String accessToken = redisTemplate.opsForValue().get(DaHuaRedisConstant.DA_HUA_TOKEN_KEY + platformsAuthEntity.getAppKey());
//        if (StringUtil.isBlank(accessToken)) {
//            return DtoResult.error("大华token不存在");
//        }
//        ThirdPartyPlatformsVideoBackUrlResp resp = new ThirdPartyPlatformsVideoBackUrlResp();
//        //查询普通录像信息
//        HlsPlaybackRequest regularVideoRecordRequest = new HlsPlaybackRequest();
//        HlsPlaybackRequest.Data data = new HlsPlaybackRequest.Data();
//        data.setChannelId(baseVideoBackUrlReq.getChannelId());
//        data.setRecordSource(BaseVideoBackProtocolEnum.CLOUD.getVlinkerConvergeProtocol().equals(baseVideoBackUrlReq.getBaseVideoBackProtocolEnum().getVlinkerConvergeProtocol()) ? "3" : "2");
//        //baseVideoBackUrlReq.getstart()返回的是毫秒的时间戳 开始结束时间时间戳转yyyy-MM-dd HH:mm:ss
//        data.setBeginTime(DateUtil.convertTimestampToDateTime(baseVideoBackUrlReq.getStart()));
//        data.setEndTime(DateUtil.convertTimestampToDateTime(baseVideoBackUrlReq.getEnd()));
//        data.setStreamType("1");
//        data.setRecordType("1");
//        data.setType(baseVideoBackUrlReq.getIsHttps() ? "hlss" : "hls");
//        regularVideoRecordRequest.setData(data);
//        HlsUrlResponse urlResponse = daHuaIccBiz.getVideoBackUrl(platformsAuthEntity, regularVideoRecordRequest);
//        if ("1000".equals(urlResponse.getCode())) {
//            String url = urlResponse.getData().getUrl();
//            //lj大华不处理，只能自己判断手动修复
//            //判断url里ip是外网还是内网，如果不是外网ip则需要手动更换外网ip
//            String ip = platformsAuthEntity.getHost().replace("https://", "").replace("http://", "").split(":")[0];
//            if (!url.contains(ip)) {
//                url = url.replace(url.replace("https://", "").replace("http://", "").split(":")[0], ip);
//            }
//            resp.setUrl(url + "?token=" + accessToken);
//            return DtoResult.ok(resp);
//        }
//        return DtoResult.error("大华icc-获取回放地址失败");
//    }

    @Override
    public DtoResult<ThirdPartyPlatformsVideoBackUrlResp> downloadCloudPlaybackUrl(ThirdPartyPlatformsBaseVideoBackUrlReq baseVideoBackUrlReq) {
        ThirdPartyPlatformsAuthEntity platformsAuthEntity = baseVideoBackUrlReq.getThirdPartyPlatformsAuthEntity();
        String accessToken = redisTemplate.opsForValue().get(DaHuaRedisConstant.DA_HUA_TOKEN_KEY + platformsAuthEntity.getAppKey());
        if (StringUtil.isBlank(accessToken)) {
            return DtoResult.error("大华token不存在");
        }
        ThirdPartyPlatformsVideoBackUrlResp resp = new ThirdPartyPlatformsVideoBackUrlResp();
        VideoDownloadRequest videoDownloadRequest = new VideoDownloadRequest();
        videoDownloadRequest.setChannelId(baseVideoBackUrlReq.getChannelId());
        videoDownloadRequest.setSubtype("1");
        videoDownloadRequest.setStarttime(DateUtil.convertTimestampToDateTimeUnderline(baseVideoBackUrlReq.getStart()));
        videoDownloadRequest.setEndtime(DateUtil.convertTimestampToDateTimeUnderline(baseVideoBackUrlReq.getEnd()));
        videoDownloadRequest.setToken(accessToken);
        //云端录像固定传值
        videoDownloadRequest.setVideoType("3");
        videoDownloadRequest.setVideoRecordType("1");
        String downloadUrl = daHuaIccBiz.downloadCloudPlaybackUrl(platformsAuthEntity, videoDownloadRequest);
        if (StringUtil.isNotBlank(downloadUrl)) {
            resp.setDownloadUrl(downloadUrl);
            return DtoResult.ok(resp);
        }
        return DtoResult.error("大华icc-获取云端录像下载地址失败");
    }

    @Override
    public DtoResult<ThirdPartyPlatformsVideoBackUrlResp> downloadDevicePlaybackUrl(ThirdPartyPlatformsBaseVideoBackUrlReq baseVideoBackUrlReq) {
        ThirdPartyPlatformsAuthEntity platformsAuthEntity = baseVideoBackUrlReq.getThirdPartyPlatformsAuthEntity();
        String accessToken = redisTemplate.opsForValue().get(DaHuaRedisConstant.DA_HUA_TOKEN_KEY + platformsAuthEntity.getAppKey());
        if (StringUtil.isBlank(accessToken)) {
            return DtoResult.error("大华token不存在");
        }
        ThirdPartyPlatformsVideoBackUrlResp resp = new ThirdPartyPlatformsVideoBackUrlResp();
        VideoDownloadRequest videoDownloadRequest = new VideoDownloadRequest();
        videoDownloadRequest.setChannelId(baseVideoBackUrlReq.getChannelId());
        videoDownloadRequest.setSubtype("1");
        videoDownloadRequest.setStarttime(DateUtil.convertTimestampToDateTimeUnderline(baseVideoBackUrlReq.getStart()));
        videoDownloadRequest.setEndtime(DateUtil.convertTimestampToDateTimeUnderline(baseVideoBackUrlReq.getEnd()));
        videoDownloadRequest.setToken(accessToken);
        //卡存录像固定传值
        videoDownloadRequest.setVideoType("2");
        videoDownloadRequest.setVideoRecordType("0");
        String downloadUrl = daHuaIccBiz.downloadCloudPlaybackUrl(platformsAuthEntity, videoDownloadRequest);
        if (StringUtil.isNotBlank(downloadUrl)) {
            resp.setDownloadUrl(downloadUrl);
            return DtoResult.ok(resp);
        }
        return DtoResult.error("大华icc-获取卡存录像下载地址失败");
    }

    @Override
    public DtoResult<List<ThirdPartyPlatformsRecordTimeLineResp>> getRecordTimeLine(ThirdPartyPlatformsBaseRecordTimeLineReq baseRecordTimeLineReq) {
        ThirdPartyPlatformsAuthEntity platformsAuthEntity = baseRecordTimeLineReq.getThirdPartyPlatformsAuthEntity();
        RegularVideoRecordRequest regularVideoRecordRequest = new RegularVideoRecordRequest();
        RegularVideoRecordRequest.Data data = new RegularVideoRecordRequest.Data();
        data.setChannelId(baseRecordTimeLineReq.getChannelId());
        data.setRecordSource(BaseVideoBackProtocolEnum.CLOUD.getVlinkerConvergeProtocol().equals(baseRecordTimeLineReq.getBaseVideoBackProtocolEnum().getVlinkerConvergeProtocol()) ? "3" : "2");
        data.setStartTime(String.valueOf(baseRecordTimeLineReq.getStart() / 1000));
        data.setEndTime(String.valueOf(baseRecordTimeLineReq.getEnd() / 1000));
        data.setStreamType("1");
        data.setRecordType("0");
        regularVideoRecordRequest.setData(data);
        RegularVideoRecordResponse regularVideoRecordResponse = daHuaIccBiz.getRecordTimeLineStatus(platformsAuthEntity, regularVideoRecordRequest);
        if ("1000".equals(regularVideoRecordResponse.getCode())) {
            List<ThirdPartyPlatformsRecordTimeLineResp> list = new ArrayList<>();
            List<RegularVideoRecordResponse.Data.RecordInfo> recordList = regularVideoRecordResponse.getData().getRecords();
            if (CollectionUtils.isEmpty(recordList)) {
                return DtoResult.ok(list);
            }
            recordList.forEach(recordInfo -> {
                ThirdPartyPlatformsRecordTimeLineResp resp = new ThirdPartyPlatformsRecordTimeLineResp();
                resp.setStart(Long.parseLong(recordInfo.getStartTime()) * 1000);
                resp.setDuration(Long.parseLong(recordInfo.getEndTime()) * 1000 - resp.getStart());
                resp.setId(recordInfo.getPlanId());
                resp.setAccessWay(61);
                list.add(resp);
            });
            return DtoResult.ok(list);
        }
        return DtoResult.error("暂不支持该功能！");
    }

    @Override
    public DtoResult<Void> ptzControl(ThirdPartyPlatformsBasePtzControlReq basePtzControlReq) {
        ThirdPartyPlatformsAuthEntity platformsAuthEntity = basePtzControlReq.getThirdPartyPlatformsAuthEntity();
        if (basePtzControlReq.getCommand() == BasePtzControlCmdEnum.UP
                || basePtzControlReq.getCommand() == BasePtzControlCmdEnum.DOWN
                || basePtzControlReq.getCommand() == BasePtzControlCmdEnum.LEFT
                || basePtzControlReq.getCommand() == BasePtzControlCmdEnum.RIGHT
                || basePtzControlReq.getCommand() == BasePtzControlCmdEnum.LEFT_UP
                || basePtzControlReq.getCommand() == BasePtzControlCmdEnum.LEFT_DOWN
                || basePtzControlReq.getCommand() == BasePtzControlCmdEnum.RIGHT_UP
                || basePtzControlReq.getCommand() == BasePtzControlCmdEnum.RIGHT_DOWN) {
            //云台方向控制
            OperateDirectRequest operateDirectRequest = new OperateDirectRequest();
            OperateDirectRequest.Data data = new OperateDirectRequest.Data();
            data.setChannelId(basePtzControlReq.getChannelId());
            data.setDirect(basePtzControlReq.getCommand().getDaHuaPtzCommand());
            //速度 1-100 换算成  1-8
            int speed = (int) Math.round(((double) (basePtzControlReq.getSpeed() - 1) / (100 - 1)) * (8 - 1) + 1);
            data.setStepX(String.valueOf(speed));
            data.setStepY(String.valueOf(speed));
            data.setCommand(basePtzControlReq.getAction().getCode());
            data.setExtend("");
            operateDirectRequest.setData(data);
            OperateDirectResponse operateDirectResponse = daHuaIccBiz.ptzOperateDirect(platformsAuthEntity, operateDirectRequest);
            if ("1000".equals(operateDirectResponse.getCode())) {
                String result = operateDirectResponse.getData().getResult();
                if ("1".equals(result)) {
                    return DtoResult.ok();
                } else {
                    return DtoResult.error("大华icc-云台方向控制失败,用户【" + operateDirectResponse.getData().getLockUser().getUserName() + "】正在控制云台，请稍后重试");
                }
            }
            return DtoResult.error("大华icc-云端云台方向控制失败");
        } else if (basePtzControlReq.getCommand() == BasePtzControlCmdEnum.ZOOM_IN
                || basePtzControlReq.getCommand() == BasePtzControlCmdEnum.ZOOM_OUT
                || basePtzControlReq.getCommand() == BasePtzControlCmdEnum.FOCUS_NEAR
                || basePtzControlReq.getCommand() == BasePtzControlCmdEnum.FOCUS_FAR) {
            //云台镜头控制
            OperateCameraRequest operateCameraRequest = new OperateCameraRequest();
            OperateCameraRequest.Data data = new OperateCameraRequest.Data();
            data.setChannelId(basePtzControlReq.getChannelId());
            data.setOperateType(basePtzControlReq.getCommand().getDaHuaPtzCommand());
            data.setDirect(basePtzControlReq.getCommand().getDaHuaPtzDirect());
            //速度 1-100 换算成  1-8
            int speed = (int) Math.round(((double) (basePtzControlReq.getSpeed() - 1) / (100 - 1)) * (8 - 1) + 1);
            data.setStep(String.valueOf(speed));
            data.setCommand(basePtzControlReq.getAction().getCode());
            data.setExtend("");
            operateCameraRequest.setData(data);
            OperateCameraResponse operateCameraResponse = daHuaIccBiz.ptzOperateCamera(platformsAuthEntity, operateCameraRequest);
            if ("1000".equals(operateCameraResponse.getCode())) {
                String result = operateCameraResponse.getData().getResult();
                if ("1".equals(result)) {
                    return DtoResult.ok();
                } else {
                    return DtoResult.error("大华icc-云台镜头控制失败,用户【" + operateCameraResponse.getData().getLockUser().getUserName() + "】正在控制云台，请稍后重试");
                }
            }
            return DtoResult.error("大华icc-云端云台镜头控制失败");
        }
        return DtoResult.error("暂不支持该功能！");

    }


    @Override
    public DtoResult<List<ThirdPartyPlatformsPrePointListResp>> getPrePointList(ThirdPartyPlatformsBasePrePointListReq basePrePointListReq) {
        ThirdPartyPlatformsAuthEntity platformsAuthEntity = basePrePointListReq.getThirdPartyPlatformsAuthEntity();
        GetPresetPointsRequest getPresetPointsRequest = new GetPresetPointsRequest();
        GetPresetPointsRequest.Data data = new GetPresetPointsRequest.Data();
        data.setChannelId(basePrePointListReq.getChannelId());
        getPresetPointsRequest.setData(data);
        GetPresetPointsResponse getPresetPointsResponse = daHuaIccBiz.getPresetPoints(platformsAuthEntity, getPresetPointsRequest);
        if ("1000".equals(getPresetPointsResponse.getCode())) {
            List<ThirdPartyPlatformsPrePointListResp> list = new ArrayList<>();
            getPresetPointsResponse.getData().getPresetPoints().forEach(item -> {
                ThirdPartyPlatformsPrePointListResp resp = new ThirdPartyPlatformsPrePointListResp();
                resp.setName(item.getPresetPointName());
                resp.setIndex(Integer.parseInt(item.getPresetPointCode()));
                resp.setEnabled(true);
                list.add(resp);
            });
            return DtoResult.ok(list);
        }
        return DtoResult.error("大华icc-获取云台预置点位失败");
    }

    @Override
    public DtoResult<Void> setPrePoint(ThirdPartyPlatformsBaseSetPrePointReq baseSetPrePointReq) {
        if (baseSetPrePointReq.getIndex() <= 0 || baseSetPrePointReq.getIndex() > 256) {
            return DtoResult.error("预置点位索引不合法，请设置为1~256区间！");
        }
        ThirdPartyPlatformsAuthEntity platformsAuthEntity = baseSetPrePointReq.getThirdPartyPlatformsAuthEntity();
        OperatePresetPointRequest operatePresetPointRequest = new OperatePresetPointRequest();
        OperatePresetPointRequest.Data data = new OperatePresetPointRequest.Data();
        data.setChannelId(baseSetPrePointReq.getChannelId());
        data.setPresetPointCode(String.valueOf(baseSetPrePointReq.getIndex()));
        data.setPresetPointName(baseSetPrePointReq.getName());
        data.setOperateType("2");
        operatePresetPointRequest.setData(data);
        OperatePresetPointResponse response = daHuaIccBiz.ptzOperatePresetPoint(platformsAuthEntity, operatePresetPointRequest);
        if ("1000".equals(response.getCode())) {
            String result = response.getData().getResult();
            if ("1".equals(result)) {
                return DtoResult.ok();
            } else {
                return DtoResult.error("大华icc-云台预置点设置失败,用户【" + response.getData().getLockUser().getUserName() + "】正在处理预置点位，请稍后重试");
            }
        }
        return DtoResult.error("大华icc-云台预置点设置失败！");
    }

    @Override
    public DtoResult<Void> jumpPrePoint(ThirdPartyPlatformsBaseJumpPrePointReq baseJumpPrePointReq) {
        if (baseJumpPrePointReq.getIndex() <= 0 || baseJumpPrePointReq.getIndex() > 256) {
            return DtoResult.error("预置点位索引不合法，请设置为1~256区间！");
        }
        ThirdPartyPlatformsAuthEntity platformsAuthEntity = baseJumpPrePointReq.getThirdPartyPlatformsAuthEntity();
        OperatePresetPointRequest operatePresetPointRequest = new OperatePresetPointRequest();
        OperatePresetPointRequest.Data data = new OperatePresetPointRequest.Data();
        data.setChannelId(baseJumpPrePointReq.getChannelId());
        data.setPresetPointCode(String.valueOf(baseJumpPrePointReq.getIndex()));
        data.setPresetPointName(baseJumpPrePointReq.getName());
        data.setOperateType("1");
        operatePresetPointRequest.setData(data);
        OperatePresetPointResponse response = daHuaIccBiz.ptzOperatePresetPoint(platformsAuthEntity, operatePresetPointRequest);
        if ("1000".equals(response.getCode())) {
            String result = response.getData().getResult();
            if ("1".equals(result)) {
                return DtoResult.ok();
            } else {
                return DtoResult.error("大华icc-云台预置点跳转失败,用户【" + response.getData().getLockUser().getUserName() + "】正在处理预置点位，请稍后重试");
            }
        }
        return DtoResult.error("大华icc-云台预置点跳转失败！");
    }

    /**
     * 如果deviceId没传则默认订阅所有设备
     *
     * @param req
     * @return com.saida.services.common.base.DtoResult<java.lang.Void>
     */
    @Override
    public DtoResult<Void> subscribeAlgorithm(ThirdPartyPlatformsBaseSubscribeReq req) {
        ThirdPartyPlatformsAuthEntity platformsAuthEntity = req.getThirdPartyPlatformsAuthEntity();
        Set<String> subscribe = req.getSubscribe();
        if (CollectionUtil.isNotEmpty(subscribe)) {
            //映射大华算法类型
            List<AlgorithmMappingEntity> dahuaAlgorithmMappingEntityList = algorithmMappingService.list(new LambdaQueryWrapper<AlgorithmMappingEntity>()
                    .eq(AlgorithmMappingEntity::getSourceId, OpenAlgorithmSourceEnum.DA_HUA_ICC.getCode())
            );
            if (CollectionUtil.isEmpty(dahuaAlgorithmMappingEntityList)) {
                log.error("大华算法映射列表为空");
                return DtoResult.error("大华算法映射列表为空");
            }
            Map<Long, List<AlgorithmMappingEntity>> algorithmMap = dahuaAlgorithmMappingEntityList.stream().collect(Collectors.groupingBy(AlgorithmMappingEntity::getAlgorithmId));
            //告警订阅处理告警类型
            for (String algorithmId : subscribe) {
                algorithmMap.get(Long.parseLong(algorithmId)).forEach(algorithmMappingEntity -> {
                    if (StringUtils.isNotBlank(algorithmMappingEntity.getCode())) {
                        //algorithmId后5位
                        String code = algorithmId.substring(algorithmId.length() - 5);
                        String[] split = algorithmMappingEntity.getCode().split(",");
                        HashSet<String> warnTypeSet = new HashSet<>(Arrays.asList(split));
                        //订阅入参封装
                        SubscribeRequest subscribeRequest = new SubscribeRequest();
                        SubscribeRequest.Param param = new SubscribeRequest.Param();
                        SubscribeRequest.Param.Subsystem subsystem = new SubscribeRequest.Param.Subsystem();
                        //订阅者ip_端口 + 告警大类 + 可能存在的设备id（若不存在表示所有） name相同会覆盖
                        //兼容单业务平台配置多个大华平台
                        subsystem.setName(SUBSCRIBE_NAME + req.getCategory() + code + (StringUtils.isNotBlank(req.getChannelId()) ? req.getChannelId() : ""));
                        subsystem.setMagic(SUBSCRIBE_NAME);
                        param.setSubsystem(subsystem);

                        List<SubscribeRequest.Param.Monitor> monitors = new ArrayList();
                        SubscribeRequest.Param.Monitor monitor = new SubscribeRequest.Param.Monitor();
                        //回调地址的接口代码可参考SubscribeCallBackController类中的receiveMsg方法
                        monitor.setMonitor(platformsAuthEntity.getAlarmPushUrl());
                        List<SubscribeRequest.Param.Monitor.Event> events = new ArrayList<>();
                        SubscribeRequest.Param.Monitor.Event event = new SubscribeRequest.Param.Monitor.Event();
                        //订阅报警事件，填alarm
                        //事件类型单独处理，默认传参是alarm
                        //判断warnTypeSet中的数据是否含有字母，且不包含'pmms.perception.msg'和'reportGPSInfo'
                        if (warnTypeSet.stream().anyMatch(s -> s.matches(".*[a-zA-Z]+.*"))) {
                            if(warnTypeSet.contains("pmms.perception.msg") || warnTypeSet.contains("reportGPSInfo")){
                                event.setCategory(EventCategoryEnums.perception.getCode());
                            }else{
                                event.setCategory(EventCategoryEnums.business.getCode());
                            }
                        }else{
                            event.setCategory(EventCategoryEnums.alarm.getCode());
                        }
                        //订阅指定报警类型,
                        if (!req.getCategory().equals(EventCategoryEnums.state.getCode())) {
                            event.setEventType(0);
                            List<SubscribeRequest.Param.Monitor.Event.Authority> authorities = new ArrayList();
                            SubscribeRequest.Param.Monitor.Event.Authority authority = new SubscribeRequest.Param.Monitor.Event.Authority();
                            //若订阅指定设备和通道可参考下列代码进行修改,若订阅所有设备和通道，则将下列nodeCodes相关代码删除
                            // TODO 后续有需求再改造
                            //如果deviceId不为空说明是单个设备订阅
                            if(!StringUtils.isBlank(req.getChannelId())){
                                List<String> nodeCodes = new ArrayList<>();
                                //这里要传三方平台设备唯一编码，能开device表的tripartite_sn,暂未处理
                                nodeCodes.add(req.getChannelId());
                                authority.setNodeCodes(nodeCodes);
                            }
                            authority.setTypes(new ArrayList<>(warnTypeSet));
                            authorities.add(authority);
                            event.setAuthorities(authorities);
                        }
                        events.add(event);
                        monitor.setEvents(events);
                        monitors.add(monitor);
                        param.setMonitors(monitors);
                        subscribeRequest.setParam(param);
                        GeneralResponse response = daHuaIccBiz.subscribeAlgorithm(platformsAuthEntity, subscribeRequest);
                    }
                });
            }
            return DtoResult.ok();
        }else {
            return DtoResult.error("未选择算法！");
        }
    }

    @Override
    public DtoResult<Void> unsubscribeAlgorithm(ThirdPartyPlatformsBaseSubscribeReq thirdPartyPlatformsBaseSubscribeReq) {
        ThirdPartyPlatformsAuthEntity platformsAuthEntity = thirdPartyPlatformsBaseSubscribeReq.getThirdPartyPlatformsAuthEntity();
        String accessToken = redisTemplate.opsForValue().get(DaHuaRedisConstant.DA_HUA_TOKEN_KEY + platformsAuthEntity.getAppKey());
        if (StringUtil.isBlank(accessToken)) {
            return DtoResult.error("icc取消订阅，token不存在");
        }
        Set<String> unSubscribe = thirdPartyPlatformsBaseSubscribeReq.getSubscribe();
        if(!CollectionUtil.isEmpty(unSubscribe)){

            for (String algorithmId : unSubscribe) {
                try {
                    //algorithmId后5位
                    String code = algorithmId.substring(algorithmId.length() - 5);
                    String subscribeName = SUBSCRIBE_NAME + thirdPartyPlatformsBaseSubscribeReq.getCategory()  + code + (StringUtils.isNotBlank(thirdPartyPlatformsBaseSubscribeReq.getChannelId()) ? thirdPartyPlatformsBaseSubscribeReq.getChannelId() : "");
                    log.info("大华icc取消订阅请求:{}", platformsAuthEntity.getHost() + DaHuaIccApiEnum.EVENT_UNSUBSCRIBE.getPath() + subscribeName);
                    HttpResponse response = HttpRequest.delete(platformsAuthEntity.getHost() + DaHuaIccApiEnum.EVENT_UNSUBSCRIBE.getPath() + subscribeName)
                            .header("Authorization", "bearer " + accessToken)
                            .execute();
                    if (response.getStatus() != 200) {
                        log.info("大华icc取消订阅失败,响应状态码:{}", response.getStatus());
                        return DtoResult.error("大华icc取消订阅失败");
                    }
                    // 获取响应内容
                    String body = response.body();
                    log.info("大华icc取消订阅响应:{}", body);
                    //{"success":true,"data":{},"code":"0","errMsg":""}
//                    JSONObject jsonObject = JSONObject.parseObject(body);
//                    if (jsonObject.getBoolean("success")) {
//                        return DtoResult.ok();
//                    } else {
//                        return DtoResult.error("大华icc取消订阅失败:" + jsonObject.getString("errMsg"));
//                    }
                } catch (Exception e) {
                    log.error("大华icc取消订阅异常", e);
                    return DtoResult.error("大华icc取消订阅异常");
                }
            }
        }
        return DtoResult.ok();

    }

    @Override
    public DtoResult<Void> deletePrePoint(ThirdPartyPlatformsBaseDeletePrePointReq baseDeletePrePointReq) {
        if (baseDeletePrePointReq.getIndex() <= 0 || baseDeletePrePointReq.getIndex() > 256) {
            return DtoResult.error("预置点位索引不合法，请设置为1~256区间！");
        }
        ThirdPartyPlatformsAuthEntity platformsAuthEntity = baseDeletePrePointReq.getThirdPartyPlatformsAuthEntity();
        OperatePresetPointRequest operatePresetPointRequest = new OperatePresetPointRequest();
        OperatePresetPointRequest.Data data = new OperatePresetPointRequest.Data();
        data.setChannelId(baseDeletePrePointReq.getChannelId());
        data.setPresetPointCode(String.valueOf(baseDeletePrePointReq.getIndex()));
        data.setOperateType("3");
        operatePresetPointRequest.setData(data);
        OperatePresetPointResponse response = daHuaIccBiz.ptzOperatePresetPoint(platformsAuthEntity, operatePresetPointRequest);
        if ("1000".equals(response.getCode())) {
            String result = response.getData().getResult();
            if ("1".equals(result)) {
                return DtoResult.ok();
            } else {
                return DtoResult.error("大华icc-云台预置点设置失败,用户【" + response.getData().getLockUser().getUserName() + "】正在处理预置点位，请稍后重试");
            }
        }
        return DtoResult.error("大华icc-云台预置点设置失败！");
    }

    public void syncTree(String parentId, String idChain, ThirdPartyPlatformsAuthEntity thirdPartyPlatformsAuthEntity) {
        LambdaQueryWrapper<ThirdPartyPlatformsTreeEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ThirdPartyPlatformsTreeEntity::getPlatformId, thirdPartyPlatformsAuthEntity.getId());
        ThirdPartyPlatformsTreeEntity thirdPartyPlatformsTreeEntity = thirdPartyPlatformsTreeService.getOne(lambdaQueryWrapper, false);
        if (thirdPartyPlatformsTreeEntity == null) {
            log.info("V-LINKER能力开放平台.大华ICC同步设备..区域树为空...程序结束");
            return;
        }
        this.syncDevice(thirdPartyPlatformsTreeEntity, thirdPartyPlatformsAuthEntity);
    }

    public void syncDevice(ThirdPartyPlatformsTreeEntity thirdPartyPlatformsTreeEntity, ThirdPartyPlatformsAuthEntity thirdPartyPlatformsAuthEntity) {
        DevicePageResponse dhDevicePageResponse;
        int pageNum = 1;

        List<OpenThirdPartyPlatformsDeviceEntity> saveOpenThirdPartyPlatformsDeviceEntityList = new ArrayList<>();

        BaseRequest req = new BaseRequest();
        req.setPageNum(pageNum);
        req.setPageSize(100);
        dhDevicePageResponse = daHuaIccBiz.getDeviceList(thirdPartyPlatformsAuthEntity, req);
        log.info("V-LINKER能力开放平台.大华icc同步设备...req={}, resp={}", JSON.toJSON(thirdPartyPlatformsAuthEntity), JSON.toJSON(dhDevicePageResponse));
        if (dhDevicePageResponse.isSuccess()) {
            List<DevicePageResponse.PageVO.DeviceInfoVO> daHuaDeviceRespList = dhDevicePageResponse.getData().getPageData();
            if (CollectionUtil.isEmpty(daHuaDeviceRespList)) {
                return;
            }
            daHuaDeviceRespList.forEach(deviceInfo -> {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("listBean", deviceInfo);
                //视频设备多通道处理unitType=1,表示视频通道
                deviceInfo.getUnits()
//                        .stream()
//                        .filter(unitVO -> unitVO.getUnitType() == 1)
                        .forEach(unit -> {
                    unit.getChannels().forEach(deviceChannelInfo -> {
                        OpenThirdPartyPlatformsDeviceEntity saveOpenThirdPartyPlatformsDeviceEntity = new OpenThirdPartyPlatformsDeviceEntity();
                        saveOpenThirdPartyPlatformsDeviceEntity.setPlatformId(thirdPartyPlatformsAuthEntity.getId());
                        saveOpenThirdPartyPlatformsDeviceEntity.setName(deviceInfo.getDeviceName());
                        saveOpenThirdPartyPlatformsDeviceEntity.setSnCode(deviceInfo.getDeviceCode());
                        saveOpenThirdPartyPlatformsDeviceEntity.setTreeId(thirdPartyPlatformsTreeEntity.getTreeId());
                        saveOpenThirdPartyPlatformsDeviceEntity.setTreeIdChain(thirdPartyPlatformsTreeEntity.getTreeIdChain());
                        saveOpenThirdPartyPlatformsDeviceEntity.setIsOnline(deviceChannelInfo.getIsOnline());
                        saveOpenThirdPartyPlatformsDeviceEntity.setDeviceModel(deviceInfo.getDeviceModel());
                        saveOpenThirdPartyPlatformsDeviceEntity.setChannelId(deviceChannelInfo.getChannelCode());
                        saveOpenThirdPartyPlatformsDeviceEntity.setChannelName(deviceChannelInfo.getChannelName());
                        String verCode = thirdPartyPlatformsDeviceService.generateVerCode(MD5.create(), saveOpenThirdPartyPlatformsDeviceEntity.getSnCode(), deviceChannelInfo.getChannelCode());
                        saveOpenThirdPartyPlatformsDeviceEntity.setVerCode(verCode);
                        saveOpenThirdPartyPlatformsDeviceEntity.setJsonStr(jsonObject.toJSONString());
                        saveOpenThirdPartyPlatformsDeviceEntity.setCreateTime(LocalDateTime.now());
                        saveOpenThirdPartyPlatformsDeviceEntity.setUpdateTime(LocalDateTime.now());
                        saveOpenThirdPartyPlatformsDeviceEntity.setCreateBy(1705477296068825090L);
                        saveOpenThirdPartyPlatformsDeviceEntity.setUpdateBy(1705477296068825090L);
                        //设备能力，如果是球机则具备（云台控制，变焦，变倍，预置点，语音对讲）
                        if ("2".equals(deviceChannelInfo.getCameraType())) {
                            Map<String, DeviceCapacityDto> deviceCapacity = new HashMap<>();
                            Arrays.stream(DeviceCapacityEnum.values()).forEach(e -> {
                                if (e == DeviceCapacityEnum.PAN_TILT_CONTROL || e == DeviceCapacityEnum.INTERCOM || e == DeviceCapacityEnum.PRESET
                                        || e == DeviceCapacityEnum.TRAJECTORY_CRUISE || e == DeviceCapacityEnum.ZOOM || e == DeviceCapacityEnum.FOCUS) {
                                    DeviceCapacityDto byField = new DeviceCapacityDto(e, 1);
                                    deviceCapacity.put(e.getField(), byField);
                                } else {
                                    //大华设备能力如果获取不到那么就全部放开
                                    DeviceCapacityDto byField = new DeviceCapacityDto(e, 1);
                                    deviceCapacity.put(e.getField(), byField);
                                }
                                saveOpenThirdPartyPlatformsDeviceEntity.setDeviceCapacity(deviceCapacity);
                            });
                        }
                        saveOpenThirdPartyPlatformsDeviceEntityList.add(saveOpenThirdPartyPlatformsDeviceEntity);
                    });
                });
            });
        }

        log.info("V-LINKER能力开放平台.大华icc同步设备..批量新增设备信息...size={}, deviceCode={}", saveOpenThirdPartyPlatformsDeviceEntityList.size(), JSON.toJSON(saveOpenThirdPartyPlatformsDeviceEntityList.stream().map(OpenThirdPartyPlatformsDeviceEntity::getSnCode).collect(Collectors.toList())));
        if (CollectionUtil.isNotEmpty(saveOpenThirdPartyPlatformsDeviceEntityList)) {
            // 任务数量
            int numberOfTasks = 10;
            // 创建计数锁存器
            CountDownLatch latch = new CountDownLatch(numberOfTasks);

            List<List<OpenThirdPartyPlatformsDeviceEntity>> subList = CustomerListUtil.subListByPart(saveOpenThirdPartyPlatformsDeviceEntityList, numberOfTasks);
            for (List<OpenThirdPartyPlatformsDeviceEntity> openThirdPartyPlatformsDeviceEntityListTemp : subList) {
                threadPoolTaskExecutor.execute(() -> {
                    try {
                        log.info("V-LINKER能力开放平台.大华icc同步设备..批量新增设备信息...deviceList={}", JSONObject.toJSON(openThirdPartyPlatformsDeviceEntityListTemp.stream().map(t1 -> {
                            JSONObject jsonObject = new JSONObject();
                            jsonObject.put("deviceId", t1.getId());
                            jsonObject.put("deviceName", t1.getName());
                            jsonObject.put("deviceCode", t1.getSnCode());
                            return jsonObject;
                        }).collect(Collectors.toList())));
                        // 任务执行，保存批次数据
                        thirdPartyPlatformsDeviceService.saveBatch(openThirdPartyPlatformsDeviceEntityListTemp);
                    } finally {
                        // 任务完成后计数减一
                        latch.countDown();
                    }
                });
            }
            try {
                // 等待所有任务完成
                latch.await();
            } catch (InterruptedException e) {
                log.error("V-LINKER能力开放平台.大华icc同步设备..异常...msg={}", e.getMessage(), e);
            }
        }
        //设备同步完成自动给所有设备订阅上下线事件
        ThirdPartyPlatformsBaseSubscribeReq subscribeReq = new ThirdPartyPlatformsBaseSubscribeReq();
        subscribeReq.setThirdPartyPlatformsAuthEntity(thirdPartyPlatformsAuthEntity);
        subscribeReq.setPushUrl(thirdPartyPlatformsAuthEntity.getAlarmPushUrl());
        subscribeReq.setCategory(EventCategoryEnums.state.getCode());
        DtoResult<Void> dtoResult = this.subscribeAlgorithm(subscribeReq);
        log.info("大华设备订阅上下线结果：{}", dtoResult);
    }
}