package com.saida.services.system.sys.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.saida.services.common.base.Result;
import com.saida.services.system.sys.entity.EnterpriseEntity;
import com.saida.services.system.sys.entity.UserEntity;

/**
 * 企业信息
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-11-25 11:09:18
 */
public interface EnterpriseService extends IService<EnterpriseEntity> {

    void addOrUpdate(EnterpriseEntity enterprise);

    IPage<EnterpriseEntity> listPage(UserEntity entity);

    Result getInfoById(Long enterpriseId);
}

