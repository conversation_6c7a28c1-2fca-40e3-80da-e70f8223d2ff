package com.saida.services.system.sys.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.saida.services.open.entity.OpenSysOrgEntity;
import com.saida.services.system.sys.entity.OrgEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SysOrgMapper extends BaseMapper<OpenSysOrgEntity> {

    List<OpenSysOrgEntity> getList(@Param("parentId") Long parentId);

    List<OpenSysOrgEntity> getListByName(@Param("idChain") String idChain, @Param("name") String name);

    List<OpenSysOrgEntity> getRegion(@Param("regionId") Long regionId, @Param("appId") Long appId);
}