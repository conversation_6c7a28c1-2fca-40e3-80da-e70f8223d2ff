package com.saida.services.system.sys.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

@Data
@HeadRowHeight(40)    // 表头行高
@ColumnWidth(30)        // 表头行宽
public class AppCallLogStatisticsUserExportExcel {

    /**
     * 日期
     */
    @ExcelProperty(value = "日期", index = 0)
    private String callDay;
    /**
     * 应用名称
     */
    @ExcelProperty(value = "应用名称", index = 1)
    private String appName;
    /**
     * 总调用次数
     */
    @ExcelProperty(value = "用量", index = 2)
    private Integer totalCount;
}
