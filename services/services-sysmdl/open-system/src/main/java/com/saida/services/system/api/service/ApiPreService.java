package com.saida.services.system.api.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.saida.services.common.base.DtoResult;
import com.saida.services.common.base.Result;
import com.saida.services.common.tools.JwtUtil;
import com.saida.services.common.tools.SpringUtil;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.open.biz.req.pre.ThirdPartyPlatformsBaseDeletePrePointReq;
import com.saida.services.open.biz.req.pre.ThirdPartyPlatformsBaseJumpPrePointReq;
import com.saida.services.open.biz.req.pre.ThirdPartyPlatformsBasePrePointListReq;
import com.saida.services.open.biz.req.pre.ThirdPartyPlatformsBaseSetPrePointReq;
import com.saida.services.open.biz.resp.ThirdPartyPlatformsPrePointListResp;
import com.saida.services.open.dto.DelPrePointDto;
import com.saida.services.open.dto.JumpPrePointDto;
import com.saida.services.open.dto.PrePointListDto;
import com.saida.services.open.dto.SetPrePointDto;
import com.saida.services.open.entity.DeviceInfoEntity;
import com.saida.services.open.entity.ThirdPartyPlatformsAuthEntity;
import com.saida.services.system.biz.service.ThirdPartyVideoPlatformsService;
import com.saida.services.system.sys.entity.SysClientDeviceEntity;
import com.saida.services.system.sys.service.DeviceInfoService;
import com.saida.services.system.sys.service.SysClientDeviceService;
import com.saida.services.system.sys.service.ThirdPartyPlatformsAuthService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class ApiPreService {

    @Autowired
    private DeviceInfoService deviceInfoService;
    @Autowired
    private SysClientDeviceService clientDeviceService;
    @Resource
    private ThirdPartyPlatformsAuthService thirdPartyPlatformsAuthService;

    /**
     * 预置点位列表
     */
    public Result getPrePointList(PrePointListDto dto) {
        try {
            if (null == dto.getDeviceId()) {
                return Result.error("设备ID不能为空！");
            }
            Long appId = JwtUtil.getAppId();
            if (null == appId) {
                return Result.error("请选择一个应用！");
            }

            if (!JwtUtil.isInternal()) {
                SysClientDeviceEntity sysClientDeviceEntity = clientDeviceService.getOne(new LambdaQueryWrapper<SysClientDeviceEntity>()
                        .eq(SysClientDeviceEntity::getClientId, appId)
                        .eq(SysClientDeviceEntity::getDeviceId, dto.getDeviceId()), false);
                if (sysClientDeviceEntity == null) {
                    return Result.error("该应用下输入的设备ID不存在！");
                }
            }
            // 1.查询设备信息
            DeviceInfoEntity deviceInfoEntity = deviceInfoService.getById(dto.getDeviceId());
            if (null == deviceInfoEntity) {
                return Result.error("找不到该设备，请重试！");
            }
            // 找到该设备的授权信息
            ThirdPartyPlatformsAuthEntity thirdPartyPlatformsAuthEntity = thirdPartyPlatformsAuthService.getById(deviceInfoEntity.getTripartiteId());
            if (null == thirdPartyPlatformsAuthEntity) {
                return Result.error("找不到对应的授权信息，请重试！");
            }
            // 获取业务处理类
            String componentName = thirdPartyPlatformsAuthEntity.getComponentName();
            if (StringUtil.isEmpty(componentName)) {
                return Result.error("授权对应业务类不存在，请联系管理员！");
            }
            ThirdPartyPlatformsBasePrePointListReq req = new ThirdPartyPlatformsBasePrePointListReq();
            req.setThirdPartyPlatformsAuthEntity(thirdPartyPlatformsAuthEntity);
            req.setDeviceCode(deviceInfoEntity.getTripartiteSn());
            req.setChannelId(deviceInfoEntity.getChannelId());
            ThirdPartyVideoPlatformsService ThirdPartyVideoPlatformsService = (ThirdPartyVideoPlatformsService) SpringUtil.getBean(componentName);
            DtoResult<List<ThirdPartyPlatformsPrePointListResp>> dtoResult = ThirdPartyVideoPlatformsService.getPrePointList(req);
            if (!dtoResult.success()) {
                return Result.error(dtoResult.getMessage(), dtoResult.getError());
            }
            return Result.ok(dtoResult.getData());
        } catch (Exception e) {
            log.error("查询预置点位列表出错...msg={}", e.getMessage(), e);
            return Result.error("查询预置点位列表出错");
        }
    }

    public Result setPrePoint(SetPrePointDto dto) {
        try {
            if (null == dto.getDeviceId()) {
                return Result.error("设备ID不能为空！");
            }
            Long appId = JwtUtil.getAppId();
            if (null == appId) {
                return Result.error("请选择一个应用！");
            }

            if (!JwtUtil.isInternal()) {
                SysClientDeviceEntity sysClientDeviceEntity = clientDeviceService.getOne(new LambdaQueryWrapper<SysClientDeviceEntity>()
                        .eq(SysClientDeviceEntity::getClientId, appId)
                        .eq(SysClientDeviceEntity::getDeviceId, dto.getDeviceId()), false);
                if (sysClientDeviceEntity == null) {
                    return Result.error("该应用下输入的设备ID不存在！");
                }
            }
            // 1.查询设备信息
            DeviceInfoEntity deviceInfoEntity = deviceInfoService.getById(dto.getDeviceId());
            if (null == deviceInfoEntity) {
                return Result.error("找不到该设备，请重试！");
            }
            // 找到该设备的授权信息
            ThirdPartyPlatformsAuthEntity thirdPartyPlatformsAuthEntity = thirdPartyPlatformsAuthService.getById(deviceInfoEntity.getTripartiteId());
            if (null == thirdPartyPlatformsAuthEntity) {
                return Result.error("找不到对应的授权信息，请重试！");
            }
            // 获取业务处理类
            String componentName = thirdPartyPlatformsAuthEntity.getComponentName();
            if (StringUtil.isEmpty(componentName)) {
                return Result.error("授权对应业务类不存在，请联系管理员！");
            }
            ThirdPartyPlatformsBaseSetPrePointReq req = new ThirdPartyPlatformsBaseSetPrePointReq();
            req.setThirdPartyPlatformsAuthEntity(thirdPartyPlatformsAuthEntity);
            req.setDeviceCode(deviceInfoEntity.getTripartiteSn());
            req.setIndex(dto.getIndex());
            req.setName(dto.getName());
            req.setChannelId(deviceInfoEntity.getChannelId());
            ThirdPartyVideoPlatformsService ThirdPartyVideoPlatformsService = (ThirdPartyVideoPlatformsService) SpringUtil.getBean(componentName);
            DtoResult<Void> dtoResult = ThirdPartyVideoPlatformsService.setPrePoint(req);
            if (!dtoResult.success()) {
                return Result.error(dtoResult.getMessage());
            }
            return Result.ok(dtoResult.getData());
        } catch (Exception e) {
            log.error("新增预置点位列表出错...msg={}", e.getMessage(), e);
            return Result.error("新增预置点位列表出错");
        }
    }

    public Result jumpPrePoint(JumpPrePointDto dto) {
        try {
            if (null == dto.getDeviceId()) {
                return Result.error("设备ID不能为空！");
            }
            Long appId = JwtUtil.getAppId();
            if (null == appId) {
                return Result.error("请选择一个应用！");
            }
            if (!JwtUtil.isInternal()) {
                SysClientDeviceEntity sysClientDeviceEntity = clientDeviceService.getOne(new LambdaQueryWrapper<SysClientDeviceEntity>()
                        .eq(SysClientDeviceEntity::getClientId, appId)
                        .eq(SysClientDeviceEntity::getDeviceId, dto.getDeviceId()), false);
                if (sysClientDeviceEntity == null) {
                    return Result.error("该应用下输入的设备ID不存在！");
                }
            }
            // 1.查询设备信息
            DeviceInfoEntity deviceInfoEntity = deviceInfoService.getById(dto.getDeviceId());
            if (null == deviceInfoEntity) {
                return Result.error("找不到该设备，请重试！");
            }
            // 找到该设备的授权信息
            ThirdPartyPlatformsAuthEntity thirdPartyPlatformsAuthEntity = thirdPartyPlatformsAuthService.getById(deviceInfoEntity.getTripartiteId());
            if (null == thirdPartyPlatformsAuthEntity) {
                return Result.error("找不到对应的授权信息，请重试！");
            }
            // 获取业务处理类
            String componentName = thirdPartyPlatformsAuthEntity.getComponentName();
            if (StringUtil.isEmpty(componentName)) {
                return Result.error("授权对应业务类不存在，请联系管理员！");
            }
            ThirdPartyPlatformsBaseJumpPrePointReq req = new ThirdPartyPlatformsBaseJumpPrePointReq();
            req.setThirdPartyPlatformsAuthEntity(thirdPartyPlatformsAuthEntity);
            req.setDeviceCode(deviceInfoEntity.getTripartiteSn());
            req.setIndex(dto.getIndex());
            req.setChannelId(deviceInfoEntity.getChannelId());
            ThirdPartyVideoPlatformsService ThirdPartyVideoPlatformsService = (ThirdPartyVideoPlatformsService) SpringUtil.getBean(componentName);
            DtoResult<Void> dtoResult = ThirdPartyVideoPlatformsService.jumpPrePoint(req);
            if (!dtoResult.success()) {
                return Result.error(dtoResult.getMessage());
            }
            return Result.ok(dtoResult.getData());
        } catch (Exception e) {
            log.error("跳转预置点位列表出错...msg={}", e.getMessage(), e);
            return Result.error("跳转预置点位列表出错");
        }
    }

    public Result deletePrePoint(DelPrePointDto dto) {
        try {
            if (null == dto.getDeviceId()) {
                return Result.error("设备ID不能为空！");
            }
            Long appId = JwtUtil.getAppId();
            if (null == appId) {
                return Result.error("请选择一个应用！");
            }
            if (!JwtUtil.isInternal()) {
                SysClientDeviceEntity sysClientDeviceEntity = clientDeviceService.getOne(new LambdaQueryWrapper<SysClientDeviceEntity>()
                        .eq(SysClientDeviceEntity::getClientId, appId)
                        .eq(SysClientDeviceEntity::getDeviceId, dto.getDeviceId()), false);
                if (sysClientDeviceEntity == null) {
                    return Result.error("该应用下输入的设备ID不存在！");
                }
            }
            // 1.查询设备信息
            DeviceInfoEntity deviceInfoEntity = deviceInfoService.getById(dto.getDeviceId());
            if (null == deviceInfoEntity) {
                return Result.error("找不到该设备，请重试！");
            }
            // 找到该设备的授权信息
            ThirdPartyPlatformsAuthEntity thirdPartyPlatformsAuthEntity = thirdPartyPlatformsAuthService.getById(deviceInfoEntity.getTripartiteId());
            if (null == thirdPartyPlatformsAuthEntity) {
                return Result.error("找不到对应的授权信息，请重试！");
            }
            // 获取业务处理类
            String componentName = thirdPartyPlatformsAuthEntity.getComponentName();
            if (StringUtil.isEmpty(componentName)) {
                return Result.error("授权对应业务类不存在，请联系管理员！");
            }
            ThirdPartyPlatformsBaseDeletePrePointReq req = new ThirdPartyPlatformsBaseDeletePrePointReq();
            req.setThirdPartyPlatformsAuthEntity(thirdPartyPlatformsAuthEntity);
            req.setDeviceCode(deviceInfoEntity.getTripartiteSn());
            req.setIndex(dto.getIndex());
            req.setChannelId(deviceInfoEntity.getChannelId());
            ThirdPartyVideoPlatformsService ThirdPartyVideoPlatformsService = (ThirdPartyVideoPlatformsService) SpringUtil.getBean(componentName);
            DtoResult<Void> dtoResult = ThirdPartyVideoPlatformsService.deletePrePoint(req);
            if (!dtoResult.success()) {
                return Result.error(dtoResult.getMessage());
            }
            return Result.ok(dtoResult.getData());
        } catch (Exception e) {
            log.error("删除预置点位列表出错...msg={}", e.getMessage(), e);
            return Result.error("删除预置点位列表出错");
        }
    }
}
