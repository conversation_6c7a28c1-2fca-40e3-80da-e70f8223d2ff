package com.saida.services.system.biz.vcpDlife.biz.impl;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.saida.services.common.tools.RedisUtil;
import com.saida.services.enums.DeviceAuthRedisKeyEnum;
import com.saida.services.open.entity.ThirdPartyPlatformsAuthEntity;
import com.saida.services.system.biz.vcpDlife.biz.VcpDlifeBiz;
import com.saida.services.system.biz.vcpDlife.component.SendToVcpDlifeRequest;
import com.saida.services.system.biz.vcpDlife.enums.VcpDlifeApiEnum;
import com.saida.services.system.biz.vcpDlife.req.VcpDlifeDeviceListReq;
import com.saida.services.system.biz.vcpDlife.req.VcpDlifeDeviceTreeReq;
import com.saida.services.system.biz.vcpDlife.req.VcpDlifeLoginReq;
import com.saida.services.system.biz.vcpDlife.resp.VcpDlifeBaseResp;
import com.saida.services.system.biz.vcpDlife.resp.VcpDlifeDeviceListResp;
import com.saida.services.system.biz.vcpDlife.resp.VcpDlifeDeviceTreeResp;
import com.saida.services.system.biz.vcpDlife.resp.VcpDlifeLoginResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Component
public class VcpDlifeBizImpl implements VcpDlifeBiz {

    public static final String authRedisKey = DeviceAuthRedisKeyEnum.VCP_DLIFE.getKey();

    @Resource
    private ObjectMapper objectMapper;
    @Autowired
    private SendToVcpDlifeRequest sendRequest;
    @Resource
    private RedisUtil redisUtil;

    @Override
    public void login(ThirdPartyPlatformsAuthEntity thirdPartyPlatformsAuthEntity) {
        VcpDlifeBaseResp<VcpDlifeLoginResp> baseResp;
        String resp;
        try {
            VcpDlifeLoginReq req = new VcpDlifeLoginReq();
            req.setGrantType("vcp_189");
            resp = sendRequest.sendRequest(VcpDlifeApiEnum.LOGIN, req, thirdPartyPlatformsAuthEntity);
            TypeReference<VcpDlifeBaseResp<VcpDlifeLoginResp>> typeReference = new TypeReference<VcpDlifeBaseResp<VcpDlifeLoginResp>>() {
            };
            baseResp = objectMapper.readValue(resp, typeReference);
            if (baseResp.ok()) {
                VcpDlifeLoginResp data = baseResp.getData();
                data.setNowTime(System.currentTimeMillis());
                redisUtil.set(authRedisKey + thirdPartyPlatformsAuthEntity.getId(), JSON.toJSONString(data), data.getExpiresIn());
                log.info("电信天翼视联平台登录成功...token={}", data);
            }
        } catch (Exception e) {
            log.error("电信天翼视联平台登录出错...msg={}", e.getMessage(), e);
        }
    }

    @Override
    public VcpDlifeBaseResp<List<VcpDlifeDeviceTreeResp>> getDeviceTreeList(VcpDlifeDeviceTreeReq req, ThirdPartyPlatformsAuthEntity thirdPartyPlatformsAuthEntity) {
        VcpDlifeBaseResp<List<VcpDlifeDeviceTreeResp>> baseResp = null;
        String resp;
        try {
            resp = sendRequest.sendRequest(VcpDlifeApiEnum.DEVICE_TREE, req, thirdPartyPlatformsAuthEntity);
            TypeReference<VcpDlifeBaseResp<List<VcpDlifeDeviceTreeResp>>> typeReference = new TypeReference<VcpDlifeBaseResp<List<VcpDlifeDeviceTreeResp>>>() {
            };
            baseResp = objectMapper.readValue(resp, typeReference);
        } catch (Exception e) {
            log.error("电信天翼视联平台获取设备树出错...msg={}", e.getMessage(), e);
        }
        return baseResp;
    }

    @Override
    public VcpDlifeBaseResp<VcpDlifeDeviceListResp> getDeviceList(VcpDlifeDeviceListReq req, ThirdPartyPlatformsAuthEntity thirdPartyPlatformsAuthEntity) {
        VcpDlifeBaseResp<VcpDlifeDeviceListResp> baseResp = null;
        String resp;
        try {
            resp = sendRequest.sendRequest(VcpDlifeApiEnum.DEVICE_LIST, req, thirdPartyPlatformsAuthEntity);
            TypeReference<VcpDlifeBaseResp<VcpDlifeDeviceListResp>> typeReference = new TypeReference<VcpDlifeBaseResp<VcpDlifeDeviceListResp>>() {
            };
            baseResp = objectMapper.readValue(resp, typeReference);
        } catch (Exception e) {
            log.error("电信天翼视联平台获取设备列表出错...msg={}", e.getMessage(), e);
        }
        return baseResp;
    }
}