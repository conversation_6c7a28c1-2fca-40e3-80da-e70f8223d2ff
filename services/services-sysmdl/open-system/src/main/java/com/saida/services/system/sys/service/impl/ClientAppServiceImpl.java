package com.saida.services.system.sys.service.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;
import cn.hutool.crypto.symmetric.AES;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.saida.services.algorithm.req.VlinkerAlgorithmSyncPlatformReq;
import com.saida.services.common.base.DtoResult;
import com.saida.services.common.tools.IdGenerateUtil;
import com.saida.services.common.tools.JwtUtil;
import com.saida.services.common.tools.RedisUtil;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.constant.RedisConstants;
import com.saida.services.exception.BizRuntimeException;
import com.saida.services.feign.algorithm.system.IFeignAlgorithmSystemApiController;
import com.saida.services.open.entity.DeviceInfoEntity;
import com.saida.services.open.enums.OpenThirdPartyPlatformsTypeEnum;
import com.saida.services.system.job.OrderRedisJob;
import com.saida.services.system.sys.entity.ClientAppEntity;
import com.saida.services.system.sys.entity.OrderInfoEntity;
import com.saida.services.system.sys.mapper.ClientAppMapper;
import com.saida.services.system.sys.mapper.OrderInfoMapper;
import com.saida.services.system.sys.service.AttributeDetailService;
import com.saida.services.system.sys.service.ClientAppService;
import com.saida.services.system.sys.service.SysClientDeviceService;
import com.saida.services.system.sys.service.ThirdPartyPlatformsAuthService;
import com.saida.services.tools.attr.AttrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service("clientAppService")
public class ClientAppServiceImpl extends ServiceImpl<ClientAppMapper, ClientAppEntity> implements ClientAppService {

    @Resource
    private OrderInfoMapper orderInfoMapper;
    @Resource
    private AttributeDetailService attributeDetailService;
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private ThirdPartyPlatformsAuthService thirdPartyPlatformsAuthService;
    @Resource
    private OrderRedisJob orderRedisJob;
    @Resource
    private SysClientDeviceService sysClientDeviceService;
    @Resource
    private IFeignAlgorithmSystemApiController iFeignAlgorithmSystemApiController;

    /**
     * 添加或更新客户端应用实体
     *
     * @param clientAppEntity 要添加或更新的客户端应用实体
     * @throws BizRuntimeException 如果订单不存在或应用已存在或应用授权生成失败，则抛出此异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addOrUpdate(ClientAppEntity clientAppEntity) {
        // 根据订单ID查询订单信息
        OrderInfoEntity order = orderInfoMapper.selectById(clientAppEntity.getOrderId());
        if (order == null) {
            throw new BizRuntimeException("订单不存在");
        }

        Long category = order.getCategory();
        boolean hasVideo = Objects.equals(OpenThirdPartyPlatformsTypeEnum.VIDEO.getDicId(), category);

        // 如果ClientAppEntity的ID为空，则执行新增操作
        if (clientAppEntity.getId() == null) {
            clientAppEntity.setType(category);
            // 设置创建人和创建时间
            clientAppEntity.setCreateUser(JwtUtil.getUserId());
            clientAppEntity.setCreateTime(DateTime.now());
            // 设置更新人和更新时间
            clientAppEntity.setUpdateUser(JwtUtil.getUserId());
            clientAppEntity.setUpdateTime(DateTime.now());

            // 查询是否已存在相同名称的应用
            ClientAppEntity tmp = getOne(
                    new LambdaQueryWrapper<ClientAppEntity>()
                            .eq(ClientAppEntity::getCreateUser, JwtUtil.getUserId())
                            .eq(ClientAppEntity::getName, clientAppEntity.getName())
                    , false);
            if (tmp != null) {
                throw new BizRuntimeException(String.format("应用：%s 已存在", clientAppEntity.getName()));
            }

            boolean appKeyPass = false;
            for (int i = 0; i < 10; i++) {
                // 生成appKey
                // 防止appKey重复，重试10次
                clientAppEntity.setAppKey(String.format("%s%s%s", RandomUtil.randomString(3), IdGenerateUtil.generateInviteCode62(), RandomUtil.randomString(3)));
                // 查询是否已存在相同的appKey
                ClientAppEntity exist = getOne(new LambdaQueryWrapper<ClientAppEntity>().eq(ClientAppEntity::getAppKey, clientAppEntity.getAppKey()), false);
                if (exist == null) {
                    appKeyPass = true;
                    break;
                }
            }
            if (!appKeyPass) {
                throw new BizRuntimeException("应用授权生成失败，请重试");
            }

            // 设置SecretKey和AES秘钥
            clientAppEntity.setSecretKey(IdUtil.fastSimpleUUID());
            clientAppEntity.setAesKey(Base64.encode(new AES(Mode.CBC, Padding.PKCS5Padding).getSecretKey().getEncoded()));

            // 保存ClientAppEntity
            save(clientAppEntity);
        } else {
            // 如果ClientAppEntity的ID不为空，则执行更新操作
            // 设置更新人和更新时间
            clientAppEntity.setUpdateUser(JwtUtil.getUserId());
            clientAppEntity.setUpdateTime(DateTime.now());

            // 查询是否已存在相同名称且ID不同的应用
            ClientAppEntity tmp = getOne(
                    new LambdaQueryWrapper<ClientAppEntity>()
                            .ne(ClientAppEntity::getId, clientAppEntity.getId())
                            .eq(ClientAppEntity::getCreateUser, JwtUtil.getUserId())
                            .eq(ClientAppEntity::getName, clientAppEntity.getName())
                    , false);
            if (tmp != null) {
                throw new BizRuntimeException(String.format("应用：%s 已存在", clientAppEntity.getName()));
            }

            // 更新ClientAppEntity的AppKey、SecretKey和AES秘钥为null
            clientAppEntity.setAppKey(null);
            clientAppEntity.setSecretKey(null);
            // AES秘钥暂不支持修改
            clientAppEntity.setAesKey(null);

            // 更新ClientAppEntity
            updateById(clientAppEntity);
        }
        if (hasVideo) {
            VlinkerAlgorithmSyncPlatformReq req = new VlinkerAlgorithmSyncPlatformReq();
            req.setName(clientAppEntity.getName());
            req.setAccount(clientAppEntity.getAppKey());
            req.setAesKey(clientAppEntity.getAesKey());
            req.setSecretKey(clientAppEntity.getSecretKey());
            DtoResult<Void> baseResp = iFeignAlgorithmSystemApiController.synchronizationPlatform(req);
            log.info("同步第三方平台请求：{} => 结果：{}", req, JSON.toJSON(baseResp));
            if (!baseResp.success()) {
                throw new BizRuntimeException(baseResp.getMessage());
            }
        }
        // 缓存ClientAppEntity
        cacheClientApp(clientAppEntity.getId());
    }

    private void cacheClientApp(Long id) {
        ClientAppEntity clientApp = getById(id);
        if (clientApp == null) {
            return;
        }
        redisUtil.set(RedisConstants.CLIENT_APP + clientApp.getAppKey(), JSON.toJSONString(clientApp));
        orderRedisJob.initOrderByEdit(clientApp.getOrderId());
    }

    @Override
    public IPage<ClientAppEntity> listPage(ClientAppEntity entity) {
        IPage<ClientAppEntity> page = page(new Page<>(entity.getPageNum(), entity.getPageSize()),
                new LambdaQueryWrapper<ClientAppEntity>().eq(ClientAppEntity::getCreateUser, JwtUtil.getUserId()));
        if (page == null || page.getRecords() == null || page.getRecords().isEmpty()) {
            return page;
        }
        fillAttr(page.getRecords());
        return page;
    }

    @Override
    public List<ClientAppEntity> getList(ClientAppEntity entity) {
        LambdaQueryWrapper<ClientAppEntity> lambdaQueryWrapper = new LambdaQueryWrapper<ClientAppEntity>()
                .eq(!JwtUtil.isSupper(), ClientAppEntity::getCreateUser, JwtUtil.getUserId())
                .eq(StringUtil.isNotEmpty(entity.getType()), ClientAppEntity::getType, entity.getType())
                .eq(Objects.nonNull(entity.getOrderId()), ClientAppEntity::getOrderId, entity.getOrderId());

        List<ClientAppEntity> list = super.list(lambdaQueryWrapper);
        if (CollectionUtil.isEmpty(list)) {
            return list;
        }
        fillAttr(list);
        return list;
    }

    @Override
    public ClientAppEntity getInfo(Long id) {
        ClientAppEntity app = getById(id);
        if (app == null) {
            return null;
        }
        return fillAttr(new ArrayList<ClientAppEntity>() {{
            add(app);
        }}).get(0);
    }

    @Override
    public List<ClientAppEntity> getListByDevice(DeviceInfoEntity deviceInfoEntity) {
        return super.baseMapper.getListByDevice(deviceInfoEntity);
    }

    @Override
    public List<ClientAppEntity> getAppListByOrder(Long orderId) {
        List<ClientAppEntity> list = super.list(new LambdaQueryWrapper<ClientAppEntity>()
                .eq(ClientAppEntity::getOrderId, orderId));
        if (CollectionUtil.isEmpty(list)) {
            return list;
        }
        fillAttr(list);
        return list;
    }

    private List<ClientAppEntity> fillAttr(List<ClientAppEntity> records) {
        if (records == null || records.isEmpty()) {
            return records;
        }
        Map<Object, Object> dicMap = new HashMap<>();
        Map<Long, String> attrMap = attributeDetailService.getAllIdNameMap();
        if (attrMap != null && !attrMap.isEmpty()) {
            dicMap.putAll(attrMap);
        }
        records.forEach(it -> {
            it.setAppSecret(it.getSecretKey());
        });
        List<OrderInfoEntity> orderList = orderInfoMapper.selectBatchIds(records.stream().map(ClientAppEntity::getOrderId).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
        if (orderList != null && !orderList.isEmpty()) {
//            dicMap.putAll(orderList.stream().collect(Collectors.toMap(OrderInfoEntity::getId, OrderInfoEntity::getDeviceNumber)));
            dicMap.putAll(orderList.stream().collect(Collectors.toMap(OrderInfoEntity::getId, o -> new HashMap<String, Object>() {{
                put("orderDeviceCount", o.getDeviceNumber());
                put("orderNumber", o.getOrderNumber());
            }})));
        }
        Map<Long, Integer> countByIds = sysClientDeviceService.getCountByIds(records.stream().map(ClientAppEntity::getId).collect(Collectors.toList()));
        if (CollectionUtil.isNotEmpty(countByIds)) {
            dicMap.putAll(countByIds);
        }
        records.replaceAll(o -> AttrUtil.putAttr(o, dicMap));
        return records;
    }
}