package com.saida.services.system.sys.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.saida.services.common.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR>
 * @ClassName UavDeviceInfo
 * @Desc
 * @Date 2025/2/10 14:15
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("uav_device_ref")
public class UavDeviceRefEntity {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    private String name;

    private String sn;

    // 0:飞机类 1:负载类 2:遥控器类 3:机场类
    private Integer domain;

    private String firmwareVersion;

    // 在线时间
    private Date onlineTime;

    // 在线状态 0离线 1在线
    private Integer onlineStatus;

    // 设备主控sn
    private String mainControlSn;

    // 机场状态	"0":"空闲中","1":"现场调试","2":"远程调试","3":"固件升级中","4":"作业中"
    private Integer dockModeCode;

    // 固件升级状态	"0":"未升级","1":"升级中"
    private Integer firmwareStatus;

    // 机场空调状态 "0":"空闲模式(无制冷、制热、除湿等)","1":"制冷模式","2":"制热模式","3":"除湿模式","4":"制冷退出模式","5":"制热退出模式","6":"除湿退出模式","7":"制冷准备模式","8":"制热准备模式","9":"除湿准备模式"
    private Integer airConditionerState;

    // 机场累计作业次数
    private Integer jobNumber;

    // 工作电流  毫安 / mA
    private Integer workingCurrent;

    // 工作电压 毫伏 / mV
    private Integer workingVoltage;

    // 是否设置备降点	"0":"未设置","1":"已设置"
    private Integer landPointIsConfigured;

    // 备用电池开关	"0":"关闭","1":"开启"
    private Integer backupBatterySwitch;

    // 备用电池电压	备用电池关闭时电压为0","max":"30000","min":"0","step":"1","unit_name":"毫伏 / mV
    private Integer backupBatteryVoltage;

    // 备用电池温度	 摄氏度 / °C
    private BigDecimal backupBatteryTemperature;

    // 仓内湿度 相对湿度 / %RH
    private Integer humidity;

    // 仓内温度
    private BigDecimal temperature;

    // 环境温度
    private BigDecimal environmentTemperature;

    // 飞行器夜航灯状态  "0":"关闭","1":"打开"
    private Integer nightLightsState;

    // 飞行器限高
    private Integer heightLimit;

    // 限远
    private Integer distanceLimit;

    // 飞行器避障状态	"0":"关闭","1":"开启"
    private Integer obstacleAvoidance;

    // 飞行安全数据库版本
    private String flysafeDatabaseVersion;

    // 电池的总剩余电量
    private Integer batteryCapacityPercent;

    // 电压	 毫伏 / mV
    private Integer batteryVoltage;

    // 电池温度
    private BigDecimal batteryTemperature;

    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private Long createBy;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    private String remark;
}
