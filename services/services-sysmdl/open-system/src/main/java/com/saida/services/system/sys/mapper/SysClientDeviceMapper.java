package com.saida.services.system.sys.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.saida.services.entities.pojo.CountDto;
import com.saida.services.open.dto.DeviceInfoDto;
import com.saida.services.open.dto.SysClientDeviceParamsDto;
import com.saida.services.open.entity.DeviceInfoEntity;
import com.saida.services.system.sys.entity.SysClientDeviceEntity;
import com.saida.services.system.vcpDlifeAlarm.dto.VcpDeviceParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * 客户应用设备
 *
 * <AUTHOR>
 * email ${email}
 * date 2023-12-08 15:37:37
 */
@Mapper
public interface SysClientDeviceMapper extends BaseMapper<SysClientDeviceEntity> {

    List<DeviceInfoDto> selectedDevicesByVcp(@Param("param") VcpDeviceParam param);

    List<DeviceInfoDto> selectedDevices(@Param("entity") SysClientDeviceParamsDto entity);

    List<DeviceInfoDto> toBeSelectedDevices(@Param("entity") SysClientDeviceParamsDto entity);

    SysClientDeviceEntity getInfoById(@Param("entity") SysClientDeviceEntity entity);

    List<CountDto> getCountByIds(@Param("ids") Collection<Long> ids);

    List<DeviceInfoEntity> getSnByIds(@Param("ids") Collection<Long> ids);

    List<DeviceInfoEntity> getEffectiveDeviceListByApp(@Param("entity") SysClientDeviceEntity entity);
}