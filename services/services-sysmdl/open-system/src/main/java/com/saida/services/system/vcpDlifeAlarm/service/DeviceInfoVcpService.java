package com.saida.services.system.vcpDlifeAlarm.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.saida.services.algorithm.dto.PushAlarmDto;
import com.saida.services.common.base.Result;
import com.saida.services.entities.base.BaseRequest;
import com.saida.services.open.entity.DeviceInfoVcpEntity;
import com.saida.services.system.vcpDlifeAlarm.dto.VcpAlarmDeviceParam;

import java.util.Set;

public interface DeviceInfoVcpService extends IService<DeviceInfoVcpEntity> {

    /**
     * 根据报警信息分页获取设备列表。
     *
     * 本函数旨在为用户提供一种通过报警信息来分页查询相关设备的方法。这对于处理大量设备数据和报警信息的场景非常有用，
     * 它可以帮助用户快速定位到与特定报警事件相关的设备，以便进行进一步的处理或分析。
     *
     * @param request 基础请求对象，包含请求的标识信息和鉴权数据等。
     * @param param 报警设备查询参数，具体包括分页信息、报警类型等过滤条件。
     * @return Result 对象，包含查询结果的分页信息和设备列表。如果查询成功，设备列表将包含满足过滤条件的设备信息；
     *         如果查询失败，Result 对象将包含错误代码和错误信息。
     */
    Result getDeviceByAlarmListPage(BaseRequest request, VcpAlarmDeviceParam param);
    /**
     * 创建设备。
     *
     * 通过此方法可以为指定的账户创建设备。设备的创建涉及到设备标识的处理和账户关联的操作。
     *
     * @param deviceIds 设备标识的字符串，可能包含多个设备标识，具体格式根据实际业务确定。
     * @param accountId 要关联的账户的ID，用于标识设备的所有者。
     * @return 返回一个Result对象，其中包含设备创建操作的结果信息，如成功与否、错误码等。
     */
    Result createDevice(Set<String> deviceIds, Long accountId);


    /**
     * 取消绑定设备接口。
     *
     * 该接口用于一次性取消绑定多个设备。调用此接口后，指定的设备将与当前用户解除绑定关系。
     * 解绑后，用户将无法再通过账号权限控制这些设备。
     *
     * @param deviceIds 需要取消绑定的设备ID集合。设备ID是设备的唯一标识符，用于准确指定要操作的设备。
     * @return 返回一个Result对象，其中包含操作的结果信息。例如，操作是否成功，如果失败，失败的原因等。
     */
    Result unbindTheDevice(Set<String> deviceIds);

    /**
     * 报警推送接口。
     * <p>
     * 该接口用于接收并处理报警信息。通过接收一个BaseAlarmDto对象，实现对不同类型报警的统一处理。
     * 报警信息的具体处理逻辑应在该方法内实现，包括但不限于发送报警通知、记录报警日志等。
     *
     * @param pushAlarmDto 报警信息的封装对象，包含了报警的详细信息，如报警类型、报警内容等。
     */
    void alarmPush(PushAlarmDto pushAlarmDto);


    /**
     * 订阅警报接口。
     *
     * 该接口用于订阅特定设备或虚拟摄像头(VCP)的警报信息。通过传入设备ID数组和VCP代码数组，
     * 系统将为订阅者设置相应的警报订阅。订阅成功后，当订阅的设备或VCP产生警报时，订阅者将收到警报通知。
     *
     * @param deviceId 设备ID数组，用于指定订阅的物理设备。可以通过设备ID订阅特定设备的警报。
     * @param vcpCode VCP代码数组，用于指定订阅的虚拟摄像头。可以通过VCP代码订阅特定虚拟摄像头的警报。
     * @return 返回一个Result对象，其中包含操作的结果信息，如成功与否、错误代码等。
     */
    Result alarmSubscription(String[] deviceId, String[] vcpCode);
}



