package com.saida.services.system.sys.controller;

import cn.hutool.core.lang.tree.Tree;
import com.saida.services.common.base.Result;
import com.saida.services.log.LogOperation;
import com.saida.services.log.LogOperationEnum;
import com.saida.services.log.ModuleEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.saida.services.system.sys.entity.RolePermissionEntity;
import com.saida.services.system.sys.service.RolePermissionService;

import java.util.List;


/**
 * 角色-资源
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-10-07 09:28:38
 */
@RestController
@RequestMapping("sys/rolepermission")
public class RolePermissionController {
    @Autowired
    private RolePermissionService rolePermissionService;

    @PostMapping("save")
    @LogOperation(type = LogOperationEnum.ADD,module = ModuleEnum.ROLE, func = "保存角色资源")
    public Result save(RolePermissionEntity entity){
        if(entity.getRid() == null){
            return Result.error("角色ID必传");
        }
        rolePermissionService.addOrUpdate(entity);
        return Result.ok();
    }

    @GetMapping("getByRole")
    @LogOperation(type = LogOperationEnum.QUERY,module = ModuleEnum.ROLE, func = "获取角色资源")
    public Result getByRole(RolePermissionEntity entity){
        if(entity.getRid() == null){
            return Result.error("角色必传");
        }
        List<Tree<Long>> list = rolePermissionService.getByRole(entity.getRid());
        return Result.ok(list);
    }
}
