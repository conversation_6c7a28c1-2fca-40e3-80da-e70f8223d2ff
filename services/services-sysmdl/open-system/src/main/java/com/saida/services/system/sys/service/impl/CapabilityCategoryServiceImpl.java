package com.saida.services.system.sys.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.saida.services.common.base.DtoResult;
import com.saida.services.common.base.Result;
import com.saida.services.common.entity.BasePageInfoEntity;
import com.saida.services.common.vo.BaseEnumVo;
import com.saida.services.entities.base.BaseRequest;
import com.saida.services.open.enums.OpenThirdPartyPlatformsTypeEnum;
import com.saida.services.system.sys.dto.CapabilityCategoryDto;
import com.saida.services.system.sys.entity.CapabilityCategoryEntity;
import com.saida.services.system.sys.mapper.CapabilityCategoryMapper;
import com.saida.services.system.sys.service.CapabilityCategoryService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;


@Service("capabilityCategoryService")
public class CapabilityCategoryServiceImpl extends ServiceImpl<CapabilityCategoryMapper, CapabilityCategoryEntity> implements CapabilityCategoryService {

    @Override
    public DtoResult<List<BaseEnumVo<String>>> getCategoryTypeList() {
        List<BaseEnumVo<String>> list = new ArrayList<>();
        BaseEnumVo<String> vo = new BaseEnumVo<>();
        vo.setValue(String.valueOf(OpenThirdPartyPlatformsTypeEnum.VIDEO.getDicId()));
        vo.setLabel(OpenThirdPartyPlatformsTypeEnum.VIDEO.getMsg());
        list.add(vo);

        vo = new BaseEnumVo<>();
        vo.setValue(String.valueOf(OpenThirdPartyPlatformsTypeEnum.IOT.getDicId()));
        vo.setLabel(OpenThirdPartyPlatformsTypeEnum.IOT.getMsg());
        list.add(vo);
        return DtoResult.ok(list);
    }

    @Override
    public Result listPage(BaseRequest baseRequest, CapabilityCategoryEntity entity) {
        PageHelper.startPage(baseRequest.getPageNum(), baseRequest.getPageSize());
        List<CapabilityCategoryEntity> list = super.baseMapper.getList(entity);
        return Result.ok(new BasePageInfoEntity<>(new PageInfo<>(list)));
    }

    @Override
    public Result getList(CapabilityCategoryEntity entity) {
        List<CapabilityCategoryEntity> list = super.baseMapper.getList(entity);
        List<CapabilityCategoryDto> resList = new ArrayList<>();
        for (CapabilityCategoryEntity item : list) {
            CapabilityCategoryDto dto = new CapabilityCategoryDto();
            BeanUtils.copyProperties(item, dto);
            resList.add(dto);
        }
        return Result.ok(resList);
    }

    @Override
    public CapabilityCategoryEntity getInfoById(CapabilityCategoryEntity entity) {
        entity = super.baseMapper.getInfoById(entity);
        return entity;
    }

    @Override
    public Result saveOrUpdateBean(CapabilityCategoryEntity entity) {
        if (Objects.equals(entity.getHomePage(), 1)) {
            long count = this.count(new LambdaQueryWrapper<CapabilityCategoryEntity>()
                    .eq(CapabilityCategoryEntity::getHomePage, 1)
                    .ne(null != entity.getId(), CapabilityCategoryEntity::getId, entity.getId())
            );
            if (count >= 5) {
                return Result.error("首页展示最大开启数为5个");
            }
        }
        super.saveOrUpdate(entity);
        return Result.ok();
    }

    @Override
    public void deleteByIds(String ids) {
        super.removeByIds(Arrays.asList(ids.split(",")));
    }

    @Override
    public List<CapabilityCategoryEntity> getByIds(List<String> list) {
        return getBaseMapper().selectBatchIds(list);
    }
}