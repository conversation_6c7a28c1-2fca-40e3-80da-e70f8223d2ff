package com.saida.services.system.biz.vcpDlife.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.saida.project.services.ytld.biz.TyVisualVideoBiz;
import com.saida.project.services.ytld.config.TyVisualVideoConfig;
import com.saida.project.services.ytld.constant.TyVisualVideoConstantRedisKey;
import com.saida.project.services.ytld.enums.TyVisualVideoCodeEnum;
import com.saida.project.services.ytld.pojo.resp.TyVisualVideoAccessTokenResp;
import com.saida.project.services.ytld.pojo.resp.TyVisualVideoResp;
import com.saida.services.common.base.Result;
import com.saida.services.enums.ResultEnum;
import com.saida.services.open.entity.ThirdPartyPlatformsAuthEntity;
import com.saida.services.system.biz.vcpDlife.dto.CallBackDto;
import com.saida.services.system.biz.vcpDlife.dto.GetAuthPageDto;
import com.saida.services.system.biz.vcpDlife.service.VcpDlifeService;
import com.saida.services.system.sys.service.ThirdPartyPlatformsAuthService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class VcpDlifeServiceImpl implements VcpDlifeService {

    @Lazy
    @Resource
    private TyVisualVideoConfig tyVisualVideoConfig;
    @Lazy
    @Resource
    private TyVisualVideoBiz tyVisualVideoBiz;
    @Resource
    private ThirdPartyPlatformsAuthService thirdPartyPlatformsAuthService;

    @Override
    public Result getOauthUrl(GetAuthPageDto dto) {
        if (dto.getType() == null) {
            return Result.error("类型不能为空！");
        }
        long count = 0;
        // 新增
        if (dto.getType() == 1) {
            count = thirdPartyPlatformsAuthService.count(new LambdaQueryWrapper<ThirdPartyPlatformsAuthEntity>()
                    .eq(ThirdPartyPlatformsAuthEntity::getPlatformName, dto.getPlatformName()));
        }
        // 修改
        if (dto.getType() == 2) {
            count = thirdPartyPlatformsAuthService.count(new LambdaQueryWrapper<ThirdPartyPlatformsAuthEntity>()
                    .eq(ThirdPartyPlatformsAuthEntity::getPlatformName, dto.getPlatformName())
                    .ne(ThirdPartyPlatformsAuthEntity::getId, dto.getThirdPartyPlatformsAuthId())
            );
        }
        if (count > 0) {
            return Result.error("平台名称已存在，请重新输入！");
        }
        Long thirdPartyPlatformsAuthId = dto.getThirdPartyPlatformsAuthId();
        // 赋值
        tyVisualVideoConfig.setHost(dto.getHost());
        tyVisualVideoConfig.setAppId(dto.getAppKey());
        tyVisualVideoConfig.setAppSecret(dto.getAppSecret());
        tyVisualVideoConfig.setRedisTokenKey(TyVisualVideoConstantRedisKey.TY_VISUAL_TOKEN + thirdPartyPlatformsAuthId);

        TyVisualVideoResp<String> tyVisualVideoResp = tyVisualVideoBiz.getOauthUrl(tyVisualVideoConfig);
        if (tyVisualVideoResp.success()) {
            return Result.ok(tyVisualVideoResp.getData());
        }
        return Result.error();
    }

    @Override
    public Result callback(CallBackDto dto) {
        if (dto.getType() == null) {
            return Result.error("类型不能为空！");
        }
        String authCode = dto.getAuthCode();
        String errorCode = dto.getErrorCode();

        // 赋值
        tyVisualVideoConfig.setHost(dto.getHost());
        tyVisualVideoConfig.setAppId(dto.getAppKey());
        tyVisualVideoConfig.setAppSecret(dto.getAppSecret());
        tyVisualVideoConfig.setRedisTokenKey(TyVisualVideoConstantRedisKey.TY_VISUAL_TOKEN + dto.getId());

        log.info("天翼视联授权-回调事件...authCode={}, errorCode={}", authCode, errorCode);
        if (TyVisualVideoCodeEnum.SUCCESS.getCode().toString().equals(errorCode)) {
            Result result = Result.error();
            if (1 == dto.getType()) {
                // 新增
                // 1.存入数据库
                result = thirdPartyPlatformsAuthService.add(dto);
            } else if (2 == dto.getType()) {
                // 编辑
                // 1.存入数据库
                result = thirdPartyPlatformsAuthService.edit(dto);
            }
            if (ResultEnum.SUCCESS.getCode().equals(result.getCode())) {
                // 2.把天翼视联token放入redis
                log.info("天翼视联-回调事件成功，通过授权码换取访问令牌...authCode={}, errorCode={}", authCode, errorCode);
                TyVisualVideoResp<TyVisualVideoAccessTokenResp> tyVisualVideoResp = tyVisualVideoBiz.getAccessToken(authCode, tyVisualVideoConfig);
                if (tyVisualVideoResp.success()) {
                    return Result.ok();
                }
                // thirdPartyPlatformsAuthService.removeById(dto.getId());
            }
        }
        return Result.error();
    }
}