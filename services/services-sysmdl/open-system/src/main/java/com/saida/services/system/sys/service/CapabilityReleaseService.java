package com.saida.services.system.sys.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.saida.services.common.tools.SDNumberUtil;
import com.saida.services.system.config.DicConfig;
import com.saida.services.system.sys.dto.CapabilityCategoryDto;
import com.saida.services.system.sys.dto.CapabilityReleaseDto;
import com.saida.services.system.sys.entity.CapabilityReleaseEntity;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

/**
 * 能力发布
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-11-22 09:37:18
 */
public interface CapabilityReleaseService extends IService<CapabilityReleaseEntity> {

    void addOrUpdate(CapabilityReleaseEntity entity);

    List<CapabilityCategoryDto> getList(CapabilityReleaseEntity entity);

    CapabilityReleaseDto getInfo(Long id);

    List<CapabilityReleaseEntity> getListByCategory(Long categoryId);

    @Getter
    @AllArgsConstructor
    enum MethodType{
        /**
         * POST
         */
        POST(1L, "POST"),

        /**
         * GET
         */
        GET(2L, "GET");

        private final Long typeId = 100004L;
        private final Long tag;
        private final String name;

        public static CapabilityReleaseService.MethodType getType(Long id){
            for(CapabilityReleaseService.MethodType t : values()){
                if(SDNumberUtil.equals(id, DicConfig.getId(t.getTypeId(),t.getTag()))){
                    return t;
                }
            }
            return null;
        }
    }
}

