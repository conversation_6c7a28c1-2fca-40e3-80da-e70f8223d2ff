package com.saida.services.system.sys.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.saida.services.system.sys.entity.AppCallLogStatisticsEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 应用调用统计
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-12-19 13:59:11
 */
@Mapper
public interface AppCallLogStatisticsMapper extends BaseMapper<AppCallLogStatisticsEntity> {

    IPage<AppCallLogStatisticsEntity> userListPage(Page<AppCallLogStatisticsEntity> appCallLogStatisticsEntityPage, @Param("entity") AppCallLogStatisticsEntity entity);

    AppCallLogStatisticsEntity toDayUse(@Param("entity") AppCallLogStatisticsEntity entity);

    IPage<AppCallLogStatisticsEntity> adminListPage(Page<AppCallLogStatisticsEntity> appCallLogStatisticsEntityPage, @Param("entity") AppCallLogStatisticsEntity entity);

    List<AppCallLogStatisticsEntity> excelExportAdmin(@Param("entity") AppCallLogStatisticsEntity entity);

    List<AppCallLogStatisticsEntity> excelExportUser(@Param("entity") AppCallLogStatisticsEntity entity);
}
