package com.saida.services.system.sys.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.saida.services.entities.base.BaseRequest;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 企业信息
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-11-25 11:09:18
 */
@Data
@TableName("sys_enterprise")
public class EnterpriseEntity extends BaseRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    /*
     *
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;
    /*
     * 用户
     */
    private Long userId;
    /*
     * 企业名称
     */
    private String name;
    /*
     * 企业代码
     */
    private String code;
    /*
     * 法人姓名
     */
    private String legalPerson;
    /*
     * 详细地址
     */
    private String address;
    /*
     * 营业执照
     */
    private String businessLicense;

    /*
     * 区域id
     */
    private Long regionId;

    /*
     * 创建人
     */
    private Long createUser;
    /*
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /*
     * 修改人
     */
    private Long updateUser;
    /*
     * 修改时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @TableField(exist = false)
    private String phone;
}
