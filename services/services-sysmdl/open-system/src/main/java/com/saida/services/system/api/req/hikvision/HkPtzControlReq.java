package com.saida.services.system.api.req.hikvision;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * ClassName: HkVideoLiveUrlReq <br/>
 * Description: <br/>
 * date: 2023/09/23 14:47<br/>
 *
 * <AUTHOR> />
 */
@Data
public class HkPtzControlReq implements Serializable {
    private static final long serialVersionUID = 1L;

    /*
     * 监控点唯一标识
     */
    @JSONField(name = "cameraIndexCode")
    private String cameraIndexCode;

    /*
     * 开始或停止操作（0-开始，1-停止，注：GOTO_PRESET命令下填任意值均可转到预置点，建议填0即可）
     */
    @JSONField(name = "action")
    private Integer action = 1;

    /*
     * 控制命令
     */
    @JSONField(name = "command")
    private String command;

    /*
     * 云台速度：取值范围为1-100，默认50
     */
    @JSONField(name = "speed")
    private Integer speed = 50;

    /*
     * 预置点编号，根据【查询预置点信息】接口获取返回参数presetIndex。 整数，通常在300以内
     */
    @JSONField(name = "presetIndex")
    private Integer presetIndex;

}