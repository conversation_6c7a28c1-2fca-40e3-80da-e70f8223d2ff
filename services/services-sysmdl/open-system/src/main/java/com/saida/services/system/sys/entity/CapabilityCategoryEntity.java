package com.saida.services.system.sys.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.saida.services.common.entity.BaseEntity;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * 能力类别
 *
 * <AUTHOR>
 * email ${email}
 * date 2023-11-21 16:02:18
 */
@Data
@JsonInclude
@TableName("capability_category")
public class CapabilityCategoryEntity extends BaseEntity<CapabilityCategoryEntity> implements Serializable {
    private static final long serialVersionUID = 1L;

    /*
     * 名称
     */
    @NotBlank(message = "名称不能为空")
    private String name;

    /*
     * 简介
     */
    @NotBlank(message = "简介不能为空")
    private String introduction;

    /*
     * 背景图片地址
     */
    @NotBlank(message = "背景图片地址不能为空")
    private String imageUrl;

    /*
     * 描述图片地址
     */
    private String desImageUrl;

    /*
     * icon地址
     */
    private String iconUrl;

    /**
     * 能力类型
     */
    private Long type;

    /*
     * 是否展示：0-不展示；1-展示
     */
    private Integer homePage;

    /**
     * 能力列表
     */
    @TableField(exist = false)
    private List<CapabilityReleaseEntity> capabilitys;
}
