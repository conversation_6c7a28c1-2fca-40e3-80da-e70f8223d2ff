package com.saida;

import com.saida.services.SrvSystemApplication;
import com.saida.services.algorithm.req.VlinkerAlgorithmEditBoxFaceReq;
import com.saida.services.common.base.DtoResult;
import com.saida.services.feign.open.system.IFeignOpenSystemApiController;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;

@Slf4j
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = SrvSystemApplication.class)
public class DeviceTest {

    @BeforeEach
    void setUp() {
        MockHttpServletRequest mockRequest = new MockHttpServletRequest();
        mockRequest.addHeader("UserPlatformType", "1");  // 添加需要的头部信息
        mockRequest.addHeader("UserPlatformTypeId", "1");  // 添加需要的头部信息
        RequestContextHolder.setRequestAttributes(new ServletRequestAttributes(mockRequest));
    }

    @AfterEach
    void tearDown() {
        RequestContextHolder.resetRequestAttributes();
    }

    @Resource
    private IFeignOpenSystemApiController feignOpenSystemApiController;

    @Test
    public void editBoxFace() {
//        String url = "/Users/<USER>/Downloads/Snipaste_2025-03-31_09-21-48.png";
        VlinkerAlgorithmEditBoxFaceReq req = new VlinkerAlgorithmEditBoxFaceReq();
        req.setBoxId(1902245258125668353L);
        req.setName("aaaa");
        // 读取图片的base64
        //转换流
//        BufferedImage read = null;
//        try {
//            read = ImageIO.read(new File(url));
//        } catch (IOException e) {
//            log.error("读取图片失败", e);
//            return;
//        }
//        //转换base64
//        String jpeg = ImgUtil.toBase64(read, ImgUtil.IMAGE_TYPE_PNG);
        String url = "https://test-vlinker-minio-api.sdvideo.cn:48801/converge-alarm/alarm/K02690659/20250331/1906512187727712258.jpg";
        req.setFaceImgData(url);
        req.setFaceImgType(1);
        req.setPhoneNumber("123");
        req.setIdentityCard("1234");

        DtoResult<Integer> dtoResult = feignOpenSystemApiController.editBoxFace(req);
        System.out.println(dtoResult);
        if (dtoResult.success()) {
//            VlinkerAlgorithmEditBoxFaceReq req1 = new VlinkerAlgorithmEditBoxFaceReq();
//            req1.setBoxId(1902245258125668353L);
//            req1.setFaceId(dtoResult.getData());
//            DtoResult<Integer> dtoResult2 = feignOpenSystemApiController.delBoxFace(req1);
//            System.out.println(dtoResult2);
        }
    }
}
