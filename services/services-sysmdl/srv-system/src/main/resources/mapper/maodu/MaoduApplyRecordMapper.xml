<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.saida.services.system.maodu.mapper.MaoduApplyRecordMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.saida.services.system.maodu.pojo.entity.MaoduApplyRecordEntity" id="maoduApplyRecordMap">
        <result property="id" column="id"/>
        <result property="orgId" column="org_id"/>
        <result property="appScene" column="app_scene"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="carType" column="car_type"/>
        <result property="carNum" column="car_num"/>
        <result property="ownerName" column="owner_name"/>
        <result property="phone" column="phone"/>
        <result property="applyStatus" column="apply_status"/>
        <result property="applyTime" column="apply_time"/>
        <result property="applyMemo" column="apply_memo"/>
        <result property="checkTime" column="check_time"/>
        <result property="checkStatus" column="check_status"/>
        <result property="checkMemo" column="check_memo"/>
        <result property="checkUser" column="check_user"/>
    </resultMap>

    <select id="getInfo" resultType="com.saida.services.system.maodu.pojo.entity.MaoduApplyRecordEntity">
        select *
        from maodu_apply_record
        where id = #{id}
    </select>

    <select id="listPage" resultType="com.saida.services.system.maodu.pojo.entity.MaoduApplyRecordEntity">
        select t.* from maodu_apply_record t
        <where>
            <if test="entity.carNum !=null and entity.carNum !=''">
                AND t.car_num LIKE CONCAT('%',#{entity.carNum},'%')
            </if>
            <if test="entity.ownerName !=null and entity.ownerName !=''">
                AND t.owner_name LIKE CONCAT('%',#{entity.ownerName},'%')
            </if>
            <if test="entity.carType !=null and entity.carType !=''">
                AND t.car_type  = #{entity.carType}
            </if>
            <if test="entity.applyStatus !=null and entity.applyStatus !=''">
                AND t.apply_status  = #{entity.applyStatus}
            </if>
            <if test="entity.orgId != null and entity.orgId != ''">
                and t.org_id = #{entity.orgId}
            </if>
            <if test="entity.appScene != null and entity.appScene != ''">
                and t.app_scene = #{entity.appScene}
            </if>
            <if test="entity.createBy != null and entity.createBy != ''">
                and t.create_by = #{entity.createBy}
            </if>
            <if test="entity.startDate != null and entity.startDate != ''">
                and t.apply_time &gt;= #{entity.startDate}
            </if>
            <if test="entity.endDate != null and entity.endDate != ''">
                and t.apply_time &lt;= #{entity.endDate}
            </if>
            <if test="entity.applyStatuss != null and entity.applyStatuss.length > 0">
                and t.apply_status in
                <foreach collection="entity.applyStatuss" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order By t.create_time desc
    </select>
    <select id="list" resultType="com.saida.services.system.maodu.pojo.entity.MaoduApplyRecordEntity">
        select t.* from maodu_apply_record t
        <where>

        </where>
        order By t.update_time desc
    </select>
    <select id="countByStatus" resultType="com.saida.services.system.maodu.pojo.dto.ColValue">
        select count(1) `value`, IFNULL(SUM(CASE WHEN t.apply_status = 10001200004  THEN 1 ELSE 0 END),0) value1 ,
               IFNULL(SUM(CASE WHEN t.apply_status = 10001200005  THEN 1 ELSE 0 END),0) value2 from maodu_apply_record t
        <where>
            and DATE_FORMAT( t.create_time, '%Y-%m-%d' ) = DATE_FORMAT(NOW(), '%Y-%m-%d')
            <if test="entity.carNum !=null and entity.carNum !=''">
                AND t.car_num LIKE CONCAT('%',#{entity.carNum},'%')
            </if>
            <if test="entity.ownerName !=null and entity.ownerName !=''">
                AND t.owner_name LIKE CONCAT('%',#{entity.ownerName},'%')
            </if>
            <if test="entity.carType !=null and entity.carType !=''">
                AND t.car_type  = #{entity.carType}
            </if>
            <if test="entity.applyStatus !=null and entity.applyStatus !=''">
                AND t.apply_status  = #{entity.applyStatus}
            </if>
            <if test="entity.orgId != null and entity.orgId != ''">
                and t.org_id = #{entity.orgId}
            </if>
            <if test="entity.appScene != null and entity.appScene != ''">
                and t.app_scene = #{entity.appScene}
            </if>
            <if test="entity.createBy != null and entity.createBy != ''">
                and t.create_by = #{entity.createBy}
            </if>
        </where>
        order By t.create_time desc
    </select>

</mapper>