<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.saida.services.system.maodu.mapper.MaoduCarpassRecordMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.saida.services.system.maodu.pojo.entity.MaoduCarpassRecordEntity" id="maoduCarpassRecordMap">
        <result property="id" column="id"/>
        <result property="orgId" column="org_id"/>
        <result property="appScene" column="app_scene"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="siteCode" column="site_code"/>
        <result property="orderNo" column="order_no"/>
        <result property="carNo" column="car_no"/>
        <result property="inType" column="in_type"/>
        <result property="inChannel" column="in_channel"/>
        <result property="inTime" column="in_time"/>
        <result property="outChannel" column="out_channel"/>
        <result property="outTime" column="out_time"/>
        <result property="carModel" column="car_model"/>
        <result property="carType" column="car_type"/>
        <result property="inPic" column="in_pic"/>
        <result property="outPic" column="out_pic"/>
        <result property="parkingDuration" column="parking_duration"/>
    </resultMap>

    <select id="getInfo" resultType="com.saida.services.system.maodu.pojo.entity.MaoduCarpassRecordEntity">
        select *
        from maodu_carpass_record
        where id = #{id}
    </select>

    <select id="listPage" resultType="com.saida.services.system.maodu.pojo.entity.MaoduCarpassRecordEntity">
        select t.*,g.channel_name gateName from maodu_carpass_record t
        left join sys_org o on t.org_id = o.id
        left join maodu_gate g on t.gate_id = g.id
        <where>
            <if test="entity.orderNo !=null and entity.orderNo !=''">
                AND t.order_no LIKE CONCAT('%',#{entity.orderNo},'%')
            </if>
            <if test="entity.carNo !=null and entity.carNo !=''">
                AND t.car_no LIKE CONCAT('%',#{entity.carNo},'%')
            </if>
            <if test="entity.orgId != null and entity.orgId != ''">
                and t.org_id = #{entity.orgId}
            </if>
            <if test="entity.startInTime != null and entity.startInTime != ''">
                and t.in_time &gt;= #{entity.startInTime}
            </if>
            <if test="entity.endInTime != null and entity.endInTime != ''">
                and t.in_time &lt;= #{entity.endInTime}
            </if>
            <if test="entity.startOutTime != null and entity.startOutTime != ''">
                and t.out_time &gt;= #{entity.startOutTime}
            </if>
            <if test="entity.endOutTime != null and entity.endOutTime != ''">
                and t.out_time &lt;= #{entity.endOutTime}
            </if>
            <if test="entity.inType != null and entity.inType != ''">
                and t.in_type = #{entity.inType}
            </if>
            <if test="entity.appScene != null and entity.appScene != ''">
                and t.app_scene = #{entity.appScene}
            </if>
            <if test="entity.orgIdChain != null and entity.orgIdChain != ''">
                and o.id_chain LIKE concat(#{entity.orgIdChain}, '%')
            </if>
        </where>
        order By t.update_time desc
    </select>
    <select id="list" resultType="com.saida.services.system.maodu.pojo.entity.MaoduCarpassRecordEntity">
        select t.* from maodu_carpass_record t
        <where>

        </where>
        order By t.update_time desc
    </select>


</mapper>