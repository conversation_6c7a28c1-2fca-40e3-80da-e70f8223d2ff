<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.saida.services.system.maodu.mapper.MaoduUserMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.saida.services.system.maodu.pojo.entity.MaoduUserEntity" id="maoduUserMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="head" column="head"/>
        <result property="phone" column="phone"/>
        <result property="sex" column="sex"/>
        <result property="orgId" column="org_id"/>
        <result property="appScene" column="app_scene"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="certificateType" column="certificate_type"/>
        <result property="certificateNum" column="certificate_num"/>
    </resultMap>

    <select id="getInfo" resultType="com.saida.services.system.maodu.pojo.entity.MaoduUserEntity">
        select *
        from maodu_user
        where id = #{id}
    </select>

    <select id="listPage" resultType="com.saida.services.system.maodu.pojo.entity.MaoduUserEntity">
        select t.* from maodu_user t
        left join sys_org o on t.org_id = o.id
        <where>
            <if test="entity.name !=null and entity.name !=''">
                AND t.name LIKE CONCAT('%',#{entity.name},'%')
            </if>
            <if test="entity.phone !=null and entity.phone !=''">
                AND t.phone LIKE CONCAT('%',#{entity.phone},'%')
            </if>
            <if test="entity.orgId != null and entity.orgId != ''">
                and t.org_id = #{entity.orgId}
            </if>
            <if test="entity.appScene != null and entity.appScene != ''">
                and t.app_scene = #{entity.appScene}
            </if>
            <if test="entity.orgIdChain != null and entity.orgIdChain != ''">
                and o.id_chain LIKE concat(#{entity.orgIdChain}, '%')
            </if>
            <if test="entity.certificateNum !=null and entity.certificateNum !=''">
                AND t.certificate_num LIKE CONCAT('%',#{entity.certificateNum},'%')
            </if>
        </where>
        order By t.update_time desc
    </select>
    <select id="list" resultType="com.saida.services.system.maodu.pojo.entity.MaoduUserEntity">
        select t.* from maodu_user t
        <where>

        </where>
        order By t.update_time desc
    </select>

</mapper>