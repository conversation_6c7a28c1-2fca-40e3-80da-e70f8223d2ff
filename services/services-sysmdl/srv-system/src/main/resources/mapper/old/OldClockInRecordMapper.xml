<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.saida.services.system.old.mapper.OldClockInRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.saida.services.system.old.pojo.entity.OldClockInRecord">
        <id column="id" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="enterprise_id" property="enterpriseId" />
        <result column="org_id" property="orgId" />
        <result column="old_person_id" property="oldPersonId" />
        <result column="img_url" property="imgUrl" />
        <result column="clock_in_status" property="clockInStatus" />
        <result column="is_delete" property="isDelete" />
        <result column="app_scene" property="appScene" />
        <result column="remark" property="remark" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
    </resultMap>
    <select id="listPage" resultType="com.saida.services.system.old.pojo.vo.OldClockInRecordVO">
        select
            t.id,
            t.org_id,
            o.name org_name,
            t.old_person_id,
            op.name old_person_name,
            t.clock_in_status,
            t.img_url,
            t.create_time
        from old_clock_in_record t
        left join old_person_info op on op.id = t.old_person_id
        left join sys_org o on o.id = t.org_id
        <where>
            t.is_delete = 0
            <if test="dto.appScene != null">
                AND t.app_scene = #{dto.appScene}
            </if>

            <if test="dto.createBy != null">
                AND t.create_by = #{dto.createBy}
            </if>
            <if test="dto.oldPersonName != null and dto.oldPersonName != ''">
                and op.name like concat('%',#{dto.oldPersonName},'%')
            </if>
            <if test="dto.startTime != null">
                and t.alarm_time >= #{dto.startTime}
            </if>
            <if test="dto.endTime != null">
                and t.alarm_time &lt;= #{dto.endTime}
            </if>
            <if test="dto.includeChildOrg == false and dto.orgId != null  ">
                AND t.org_id = #{dto.orgId}
            </if>
            <if test="dto.includeChildOrg == true and dto.orgIds != null and dto.orgIds.size() > 0">
                and t.org_id in
                <foreach collection="dto.orgIds" item="orgId" open="(" separator="," close=")">
                    #{orgId}
                </foreach>
            </if>
        </where>
    </select>

</mapper>
