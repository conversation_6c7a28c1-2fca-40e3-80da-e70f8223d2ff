<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.saida.services.system.illegal.mapper.IllegalRuleMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.saida.services.system.illegal.entity.IllegalRuleEntity" id="illegalRuleMap">
        <result property="id" column="id"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="type" column="type"/>
        <result property="overloadCount" column="overload_count"/>
        <result property="tricycleCount" column="tricycle_count"/>
        <result property="helmetCount" column="helmet_count"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="orgId" column="org_id"/>
        <result property="appScene" column="app_scene"/>
    </resultMap>
</mapper>