<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.saida.services.system.sys.mapper.UserRoleMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.saida.services.system.sys.entity.UserRoleEntity" id="userRoleMap">
        <result property="id" column="id"/>
        <result property="uid" column="uid"/>
        <result property="rid" column="rid"/>
    </resultMap>

    <select id="getUserRolePage" resultType="com.saida.services.system.sys.dto.UserRoleDto">
        SELECT
        ur.*, r.name as roleName, u.name as userName, u.phone as userPhone
        FROM
        sys_user_role ur
        LEFT JOIN sys_role r ON r.id = ur.rid
        LEFT JOIN sys_user u ON u.id = ur.uid
        <where>
            <if test="param.orgId != null">
                AND r.org_id = #{param.orgId}
            </if>
            <if test="param.uid != null">
                AND ur.uid = #{param.uid}
            </if>
            <if test="param.roleId != null">
                AND ur.rid = #{param.roleId}
            </if>
            <if test="param.roleName != null and param.roleName != ''">
                AND r.name = #{param.roleName}
            </if>
            <if test="param.userName != null and param.userName != ''">
                AND u.name = #{param.userName}
            </if>
            <if test="param.uids != null and param.uids.size() > 0">
                AND ur.uid in
                <foreach item="item" index="index" collection="param.uids" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="param.excludeRoleName != null and param.excludeRoleName.size() > 0">
                AND r.name not in
                <foreach item="item" index="index" collection="param.excludeRoleName" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>



    <select id="getUserRoleList" resultType="com.saida.services.system.sys.dto.UserRoleDto">
        SELECT
        ur.*, r.name as roleName, u.name as userName, u.phone as userPhone
        FROM
        sys_user_role ur
        LEFT JOIN sys_role r ON r.id = ur.rid
        LEFT JOIN sys_user u ON u.id = ur.uid
        <where>
            <if test="param.orgId != null">
                AND r.org_id = #{param.orgId}
            </if>
            <if test="param.orgIdSet != null and param.orgIdSet.size() > 0">
                AND r.org_id in
                <foreach item="item" index="index" collection="param.orgIdSet" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="param.uid != null">
                AND ur.uid = #{param.uid}
            </if>
            <if test="param.roleId != null">
                AND ur.rid = #{param.roleId}
            </if>
            <if test="param.roleName != null and param.roleName != ''">
                AND r.name = #{param.roleName}
            </if>
            <if test="param.userName != null and param.userName != ''">
                AND u.name = #{param.userName}
            </if>
            <if test="param.uids != null and param.uids.size() > 0">
                AND ur.uid in
                <foreach item="item" index="index" collection="param.uids" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="param.excludeRoleName != null and param.excludeRoleName.size() > 0">
                AND r.name not in
                <foreach item="item" index="index" collection="param.excludeRoleName" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
    <select id="getCountByOrgUrl" resultType="java.lang.Integer">
        SELECT
            count(*)
        FROM
            sys_user su
                LEFT JOIN sys_user_role sur on su.id = sur.uid
                LEFT JOIN sys_role_permission srp on sur.rid = srp.rid
                LEFT JOIN sys_permission sp on srp.pid = sp.id
        WHERE
            su.org_id = #{orgId}
          AND su.type = 10000700002
        and LOWER(sp.url) = LOWER(#{url})
          and sp.app_scene = #{appScene}
    </select>

</mapper>