<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.saida.services.system.visual.mapper.VisualDeviceAuthMapper">

    <resultMap type="com.saida.services.srv.entity.VisualDeviceAuthEntity" id="VisualDeviceAuthResult">
        <result property="id" column="id"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="platformName" column="platform_name"/>
        <result property="platformType" column="platform_type"/>
        <result property="appKey" column="app_key"/>
        <result property="appSecret" column="app_secret"/>
        <result property="host" column="host"/>
        <result property="componentName" column="component_name"/>
        <result property="isEnable" column="is_enable"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectVisualDeviceAuthVo">
        select * from visual_device_auth
    </sql>

    <select id="selectVisualDeviceAuthList" parameterType="com.saida.services.system.visual.dto.DeviceAuthPageListDto" resultMap="VisualDeviceAuthResult">
        <include refid="selectVisualDeviceAuthVo"/>
        <where>
            <if test="dto.platformName != null and dto.platformName != ''"> and platform_name like concat('%', #{dto.platformName}, '%')</if>
            <if test="dto.platformType != null"> and platform_type = #{dto.platformType}</if>
            <if test="dto.appKey != null and dto.appKey != ''"> and app_key = #{dto.appKey}</if>
            <if test="dto.appSecret != null and dto.appSecret != ''"> and app_secret = #{dto.appSecret}</if>
            <if test="dto.host != null and dto.host != ''"> and host = #{dto.host}</if>
            <if test="dto.componentName != null and dto.componentName != ''"> and component_name = #{dto.componentName}</if>
            <if test="dto.isEnable != null"> and is_enable = #{dto.isEnable}</if>
            <if test="dto.createBy != null"> and create_by = #{dto.createBy}</if>
            <if test="dto.orgIdSet != null and dto.orgIdSet.size() > 0">
                AND org_id in
                <foreach item="orgId" index="index" collection="dto.orgIdSet" open="(" separator="," close=")">
                    #{orgId}
                </foreach>
            </if>
        </where>
        order by create_time desc
    </select>


    <delete id="deleteVisualDeviceAuthByIds" parameterType="String">
        delete from visual_device_auth where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>