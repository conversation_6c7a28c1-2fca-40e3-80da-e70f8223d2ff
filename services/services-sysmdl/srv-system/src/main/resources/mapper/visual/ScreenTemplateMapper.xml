<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.saida.services.system.visual.mapper.ScreenTemplateMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.saida.services.system.visual.entity.ScreenTemplateEntity" id="screenTemplateMap">
        <result property="id" column="id"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="name" column="name"/>
        <result property="img" column="img"/>
        <result property="assemblyId" column="assembly_id"/>
        <result property="sceneId" column="scene_id"/>
        <result property="title" column="title"/>
    </resultMap>

    <sql id="base_sql">
        select
            t.*
        from
            screen_template t
        <where>
            <if test="entity.id != null">
                AND t.id = #{entity.id}
            </if>
            <if test="entity.sceneId != null">
                AND t.scene_id = #{entity.sceneId}
            </if>
            <if test="entity.sceneIdSet != null and entity.sceneIdSet.size() > 0">
                AND t.scene_id in
                <foreach item="sceneId" collection="entity.sceneIdSet" open="(" separator="," close=")">
                    #{sceneId}
                </foreach>
            </if>
            <if test="entity.title != null and entity.title != ''">
                AND t.title LIKE CONCAT('%', #{entity.title}, '%')
            </if>
            <if test="entity.name != null and entity.name != ''">
                AND t.name LIKE CONCAT('%', #{entity.name}, '%')
            </if>
            <if test="entity.type != null">
                AND t.type = #{entity.type}
            </if>
            <if test="entity.defaultTemplate != null">
                AND t.default_template = #{entity.defaultTemplate}
            </if>
        </where>
    </sql>

    <select id="getList" resultType="com.saida.services.system.visual.entity.ScreenTemplateEntity">
        <include refid="base_sql"/>
    </select>


    <select id="getInfoById" resultType="com.saida.services.system.visual.entity.ScreenTemplateEntity">
        <include refid="base_sql"/>
    </select>
</mapper>