<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.saida.services.system.community.mapper.CarInfoMapper">


    <sql id="base_sql">
        select t.*,c.name as communityName
        from car_info t
        left join community_info c on t.community_id = c.id
        where t.is_delete = 0
        <if test="entity.id != null and entity.id != ''">
            and t.id = #{entity.id}
        </if>
        <if test="entity.plate != null and entity.plate != ''">
            AND t.plate like CONCAT('%', #{entity.plate}, '%')
        </if>
        <if test="entity.carType != null ">
            AND t.car_type = #{entity.carType}
        </if>
        <if test="entity.communityId != null ">
            AND t.community_id = #{entity.communityId}
        </if>
        <if test="entity.carVariety != null ">
            AND t.car_variety = #{entity.carVariety}
        </if>
        <if test="entity.orgId != null and entity.orgId != ''">
            and t.org_id = #{entity.orgId}
        </if>
        order by t.create_time desc
    </sql>

    <select id="listPage" resultType="com.saida.services.system.community.entity.CarInfoEntity">
        <include refid="base_sql"/>
    </select>


    <select id="getInfoById" resultType="com.saida.services.system.community.entity.CarInfoEntity">
        <include refid="base_sql"/>
    </select>

    <select id="updateIsDeleteByIds" >
        update car_info
        set is_delete = 1
        where FIND_IN_SET(#{ids}, id)
    </select>


    <select id="getListByIds" resultType="com.saida.services.system.community.entity.CarInfoEntity">
        select * from car_info t
        where id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>


    <select id="getListByDeviceTreeId" resultType="com.saida.services.system.community.entity.CarInfoEntity">
        select i.*
        from car_info i
        left join community_info c on i.community_id = c.id
        where c.visual_device_tree_id = #{deviceTreeId}
    </select>

</mapper>