package com.saida.services.system.visual.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.saida.services.common.entity.BaseEntity;
import lombok.Data;

/**
 * 设备快照
 *
 * <AUTHOR>
 * email ${email}
 * date 2023-09-28 14:49:28
 */
@Data
@TableName("device_gallery")
public class DeviceGalleryEntity extends BaseEntity {
    private static final long serialVersionUID = 1L;

    private Long deviceId;

    private String imgUrl;

}
