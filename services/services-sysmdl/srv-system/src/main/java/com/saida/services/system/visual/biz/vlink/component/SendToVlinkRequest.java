package com.saida.services.system.visual.biz.vlink.component;

import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.Method;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.saida.services.common.tools.RedisUtil;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.srv.entity.VisualDeviceAuthEntity;
import com.saida.services.system.visual.biz.vlink.enums.VlinkCodeEnum;
import com.saida.services.system.visual.biz.vlink.enums.VlinkRequestEnum;
import com.saida.services.system.visual.biz.vlink.exception.UnauthorizedException;
import com.saida.services.system.visual.biz.vlink.pojo.req.VlinkLoginReq;
import com.saida.services.system.visual.biz.vlink.pojo.resp.VlinkResp;
import com.saida.services.system.visual.biz.vlink.service.VlinkService;
import com.saida.services.system.visual.biz.vlink.service.impl.VlinkServiceImpl;
import com.saida.services.system.visual.req.CommonBaseReq;
import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.springframework.http.HttpMethod;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.security.Security;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * Vlink视频平台请求工具类
 */
@Slf4j
@Component
public class SendToVlinkRequest {

    @Resource
    private VlinkService vlinkService;
    @Resource
    private RedisUtil redisUtil;

    private static final String tokenParam = "Authorization";

    @Retryable(value = UnauthorizedException.class, maxAttempts = 1, backoff = @Backoff(delay = 100))
    public String sendRequest(VlinkRequestEnum vlinkRequestEnum, String path, CommonBaseReq commonBaseReq, VisualDeviceAuthEntity visualDeviceAuthEntity) throws Exception {
        log.info("==================================== 请求V-LINKER能力开放平台接口开始 ====================================");
        String resp = null;
        if (StringUtil.isEmpty(path)) {
            path = vlinkRequestEnum.getPath();
        }
        String url = visualDeviceAuthEntity.getHost() + path;
        Object authToken = redisUtil.get(VlinkServiceImpl.authRedisKey + visualDeviceAuthEntity.getId());
        if (null == authToken) {
            log.error("Redis中V-LINKER能力开放平台token为空！");
            throw new UnauthorizedException("Redis中V-LINKER能力开放平台token为空！");
        }
        try {
            // 放入token
            long timestamp = System.currentTimeMillis();
            String jsonString = JSON.toJSONString(commonBaseReq, SerializerFeature.MapSortField);
            String md5 = SecureUtil.md5(jsonString + timestamp);
            // String signature = encrypt(md5, visualDeviceAuthEntity.getAesKey(), String.format("%016d", timestamp));
            Map<String, String> headers = new HashMap<>();
            headers.put("timestamp", String.valueOf(timestamp));
            headers.put("signature", visualDeviceAuthEntity.getAppKey());
            headers.put(tokenParam, authToken.toString());
            log.info("请求V-LINKER能力开放平台接口开始...url={}, md5={},header={}, req={}", url, md5, headers, jsonString);
            HttpRequest httpRequest = HttpRequest.of(url);
            httpRequest.addHeaders(headers);
            // get方法
            if (HttpMethod.GET.matches(vlinkRequestEnum.getMethod())) {
                httpRequest.form(jsonString);
                httpRequest.setMethod(Method.GET);

            } else if (HttpMethod.POST.matches(vlinkRequestEnum.getMethod())) {
                httpRequest.body(jsonString);
                httpRequest.setMethod(Method.POST);
            } else {
                httpRequest.body(jsonString);
                httpRequest.setMethod(Method.POST);
            }
            try (HttpResponse httpResponse = httpRequest.execute()) {
                resp = httpResponse.body();
            }
            log.info("请求V-LINKER能力开放平台接口结束...url={}, md5={}, header={}, req={} , resp={}", url, md5, headers, jsonString, resp);
            VlinkResp vlinkResp = JSON.parseObject(resp, VlinkResp.class);
            // 用户已过期或者未授权
            if (Objects.equals(vlinkResp.getCode(), VlinkCodeEnum.TOKEN_INVALID_OR_EXPIRED.getCode())) {
                log.error("token无效或已过期，msg={}", vlinkResp.getMessage());
                throw new UnauthorizedException(vlinkResp.getMessage());
            }
        } catch (UnauthorizedException e1) {
            log.info("请求V-LINKER能力开放平台接口出错，用户已过期或者访问未授权...url={}, req={}, resp={}, msg={}", url, commonBaseReq, resp, e1.getMessage());
            throw new UnauthorizedException(resp);
        } catch (Exception e) {
            log.info("请求V-LINKER能力开放平台接口出错...url={}, req={}, resp={}, msg={}", url, commonBaseReq, resp, e.getMessage());
            return resp;
        } finally {
            log.info("==================================== 请求V-LINKER能力开放平台接口结束 ====================================");
        }
        return resp;
    }

    @Recover
    public String sendRequestRetry(UnauthorizedException e, VlinkRequestEnum vlinkRequestEnum, String path, CommonBaseReq commonBaseReq, VisualDeviceAuthEntity visualDeviceAuthEntity) throws Exception {
        log.error("调用登录接口重新获取授权！");
        VlinkLoginReq vlinkLoginReq = this.getReq(visualDeviceAuthEntity);
        vlinkService.login(vlinkLoginReq, visualDeviceAuthEntity);
        return this.sendRequest(vlinkRequestEnum, path, commonBaseReq, visualDeviceAuthEntity);
    }

    static {
        // 添加Bouncy Castle作为加密提供程序
        Security.addProvider(new BouncyCastleProvider());
    }

    private static final Map<String, Cipher> cipherMap = new HashMap<>();
    private static final Map<String, SecretKeySpec> secretKeySpecMap = new HashMap<>();

    /**
     * 加密
     *
     * @param plaintext 明文
     * @param key       密钥
     * @param iv        偏移量
     */
    public static String encrypt(String plaintext, String key, String iv) {
        try {
            Cipher cipher = cipherMap.get(key);
            if (cipher == null) {
                cipher = Cipher.getInstance("AES/CBC/PKCS7Padding", "BC");
                secretKeySpecMap.put(key, new SecretKeySpec(key.getBytes(), "AES"));
                cipherMap.put(key, cipher);
            }
            IvParameterSpec ivParameterSpec = new IvParameterSpec(iv.getBytes());
            cipher.init(Cipher.ENCRYPT_MODE, secretKeySpecMap.get(key), ivParameterSpec);
            byte[] encryptedBytes = cipher.doFinal(plaintext.getBytes());
            return Base64.getEncoder().encodeToString(encryptedBytes);
        } catch (Exception e) {
            log.error("加密失败", e);
        }
        return null;
    }

    public String doLogin(VlinkLoginReq vlinkLoginReq, VisualDeviceAuthEntity entity) {
        long timestamp = System.currentTimeMillis();
        String url = entity.getHost() + VlinkRequestEnum.LOGIN.getPath();
        Map<String, Object> param = new HashMap<>();
        param.put("appKey", vlinkLoginReq.getAppKey());
        param.put("secretKey", vlinkLoginReq.getSecretKey());
        String jsonString = JSON.toJSONString(param, SerializerFeature.MapSortField);
        String md5 = SecureUtil.md5(jsonString + timestamp);
        String signature = encrypt(md5, vlinkLoginReq.getAesKey(), String.format("%016d", timestamp));
        HttpResponse execute = HttpRequest.post(url)
                .header("timestamp", String.valueOf(timestamp))
                .header("signature", signature)
                .body(jsonString)
                .execute();
        String body = execute.body();
        log.info("vlink视频登录接口开始...url={}, req={}, resp={}", url, JSON.toJSON(param), body);
        return body;
    }

    private VlinkLoginReq getReq(VisualDeviceAuthEntity visualDeviceAuthEntity) {
        VlinkLoginReq req = new VlinkLoginReq();
        req.setAppKey(visualDeviceAuthEntity.getAppKey());
        req.setSecretKey(visualDeviceAuthEntity.getAppSecret());
        req.setAesKey(visualDeviceAuthEntity.getAesKey());
        return req;
    }
}