package com.saida.services.system.visual.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.saida.services.system.visual.entity.EnterpriseSceneInfoEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 企业场景关联
 *
 * <AUTHOR>
 * email ${email}
 * date 2023-10-07 16:22:45
 */
@Mapper
public interface EnterpriseSceneInfoMapper extends BaseMapper<EnterpriseSceneInfoEntity> {


    IPage<EnterpriseSceneInfoEntity> listPage(@Param("page") IPage<EnterpriseSceneInfoEntity> page, @Param("entity") EnterpriseSceneInfoEntity entity);

        EnterpriseSceneInfoEntity getInfoById(@Param("entity") EnterpriseSceneInfoEntity entity);

}
