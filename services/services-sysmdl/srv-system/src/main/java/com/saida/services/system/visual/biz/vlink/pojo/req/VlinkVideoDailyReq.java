package com.saida.services.system.visual.biz.vlink.pojo.req;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * ClassName: TsingseeLoginReq <br/>
 * Description: <br/>
 * date: 2023/07/20 17:36<br/>
 *
 * <AUTHOR> />
 */
@Data
public class VlinkVideoDailyReq extends VlinkBaseTokenReq implements Serializable {
    private static final long serialVersionUID = 1L;

    /*
     * 设备SN码
     */
    @JSONField(name = "deviceId")
    private String deviceId;

    private String date;

    /*
     * 开始时间戳 13位
     */
    @JSONField(name = "start")
    private Long start;

    /*
     * 结束时间戳 13位
     */
    @JSONField(name = "end")
    private Long end;

    /*
     * 存储类型， 1 CLOUD: 云存录像;  2 LOCAL: 卡存录像
     */
    @JSONField(name = "source")
    private Integer source;

    private String ssrc;
}