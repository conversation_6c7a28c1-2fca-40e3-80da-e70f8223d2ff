package com.saida.services.system.sys.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.saida.services.common.base.Result;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.log.LogOperation;
import com.saida.services.log.LogOperationEnum;
import com.saida.services.log.ModuleEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.saida.services.system.sys.entity.AttributeDetailEntity;
import com.saida.services.system.sys.service.AttributeDetailService;

import javax.validation.Valid;
import java.util.List;


/**
 * 属性详情
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-09-25 09:12:40
 */
@RestController
@RequestMapping("sys/attributedetail")
public class AttributeDetailController {
    @Autowired
    private AttributeDetailService attributeDetailService;

    @PostMapping("save")
    @LogOperation(type = LogOperationEnum.ADDOREDIT, func = "字典选项信息", module = ModuleEnum.ATTRIBUTE)
    public Result save(@Valid AttributeDetailEntity entity){
        attributeDetailService.addOrUpdate(entity);
        return Result.ok();
    }

    @GetMapping("listPage")
    public Result listPage(AttributeDetailEntity entity){
        if(StringUtil.isEmpty(entity.getAttrType())){
            return Result.error("所属字典必传");
        }
        IPage<AttributeDetailEntity> page = attributeDetailService.listPage(entity);
        return Result.ok(page);
    }

    @GetMapping("getList")
    public Result getList(AttributeDetailEntity entity){
        if(StringUtil.isEmpty(entity.getAttrType())){
            return Result.error("所属字典必传");
        }
        List<AttributeDetailEntity> list = attributeDetailService.getList(entity);
        return Result.ok(list);
    }

    @GetMapping("getInfo")
    public Result getInfo(AttributeDetailEntity entity){
        if(entity.getId() == null){
            return Result.error("ID必传");
        }
        IPage<AttributeDetailEntity> page = attributeDetailService.listPage(entity);
        if(page == null || page.getRecords() == null || page.getRecords().isEmpty()){
            return Result.ok();
        }
        return Result.ok(page.getRecords().get(0));
    }

    @PostMapping("delete")
    @LogOperation(type = LogOperationEnum.DELETE, func = "删除字典选项信息", module = ModuleEnum.ATTRIBUTE)
    public Result delete(AttributeDetailEntity entity){
        if(entity.getId() == null){
            return Result.error("ID必传");
        }
        attributeDetailService.delete(entity.getId());
        return Result.ok();
    }
}
