package com.saida.services.system.visual.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.saida.services.common.entity.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@TableName("alarm_set")
public class AlarmSetEntity extends BaseEntity<AlarmSetEntity> implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long userId;

    private Long orgId;

    private Long appScene;

    private Integer modalEnabled;
    private Integer modalClose;
    private Integer modalCloseTime;
    private Integer soundEnabled;
    private Integer soundType;
    private Integer videoAlarmEnable;
    private Integer iotAlarmEnable;

    // ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

    @TableField(exist = false)
    private Long userType;
}