package com.saida.services.system.old.controller;

import com.saida.services.common.base.Result;
import com.saida.services.log.LogOperation;
import com.saida.services.log.LogOperationEnum;
import com.saida.services.log.ModuleEnum;
import com.saida.services.system.old.pojo.dto.OldVideoTurningEditDTO;
import com.saida.services.system.old.pojo.dto.OldVideoTurningSearchDTO;
import com.saida.services.system.old.service.OldVideoTurningService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 养老-视频轮巡设置 
 *
 * <AUTHOR>
 * @since 2025-04-01
 */
@RestController
@RequestMapping("old/videoTurning")
public class OldVideoTurningController {
    @Resource
    private OldVideoTurningService oldVideoTurningService;

    /**
     *养老-视频轮巡设置-详情
     *
     * @return com.saida.services.common.base.Result
     * <AUTHOR>
     */
    @GetMapping("info")
    @LogOperation(type = LogOperationEnum.QUERY, func = "视频轮巡设置详情", module = ModuleEnum.OLD)
    public Result info(OldVideoTurningSearchDTO dto) {
        return Result.ok(oldVideoTurningService.getInfo(dto));
    }

    /*
     * 养老-视频轮巡设置-编辑
     *
     * @return com.saida.services.common.base.Result
     * <AUTHOR>
     */
    @PostMapping("update")
    @LogOperation(type = LogOperationEnum.EDIT, func = "视频轮巡设置编辑", module = ModuleEnum.OLD)
    public Result update(@RequestBody @Valid OldVideoTurningEditDTO dto) {
        return Result.ok(oldVideoTurningService.save(dto));
    }


}
