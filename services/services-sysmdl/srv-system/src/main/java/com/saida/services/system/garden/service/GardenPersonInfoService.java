package com.saida.services.system.garden.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.saida.services.common.base.Result;
import com.saida.services.entities.base.BaseRequest;
import com.saida.services.system.garden.entity.GardenPersonInfoEntity;

import java.util.List;

/**
 * 人员信息
 *
 * <AUTHOR>
 * email ${email}
 * date 2024-7-8 09:54:50
 */
public interface GardenPersonInfoService extends IService<GardenPersonInfoEntity> {

    /**
     * 分页查询人员信息列表
     *
     * @param entity  人员信息实体类，包含查询条件
     * @param request 基础请求类，包含分页信息（页码和每页数量）
     * @return 分页查询结果，包含人员信息列表
     */
    Result listPage(GardenPersonInfoEntity entity, BaseRequest request);

    /**
     * 获取人员信息列表
     *
     * @param entity 人员信息实体类，包含查询条件
     * @return 返回一个包含人员信息实体的列表
     */
    List<GardenPersonInfoEntity> getList(GardenPersonInfoEntity entity);

    /**
     * 根据给定的ID获取学校人员信息实体类
     *
     * @param entity 包含人员信息ID的实体类对象
     * @return 返回包含人员信息的实体类对象，如果未找到则返回null
     */
    GardenPersonInfoEntity getInfoById(GardenPersonInfoEntity entity);

    /**
     * 保存或更新人员信息实体类
     *
     * @param entity 人员信息实体类对象，包含要保存或更新的人员信息
     * @return Result 操作结果，包含是否成功等信息
     */
    Result saveOrUpdateBean(GardenPersonInfoEntity entity);

    /**
     * 根据ID列表批量删除记录
     *
     * @param entity 包含要删除的ID列表的BaseRequest对象
     * @return Result 类型的操作结果，包含操作是否成功等信息
     */
    Result deleteByIds(BaseRequest entity);

    List<GardenPersonInfoEntity> getListByDeviceTreeId(Long deviceTreeId);


}

