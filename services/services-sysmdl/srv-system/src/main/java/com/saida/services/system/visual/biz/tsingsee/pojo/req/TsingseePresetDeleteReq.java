package com.saida.services.system.visual.biz.tsingsee.pojo.req;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * ClassName: TsingseeLoginReq <br/>
 * Description: <br/>
 * date: 2023/07/20 17:36<br/>
 *
 * <AUTHOR> />
 */
@Data
public class TsingseePresetDeleteReq extends TsingseeBaseTokenReq implements Serializable {
    private static final long serialVersionUID = 1L;

    /*
     * 通道id（备注:通道列表中 ChannelID 字段获取）
     */
    @JSONField(name = "channel")
    private String channel;

    /*
     * 预置位序列号
     */
    @JSONField(name = "presettoken")
    private Integer presettoken;

}