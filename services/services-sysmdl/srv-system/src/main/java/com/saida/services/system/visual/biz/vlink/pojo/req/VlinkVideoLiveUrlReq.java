package com.saida.services.system.visual.biz.vlink.pojo.req;

import lombok.Data;

import java.io.Serializable;

/**
 * ClassName: TsingseeLoginReq <br/>
 * Description: <br/>
 * date: 2023/07/20 17:36<br/>
 *
 * <AUTHOR> />
 */
@Data
public class VlinkVideoLiveUrlReq extends VlinkBaseTokenReq implements Serializable {
    private static final long serialVersionUID = 1L;

    /*
     * 设备编码
     */
    private String deviceId;

    /*
     * 视频直播流协议
     * 直播流协议：1-httpsflv；2-hls；3-rtmp；4-rtsp；5-wbrtc
     */
    private String protocolCode;

    /**
     * 流有效时间
     */
    private Integer activeSecond;

    /**
     * 1 主码流
     * 2 子码流
     */
    private Integer bitstream;
    /**
     * com.saida.services.system.pb.CommonEnum.BaseNetworkingProtocol
     * 安卓 quic 1
     * web ws 4
     */
    private Integer networking;

    /**
     *  是否开启AI 默认否
     */
    private Boolean openAi = false;
}