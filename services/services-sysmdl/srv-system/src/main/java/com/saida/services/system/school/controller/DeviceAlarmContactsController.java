package com.saida.services.system.school.controller;

import com.saida.services.common.base.Result;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.log.LogOperation;
import com.saida.services.log.LogOperationEnum;
import com.saida.services.log.ModuleEnum;
import com.saida.services.system.school.dto.DeviceAlarmContactsUpdateDto;
import com.saida.services.system.school.entity.DeviceAlarmContactsEntity;
import com.saida.services.system.school.service.DeviceAlarmContactsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 设备告警通知人
 *
 * <AUTHOR>
 * email ${email}
 * date 2024-7-8 09:54:50
 */
@RestController
@RequestMapping("deviceAlarmContacts")
public class DeviceAlarmContactsController {

    @Autowired
    private DeviceAlarmContactsService deviceAlarmContactsService;

    @GetMapping("getList")
    @LogOperation(type = LogOperationEnum.QUERY, func = "设备告警通知人列表", module = ModuleEnum.DEVICE_ALARM_CONTACTS)
    public Result getList(Long deviceId) {
        if (StringUtil.isEmpty(deviceId)) {
            return Result.error("设备ID不能为空");
        }
        List<DeviceAlarmContactsEntity> list = deviceAlarmContactsService.getList(deviceId);
        return Result.ok(list);
    }


    @PostMapping("saveOrUpdateList")
    @LogOperation(type = LogOperationEnum.EDIT, func = "设备告警通知人编辑", module = ModuleEnum.DEVICE_ALARM_CONTACTS)
    public Result saveOrUpdateList(@RequestBody DeviceAlarmContactsUpdateDto dto) {
        if (StringUtil.isEmpty(dto.getDeviceId())) {
            return Result.error("设备ID不能为空");
        }
        return deviceAlarmContactsService.saveOrUpdateList(dto);
    }

}
