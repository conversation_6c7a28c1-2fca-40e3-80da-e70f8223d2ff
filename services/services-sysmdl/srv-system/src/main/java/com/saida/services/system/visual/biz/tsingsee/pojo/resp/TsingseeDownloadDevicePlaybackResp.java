package com.saida.services.system.visual.biz.tsingsee.pojo.resp;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * ClassName: TsingseeCommonResp <br/>
 * Description: <br/>
 * date: 2023/07/20 17:40<br/>
 *
 * <AUTHOR> />
 */
@Data
public class TsingseeDownloadDevicePlaybackResp implements Serializable {
    private static final long serialVersionUID = 1L;

    @JSONField(name = "stream_id")
    private String streamId;

    @JSONField(name = "url")
    private String url;
}