package com.saida.services.system.maodu.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.saida.services.common.entity.BaseEntity;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 通行时间
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-03-17 13:58:44
 */
@Data
@TableName("maodu_carpass_record")
public class MaoduCarpassRecordEntity extends BaseEntity implements Serializable {
	private static final long serialVersionUID = 1L;


	/**
	 * 组织ID（企业ID）
	 */
	private Long orgId;


	/**
	 * 应用场景
	 */
	private Long appScene;


	/**
	 * 车场编码
	 */
	private String siteCode;


	/**
	 * 订单编号
	 */
	private String orderNo;


	/**
	 * 车牌号
	 */
	private String carNo;


	/**
	 * 进场类型
	 */
	private String inType;

	/**
	 * 出场类型
	 */
	private String outType;


	/**
	 * 进场通道
	 */
	private String inChannel;


	/**
	 * 进场时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private LocalDateTime inTime;


	/**
	 * 出场通道
	 */
	private String outChannel;


	/**
	 * 出场时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private LocalDateTime outTime;


	/**
	 * 车型
	 */
	private String carModel;



	/**
	 * 进场图片
	 */
	private String inPic;


	/**
	 * 出场图片
	 */
	private String outPic;


	/**
	 * 停车时长
	 */
	private String parkingDuration;

	//道闸id
	private String gateId;

	@TableField(exist = false)
	private Boolean subLevel = false;

	@TableField(exist = false)
	private String orgIdChain;

	@TableField(exist = false)
	private String gateName;

	@TableField(exist = false)
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private String startInTime;

	@TableField(exist = false)
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private String endInTime;

	@TableField(exist = false)
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private String startOutTime;

	@TableField(exist = false)
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private String endOutTime;


}
