package com.saida.services.system.community.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum EventStatusEnum {

    EVENT_STATUS_REPORT("event_status_report", "事件上报"),
    EVENT_STATUS_DISTRIBUTE("event_status_distribute", "事件分发"),
    EVENT_STATUS_HANDLE("event_status_handle", "事件处理"),
    EVENT_STATUS_NO_NEED("event_status_no_need", "无需处理"),
    EVENT_STATUS_CLOSE("event_status_close", "事件完结");

    public final String status;
    public final String msg;

}







