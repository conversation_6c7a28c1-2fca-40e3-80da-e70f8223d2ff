package com.saida.services.system.maodu.controller;

import com.saida.services.common.base.Result;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.entities.base.BaseRequest;
import com.saida.services.log.LogOperation;
import com.saida.services.log.LogOperationEnum;
import com.saida.services.log.ModuleEnum;
import com.saida.services.system.maodu.pojo.entity.MaoduUserpassRecordEntity;
import com.saida.services.system.maodu.service.MaoduUserpassRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 门禁记录
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-03-17 13:58:44
 */
@RestController
@RequestMapping("maodu/maoduuserpassrecord")
public class MaoduUserpassRecordController {
    @Autowired
    private MaoduUserpassRecordService iMaoduUserpassRecordService;


    /**
     * 门禁记录 - 分页列表
     */
    @GetMapping("listPage")
    @LogOperation(module = ModuleEnum.MaoduUserpassRecord, type = LogOperationEnum.QUERY, func = "门禁记录分页")
    public Result listPage(MaoduUserpassRecordEntity entity, BaseRequest baseRequest) {
        return iMaoduUserpassRecordService.listPage(entity,baseRequest);
    }



    /**
     * 门禁记录 -新增、修改
     */
    @PostMapping("save")
    @LogOperation(module = ModuleEnum.MaoduUserpassRecord, type = LogOperationEnum.ADDOREDIT, func = "门禁记录编辑")
    public Result save(@Valid MaoduUserpassRecordEntity entity) {
        return iMaoduUserpassRecordService.saveOrUpdateBean(entity);
    }
    /**
     * 门禁记录 - 详情
     */
    @GetMapping("getInfo")
    @LogOperation(module = ModuleEnum.MaoduUserpassRecord, type = LogOperationEnum.QUERY, func = "门禁记录详情")
    public Result getInfo(Long id) {
        if (null == id) {
            return Result.error("id不可为空");
        }
        return Result.ok(iMaoduUserpassRecordService.getInfo(id));
    }
    /**
    * 门禁记录 - 删除
    */
    @PostMapping("delete")
    @LogOperation(module = ModuleEnum.MaoduUserpassRecord, type = LogOperationEnum.DELETE, func = "门禁记录删除")
    public Result delete(String ids) {
        if (StringUtil.isEmpty(ids)){
            return Result.error("ids不可为空");
        }
        return iMaoduUserpassRecordService.delete(ids);
    }




}
