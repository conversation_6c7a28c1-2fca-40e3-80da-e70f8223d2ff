package com.saida.services.system.sys.dto;

import com.saida.services.entities.base.BaseRequest;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 服务套餐列表查询dto
 *
 * <AUTHOR>
 * @version PackageOrderCreateDto v1.0.0
 * @since 2025年1月20日14:17:05
 */
@Data
public class PackageOrderQryDto extends BaseRequest {
    /**
     * 是否是管理员
     */
    private Boolean adminFlag = false;
    /**
     * 登录用户
     */
    private Long userId;
    /**
     * 套餐类型：1-云存；2-提醒；3-AI服务
     */
    private Integer servicePackageType;
    /**
     * 时间选择：1-三个月内，2-半年内，3-一年内
     */
    private Integer dateType;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 账户
     */
    private String account;

    /**
     * 套餐id
     */
    private String servicePackageId;

    /**
     * 订单状态，0-待支付，1-已支付，99-已作废
     */
    private Integer orderStatus;

    /**
     * 订单类型，0-新购，1-续订（手动），2-续订（自动）
     */
    private Integer orderType;

    /**
     * 移动终端类型： 1-安卓；2-IOS；3-全部'
     */
    private Integer mobileTerminalType;

    /**
     * 支付日期-开始
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String startDate;

    /**
     * 支付日期-结束
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String endDate;

    /**
     * 过期标识:1-已过期，0-使用中
     */
    private Integer expireFlag;

    /**
     * 0-权益取消，1-权益下发
     */
    private Integer type;
}
