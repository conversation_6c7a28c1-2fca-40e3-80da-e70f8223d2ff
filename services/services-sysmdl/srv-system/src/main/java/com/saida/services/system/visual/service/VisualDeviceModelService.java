package com.saida.services.system.visual.service;

import com.saida.services.common.base.DtoResult;
import com.saida.services.common.entity.BasePageInfoEntity;
import com.saida.services.common.service.IBaseService;
import com.saida.services.converge.entity.DeviceModelVersionEntity;
import com.saida.services.entities.base.BaseRequest;
import com.saida.services.srv.entity.SrvVisualDeviceModelEntity;
import com.saida.services.srv.entity.SrvVisualParentDeviceEntity;

import java.util.List;
import java.util.Map;

public interface VisualDeviceModelService extends IBaseService<SrvVisualDeviceModelEntity> {

    DtoResult<BasePageInfoEntity<SrvVisualDeviceModelEntity>> listPage(SrvVisualDeviceModelEntity entity, BaseRequest baseRequest);

    DtoResult<SrvVisualDeviceModelEntity> getDeviceModelInfoByDeviceCodeCode(SrvVisualParentDeviceEntity srvVisualParentDeviceEntity);

    DtoResult<Void> edit(SrvVisualDeviceModelEntity entity);

    DtoResult<Void> delete(SrvVisualDeviceModelEntity entity);

    DtoResult<Void> syncDeviceModel();

    DtoResult<List<DeviceModelVersionEntity>> getDeviceModelVersionList(SrvVisualDeviceModelEntity entity);

    DtoResult<Map<String, Object>> getAllAlarmParameters();

    DtoResult<java.util.Map<String, Object>> getCapabilityParameters();
}