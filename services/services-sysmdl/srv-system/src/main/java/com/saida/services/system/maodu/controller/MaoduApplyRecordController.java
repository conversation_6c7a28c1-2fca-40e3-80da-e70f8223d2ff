package com.saida.services.system.maodu.controller;

import com.saida.services.common.base.Result;
import com.saida.services.entities.base.BaseRequest;
import com.saida.services.log.LogOperation;
import com.saida.services.log.LogOperationEnum;
import com.saida.services.log.ModuleEnum;
import com.saida.services.system.maodu.pojo.entity.MaoduApplyRecordEntity;
import com.saida.services.system.maodu.service.MaoduApplyRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * 入园管理
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-03-17 13:58:44
 */
@RestController
@RequestMapping("maodu/maoduapplyrecord")
public class MaoduApplyRecordController {
    @Autowired
    private MaoduApplyRecordService iMaoduApplyRecordService;

    /**
     * 入园管理 - 分页列表
     */
    @GetMapping("listPage")
    @LogOperation(module = ModuleEnum.MaoduApplyRecord, type = LogOperationEnum.QUERY, func = "入园管理分页")
    public Result listPage(MaoduApplyRecordEntity entity, BaseRequest baseRequest) {
        return iMaoduApplyRecordService.listPage(entity,baseRequest);
    }


    /**
     * 入园管理 -新增、修改
     */
    @PostMapping("saveBean")
    @LogOperation(module = ModuleEnum.MaoduApplyRecord, type = LogOperationEnum.ADDOREDIT, func = "入园管理编辑")
    public Result saveBean(@Valid MaoduApplyRecordEntity entity) {
        return iMaoduApplyRecordService.saveBean(entity);
    }


    /**
     * 入园管理 - 详情
     */
    @GetMapping("getInfo")
    @LogOperation(module = ModuleEnum.MaoduApplyRecord, type = LogOperationEnum.QUERY, func = "入园管理详情")
    public Result getInfo(Long id) {
        if (null == id) {
            return Result.error("id不可为空");
        }
        return Result.ok(iMaoduApplyRecordService.getInfo(id));
    }

    /**
     * 统计入园记录
     */
    @GetMapping("countByStatus")
    @LogOperation(module = ModuleEnum.MaoduApplyRecord, type = LogOperationEnum.QUERY, func = "根据状态统计入园记录")
    public Result countByStatus(MaoduApplyRecordEntity entity) {
        return iMaoduApplyRecordService.countByStatus(entity);
    }


    /**
     * 入园管理上传点位
     */
    @PostMapping("savePoint")
    @LogOperation(module = ModuleEnum.MaoduApplyRecord, type = LogOperationEnum.ADDOREDIT, func = "入园管理上传点位")
    public Result savePoint(MaoduApplyRecordEntity entity) {
        if (null == entity.getId()) {
            return Result.error("id不可为空");
        }
        return iMaoduApplyRecordService.savePoint(entity);
    }


}
