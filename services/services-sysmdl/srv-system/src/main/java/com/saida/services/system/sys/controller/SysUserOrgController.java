package com.saida.services.system.sys.controller;

import com.saida.services.common.base.DtoResult;
import com.saida.services.common.entity.BasePageInfoEntity;
import com.saida.services.common.tools.JwtUtil;
import com.saida.services.system.job.UpdateOrgRenewJob;
import com.saida.services.system.sys.entity.UserEntity;
import com.saida.services.system.sys.service.SysUserOrgService;
import com.xxl.job.core.biz.model.ReturnT;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


@RestController
@RequestMapping("/sys/user/org")
public class SysUserOrgController {

    @Resource
    private SysUserOrgService sysUserOrgService;
    @Resource
    private UpdateOrgRenewJob updateOrgRenewJob;

    /**
     * 刷存量数据
     */
    @PostMapping("/testUpdateOrgRenewJob")
    public DtoResult<ReturnT<String>> testUpdateOrgRenewJob() {
        return DtoResult.ok(updateOrgRenewJob.updateOrgRenewJob());
    }

    @GetMapping("/listPage")
    public DtoResult<BasePageInfoEntity<UserEntity>> listPage(UserEntity entity) {
        if (entity.getOrgId() == null) {
            entity.setOrgId(JwtUtil.getOrgId());
        }
        return sysUserOrgService.listPage(entity);
    }
}