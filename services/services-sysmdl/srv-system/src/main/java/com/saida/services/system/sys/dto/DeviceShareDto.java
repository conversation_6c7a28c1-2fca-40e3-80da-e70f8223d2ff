package com.saida.services.system.sys.dto;

import com.saida.services.entities.base.BaseRequest;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DeviceShareDto extends BaseRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    private List<DeviceShare> deviceShareList;


    @Data
    public static class DeviceShare {
        private Long userId;

        private String deviceCodeCode;

        private String deviceName;
        /**
         * 共享权限说明,1-只可查看，2-允许控制
         */
        private Long shareAuthority;

        private Long deviceId;

        private String userName;
    }
}