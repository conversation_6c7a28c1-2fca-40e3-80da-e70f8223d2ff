package com.saida.services.system.community.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.saida.services.system.community.entity.BuildingInfoEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 楼栋管理
 *
 * <AUTHOR>
 * email ${email}
 * date 2023-10-17 09:54:50
 */
@Mapper
public interface BuildingInfoMapper extends BaseMapper<BuildingInfoEntity> {


    List<BuildingInfoEntity> listPage(@Param("entity") BuildingInfoEntity entity);

    BuildingInfoEntity getInfoById(@Param("entity") BuildingInfoEntity entity);

    void updateIsDeleteByIds(@Param("ids") String ids);

    void updateIsDeleteByCommunityIds(@Param("ids") String ids);

}
