package com.saida.services.system.threshold.service;

import com.saida.services.common.base.Result;
import com.saida.services.common.service.IBaseService;
import com.saida.services.system.threshold.dto.DeviceThresholdUpdateDto;
import com.saida.services.system.threshold.entity.DeviceThresholdInfoEntity;

public interface DeviceThresholdInfoService extends IBaseService<DeviceThresholdInfoEntity> {


    Result updateDto(DeviceThresholdUpdateDto updateDto);
    Result saveOrUpdateNoiseThreshold(DeviceThresholdUpdateDto updateDto);
}
