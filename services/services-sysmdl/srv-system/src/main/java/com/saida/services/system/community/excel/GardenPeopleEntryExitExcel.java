package com.saida.services.system.community.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;

@Data
@HeadRowHeight(40)
@ColumnWidth(25)
public class GardenPeopleEntryExitExcel implements Serializable {
    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "出入编号", index = 0)
    private String id;

    @ExcelProperty(value = "人员姓名", index = 1)
    private String peopleName;

    @ExcelProperty(value = "人员类型", index = 2)
    private String peopleTypeName;

    @ExcelProperty(value = "联系方式", index = 3)
    private String linkPhone;

    @ExcelProperty(value = "所属企业", index = 4)
    private String gardenEnterpriseName;

    @ExcelProperty(value = "抓拍设备编码", index = 5)
    private String deviceCode;

    @ExcelProperty(value = "抓拍设备名称", index = 6)
    private String deviceName;

    @ExcelProperty(value = "抓拍时间", index = 7)
    private String entryExitTime;

    @ExcelProperty(value = "抓拍图片地址", index = 8)
    private String imgUrl;
}