package com.saida.services.system.old.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;


import com.saida.services.system.old.pojo.dto.OldPersonInfoSearchDTO;
import com.saida.services.system.old.pojo.entity.OldPersonInfo;
import com.saida.services.system.old.pojo.vo.OldPersonInfoVO;
import com.saida.services.system.old.pojo.vo.PersonCountByAgeVO;
import com.saida.services.system.old.pojo.vo.PersonCountByBedVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 养老-老人表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-26
 */
@Mapper
public interface OldPersonInfoMapper extends BaseMapper<OldPersonInfo> {

    List<OldPersonInfoVO> listPage(@Param("dto") OldPersonInfoSearchDTO dto);

    OldPersonInfoVO getInfoById(@Param("id")Long id);

    PersonCountByBedVO countByBed(@Param("dto") OldPersonInfoSearchDTO dto);

    PersonCountByAgeVO countByAge(@Param("dto") OldPersonInfoSearchDTO dto);
}
