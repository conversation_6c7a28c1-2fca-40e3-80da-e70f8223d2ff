package com.saida.services.system.visual.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.saida.services.common.base.Result;
import com.saida.services.system.visual.mapper.BroadcastTasksCycleMapper;
import com.saida.services.system.visual.service.BroadcastTasksCycleService;
import com.saida.services.system.visual.entity.BroadcastTasksCycleEntity;
import org.springframework.stereotype.Service;


@Service("broadcastTasksCycleService")
public class BroadcastTasksCycleServiceImpl extends ServiceImpl<BroadcastTasksCycleMapper, BroadcastTasksCycleEntity> implements BroadcastTasksCycleService {


    @Override
    public Result saveOrUpdateBean(BroadcastTasksCycleEntity entity) {
        super.saveOrUpdate(entity);
        return Result.ok();
    }

}