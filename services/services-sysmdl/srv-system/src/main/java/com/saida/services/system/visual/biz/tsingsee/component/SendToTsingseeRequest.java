package com.saida.services.system.visual.biz.tsingsee.component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.saida.services.common.tools.OkHttpUtil;
import com.saida.services.common.tools.RedisUtil;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.system.visual.biz.tsingsee.enums.TsingseeRequestEnum;
import com.saida.services.system.visual.biz.tsingsee.exception.UnauthorizedException;
import com.saida.services.system.visual.biz.tsingsee.pojo.req.TsingseeLoginReq;
import com.saida.services.system.visual.biz.tsingsee.pojo.resp.TsingseeLoginResp;
import com.saida.services.system.visual.biz.tsingsee.service.TsingseeService;
import com.saida.services.system.visual.biz.tsingsee.service.impl.TsingseeServiceImpl;
import com.saida.services.srv.entity.VisualDeviceAuthEntity;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.springframework.http.HttpMethod;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

/**
 * ClassName: SendRequestRetry <br/>
 * Description: <br/>
 * date: 2023/07/27 18:33<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@Component
public class SendToTsingseeRequest {

    @Resource
    private TsingseeService tsingseeService;
    @Resource
    private RedisUtil redisUtil;

    private static final String tokenParam = "token";

    @Retryable(value = UnauthorizedException.class, maxAttempts = 1, backoff = @Backoff(delay = 100))
    public String sendRequest(TsingseeRequestEnum tsingseeRequestEnum, String path, Map<String, Object> map, VisualDeviceAuthEntity visualDeviceAuthEntity) throws Exception {
        log.info("==================================== 请求青犀视频平台接口开始 ====================================");
        Response resp = null;
        String res = "";
        String formatResp = null;
        if (StringUtil.isEmpty(path)) {
            path = tsingseeRequestEnum.getPath();
        }
        String url = visualDeviceAuthEntity.getHost() + path;
        Object authObject = redisUtil.get(TsingseeServiceImpl.authRedisKey + visualDeviceAuthEntity.getId());
        if (null == authObject) {
            log.error("Redis中青犀视频平台token为空！");
            throw new UnauthorizedException("Redis中青犀视频平台token为空！");
        }
        try {
            // 放入token
            TsingseeLoginResp tsingseeLoginResp = JSONObject.parseObject(authObject.toString(), TsingseeLoginResp.class);
            map.put(tokenParam, tsingseeLoginResp.getToken());
            log.info("请求青犀视频平台接口开始...url={}, req={}", url, JSON.toJSON(map));
            // get方法
            if (HttpMethod.GET.matches(tsingseeRequestEnum.getMethod())) {
                resp = OkHttpUtil.doGet(url, map);
                res = new String(resp.body().bytes());
            } else if (HttpMethod.POST.matches(tsingseeRequestEnum.getMethod())) {
                resp = OkHttpUtil.doPostJson(url, null, map);
                res = new String(resp.body().bytes());
            }
            log.info("请求青犀视频平台接口结束...url={}, req={}, resp={}", url, JSON.toJSON(map), res);
            formatResp = this.formatResp(res);
            // 用户已过期或者未授权
            if ("用户已过期".equals(formatResp) || "Unauthorized".equals(formatResp)) {
                log.error("用户已过期或者访问未授权，调用登录接口重新获取授权！");
                throw new UnauthorizedException("用户已过期或者访问未授权！");
            }
        } catch (UnauthorizedException e1) {
            log.info("请求青犀视频平台接口出错，用户已过期或者访问未授权...url={}, req={}, resp={}, msg={}", url, JSON.toJSON(map), resp, e1.getMessage());
            throw new UnauthorizedException(formatResp);
        } catch (Exception e) {
            log.info("请求青犀视频平台接口出错...url={}, req={}, resp={}, msg={}", url, JSON.toJSON(map), resp, e.getMessage());
            return this.formatResp(res);
        } finally {
            log.info("==================================== 请求青犀视频平台接口结束 ====================================");
        }
        return formatResp;
    }

    @Recover
    public String sendRequestRetry(UnauthorizedException e, TsingseeRequestEnum tsingseeRequestEnum, String path, Map<String, Object> map, VisualDeviceAuthEntity visualDeviceAuthEntity) throws Exception {
        log.error("调用登录接口重新获取授权！");
        TsingseeLoginReq tsingseeLoginReq = this.getReq(visualDeviceAuthEntity);
        tsingseeService.login(tsingseeLoginReq, visualDeviceAuthEntity);
        return this.sendRequest(tsingseeRequestEnum, path, map, visualDeviceAuthEntity);
    }

    private TsingseeLoginReq getReq(VisualDeviceAuthEntity visualDeviceAuthEntity) {
        TsingseeLoginReq req = new TsingseeLoginReq();
        req.setUsername(visualDeviceAuthEntity.getAppKey());
        req.setPassword(visualDeviceAuthEntity.getAppSecret());
        return req;
    }

    /**
     * 去除前后双引号
     */
    private String formatResp(String resp) {
        if (!StringUtil.isEmpty(resp)) {
            if (resp.startsWith("\"") && resp.endsWith("\"")) {
                // 使用substring方法截取字符串的子串，去除首尾的双引号
                return resp.substring(1, resp.length() - 1);
            }
        }
        return resp;
    }
}