package com.saida.services.system.job;


import com.saida.services.common.base.DtoResult;
import com.saida.services.common.service.FileService;
import com.saida.services.common.tools.SpringUtil;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.entities.pojo.FileModel;
import com.saida.services.srv.entity.VisualDeviceAuthEntity;
import com.saida.services.srv.entity.VisualDeviceEntity;
import com.saida.services.system.visual.biz.CameraBiz;
import com.saida.services.system.visual.biz.pojo.req.CommonSnapUrlReq;
import com.saida.services.system.visual.biz.pojo.resp.CommonSnapShotResp;
import com.saida.services.system.visual.entity.DeviceGalleryEntity;
import com.saida.services.system.visual.service.DeviceGalleryService;
import com.saida.services.system.visual.service.VisualDeviceAuthService;
import com.saida.services.system.visual.service.VisualDeviceService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.VlinkerXxlJob;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class GalleryJob {

    @Autowired
    private VisualDeviceService visualDeviceService;
    @Autowired
    private VisualDeviceAuthService visualDeviceAuthService;
    @Autowired
    private FileService fileService;


    /**
     * 设备快照任务
     */
//    @VlinkerXxlJob(
//            value = "deviceGallery",
//            cron = "0 0 3 * * ? ",
//            desc = "设备快照任务执行开始"
//    )
    public ReturnT<String> deviceGallery() {
        log.info("<--------设备快照任务执行开始-------->");

        List<VisualDeviceEntity> deviceEntities = visualDeviceService.list();
        List<VisualDeviceAuthEntity> authEntityList = visualDeviceAuthService.list();

//        Map<Long, VisualDeviceAuthEntity> authEntityMap = authEntityList.stream().collect(Collectors.toMap(VisualDeviceAuthEntity::getId, Function.identity()));
//        deviceEntities.forEach(e -> {
//            VisualDeviceAuthEntity visualDeviceAuthEntity = authEntityMap.get(e.getAuthId());
//            if (visualDeviceAuthEntity == null || StringUtil.isEmpty(visualDeviceAuthEntity.getComponentName())) {
//                return;
//            }
//            try {
//                getDeviceGallery(visualDeviceAuthEntity, e);
//            } catch (Exception exception) {
//                log.error("设备快照任务执行异常 getDeviceCode:{}", e.getDeviceCode(), exception);
//            }
//
//        });

        log.info("<--------设备快照任务执行结束-------->");
        return ReturnT.SUCCESS;
    }

    @Autowired
    private DeviceGalleryService deviceGalleryService;

    public void getDeviceGallery(VisualDeviceAuthEntity visualDeviceAuthEntity, VisualDeviceEntity e) {
        CommonSnapUrlReq req = new CommonSnapUrlReq();
        req.setVisualDeviceAuthEntity(visualDeviceAuthEntity);
        req.setCameraCode(e.getDeviceCode());
        req.setChannelCode(e.getChannelCode());
        CameraBiz cameraBiz = (CameraBiz) SpringUtil.getBean(visualDeviceAuthEntity.getComponentName());
        DtoResult<CommonSnapShotResp> fResult = null;
        try {
            fResult = cameraBiz.getSnapUrl(req);
        } catch (Exception ex) {
            log.error("设备快照任务执行异常 getDeviceCode:{}", e.getDeviceCode(), ex);
            return;
        }
        if (fResult == null || fResult.getData() == null || !fResult.success()) {
            log.error("设备快照任务执行失败 getDeviceCode:{}", e.getDeviceCode());
            return;
        }
        DeviceGalleryEntity deviceGallery = new DeviceGalleryEntity();
        deviceGallery.setDeviceId(e.getId());
        CommonSnapShotResp data = fResult.getData();
        if (data.getType() == 2) {
            DtoResult<FileModel> fileModelDtoResult = fileService.uploadBase64(data.getBase64(), "jpg", "deviceGallery");
            if (fileModelDtoResult.success()) {
                log.info("设备快照任务执行成功 getDeviceCode:{},jpg:{}", e.getDeviceCode(), fileModelDtoResult);
                deviceGallery.setImgUrl(fileModelDtoResult.getData().getUrl());
            }
        } else {
            log.info("设备快照任务执行成功 getDeviceCode:{},jpg:{}", e.getDeviceCode(), data);
            deviceGallery.setImgUrl(data.getUrl());
        }
        if (StringUtil.isEmpty(deviceGallery.getImgUrl())) {
            return;
        }
        deviceGallery.setCreateBy(1705477296068825090L);
        deviceGalleryService.save(deviceGallery);
    }
}
