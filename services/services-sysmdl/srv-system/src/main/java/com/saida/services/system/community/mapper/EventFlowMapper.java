package com.saida.services.system.community.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.saida.services.system.community.entity.EventFlowEntity;
import org.apache.ibatis.annotations.Mapper;

/**
 * 事件流
 *
 * <AUTHOR>
 * email ${email}
 * date 2024-05-08 17:43:50
 */
@Mapper
public interface EventFlowMapper extends BaseMapper<EventFlowEntity> {


//    List<EventFlowEntity> listPage(@Param("entity") EventFlowEntity entity);
//
//    EventFlowEntity getInfoById(@Param("entity") EventFlowEntity entity);

}
