package com.saida.services.system.community.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;

/**
 * 单元
 *
 * <AUTHOR>
 * email ${email}
 * date 2023-10-17 09:54:50
 */
@Data
@HeadRowHeight(40)    // 表头行高
@ColumnWidth(15)        // 表头行宽
public class UnitInfoExcel implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 单元编号
     */
    @ExcelProperty(value = "单元编号", index = 0)
    private Integer num;

    /**
     * 单元名称
     */
    @ExcelProperty(value = "单元名称", index = 1)
    private String name;

    /**
     * 楼栋名称
     */
    @ExcelProperty(value = "楼栋", index = 2)
    private String buildingName;

    /**
     * 小区名称
     */
    @ExcelProperty(value = "小区", index = 3)
    private String communityName;

    /**
     * 五保户数量
     */
    @ExcelProperty(value = "五保户数", index = 4)
    private Integer fiveGuarantees;

    /**
     * 入住人员数
     */
    @ExcelProperty(value = "入住人员数", index = 5)
    private Integer checkInNum;

    /**
     * 房屋数量
     */
    @ExcelProperty(value = "房屋数量", index = 6)
    private Integer roomNum;

}
