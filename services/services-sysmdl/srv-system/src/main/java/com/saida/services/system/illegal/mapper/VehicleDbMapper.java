package com.saida.services.system.illegal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.saida.services.system.illegal.entity.VehicleDbEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


/**
 * 电子劝导员-车辆信息
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-02-14 13:54:39
 */
@Mapper
public interface VehicleDbMapper extends BaseMapper<VehicleDbEntity> {

    Page<VehicleDbEntity> listPage(@Param("page")Page<Object> page, @Param("entity")VehicleDbEntity entity);
}
