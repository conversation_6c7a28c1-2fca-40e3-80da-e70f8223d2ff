package com.saida.services.system.visual.controller;

import com.saida.services.common.base.Result;
import com.saida.services.system.visual.service.VisualUserDeviceGroupService;
import com.saida.services.system.visual.dto.AddVisualUserDeviceGroupDto;
import com.saida.services.system.visual.dto.EditVisualUserDeviceGroupDto;
import com.saida.services.system.visual.dto.VisualUserDeviceGroupListDto;
import com.saida.services.system.visual.entity.VisualUserDeviceGroupEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;


@Slf4j
@RestController
@RequestMapping("/user/device/group")
public class VisualUserDeviceGroupController {

    @Resource
    private VisualUserDeviceGroupService visualUserDeviceGroupService;

    /**
     * 视频汇聚-分组列表
     */
    @GetMapping("/list")
    public Result list(VisualUserDeviceGroupListDto dto) {
        return visualUserDeviceGroupService.list(dto);
    }

    /**
     * 首页大屏-分组列表
     */
    @GetMapping("/listByScreen")
    public Result listByScreen(VisualUserDeviceGroupListDto dto) {
        return visualUserDeviceGroupService.listByScreen(dto);
    }

    /**
     * 新增分组
     */
    @PostMapping("/add")
    public Result add(@RequestBody AddVisualUserDeviceGroupDto dto) {
        return visualUserDeviceGroupService.add(dto);
    }

    /**
     * 分组详情
     */
    @GetMapping("/info")
    public Result info(VisualUserDeviceGroupEntity entity) {
        return visualUserDeviceGroupService.info(entity);
    }

    /**
     * 编辑分组
     */
    @PostMapping("/edit")
    public Result edit(@RequestBody EditVisualUserDeviceGroupDto dto) {
        return visualUserDeviceGroupService.edit(dto);
    }

    /**
     * 删除分组
     */
    @PostMapping("/delete")
    public Result delete(@RequestBody VisualUserDeviceGroupEntity entity) {
        return visualUserDeviceGroupService.delete(entity);
    }
}