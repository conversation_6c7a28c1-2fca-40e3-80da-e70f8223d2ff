package com.saida.services.system.school.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.saida.services.common.entity.BaseEntity;
import com.saida.services.tools.attr.DisplayField;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 学校信息
 *
 * <AUTHOR>
 * email ${email}
 * date 2024-7-8 09:54:50
 */
@Data
@TableName("school_info")
public class SchoolInfoEntity extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 学校编码
     */
    @NotNull(message = "学校编码不能为空")
    private String schoolCode;

    /**
     * 学校名称
     */
    @NotNull(message = "学校名称不能为空")
    private String name;

    /**
     * 显示排序
     */
    @NotNull(message = "显示排序不能为空")
    private Integer sort;

    /**
     * 归属区域id
     */
    @NotNull(message = "归属区域不能为空")
    @DisplayField(field = "belongAreaName")
    private String belongAreaId;

    /**
     * 学校经度
     */
    @NotNull(message = "经度不能为空")
    private String lng;

    /**
     * 学校纬度
     */
    @NotNull(message = "纬度不能为空")
    private String lat;

    /**
     * 详细地址
     */
    @NotNull(message = "详细地址不能为空")
    private String address;

    /**
     * 负责人
     */
    @NotNull(message = "负责人不能为空")
    private String linkPeople;

    /**
     * 联系方式
     */
    @NotNull(message = "联系方式不能为空")
    private String linkPhone;

    /**
     * 机构id(企业id)
     */
    private String orgId;

    /*
     * 归属机构ID
     */
    private Long belongOrgId;

    /**
     * 是否删除：0：未删除；1已删除
     */
    private Integer isDelete = 0;

    private Long visualDeviceTreeId;

    /**
     * 归属区域名称
     */
    @TableField(exist = false)
    private String belongAreaName;

    private Long appScene;

    @TableField(exist = false)
    private String idChain;
}