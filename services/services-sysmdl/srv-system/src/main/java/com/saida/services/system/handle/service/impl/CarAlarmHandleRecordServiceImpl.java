package com.saida.services.system.handle.service.impl;

import com.saida.services.system.handle.entity.CarAlarmHandleRecordEntity;
import com.saida.services.system.handle.mapper.CarAlarmHandleRecordMapper;
import com.saida.services.system.handle.service.CarAlarmHandleRecordService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


@Service("carAlarmHandleRecordService")
public class CarAlarmHandleRecordServiceImpl extends ServiceImpl<CarAlarmHandleRecordMapper, CarAlarmHandleRecordEntity> implements CarAlarmHandleRecordService {


}