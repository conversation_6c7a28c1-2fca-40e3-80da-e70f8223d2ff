package com.saida.services.system.sys.service;

import com.saida.services.common.base.DtoResult;
import com.saida.services.common.service.IBaseService;
import com.saida.services.srv.entity.SrvAppChannelAlarmConfigEntity;

public interface AppChannelAlarmConfigService extends IBaseService<SrvAppChannelAlarmConfigEntity> {

    DtoResult<SrvAppChannelAlarmConfigEntity> info(SrvAppChannelAlarmConfigEntity entity);

    DtoResult<Void> edit(SrvAppChannelAlarmConfigEntity entity);

    DtoResult<Void> delete(SrvAppChannelAlarmConfigEntity entity);
}