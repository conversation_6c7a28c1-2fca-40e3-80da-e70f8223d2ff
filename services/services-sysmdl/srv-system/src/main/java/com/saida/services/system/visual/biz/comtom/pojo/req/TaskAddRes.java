package com.saida.services.system.visual.biz.comtom.pojo.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.saida.services.system.visual.entity.BroadcastTasksCycleEntity;
import com.saida.services.system.visual.req.CommonBaseReq;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class TaskAddRes extends CommonBaseReq {
    private String deviceId;

    private Long id;

    /**
     * 任务名称
     */
    private String name;
    /**
     * 开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endTime;
    /**
     * 循环次数
     */
    private Integer cyclesCount;
    /**
     * 内容
     */
    private String content;
    /**
     * 音频文件
     */
    private String contentFile;
    /**
     * 1 男 2女
     */
    private Integer sex;

    private List<BroadcastTasksCycleEntity> cycleList;

}
