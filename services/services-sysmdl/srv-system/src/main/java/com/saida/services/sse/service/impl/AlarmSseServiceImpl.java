package com.saida.services.sse.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson2.JSON;
import com.saida.services.common.base.DtoResult;
import com.saida.services.exception.BizRuntimeException;
import com.saida.services.sse.pojo.SseMessage;
import com.saida.services.sse.pojo.dto.SseMessageDTO;
import com.saida.services.sse.service.AlarmSseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

@Slf4j
@Service
public class AlarmSseServiceImpl implements AlarmSseService {

    /**
     * k:客户端id  v:SseEmitter
     */
    private static final Map<String, SseEmitter> sseEmitterMap = new ConcurrentHashMap<>();

    // 用户连接聚合：userId -> 所有连接的SseEmitter列表
    Map<String, List<SseEmitter>> userConnectionsMap = new ConcurrentHashMap<>();

    private final int MAX_CONNECTIONS_PER_USER = 5;

    /**
     * 连接sse
     * clientId用于区分客户端
     */
    @Override
    public SseEmitter connect(Long orgId, Long appScene, Long userId) {
        String userClientId = getUserClientId(orgId, appScene, userId);
        String clientId = String.format("%s_%s", userClientId, UUID.randomUUID().toString().replace("-", ""));
        log.info("sse实时推送新用户连接...orgId={}, appScene={}, userId={}, clientId={}", orgId, appScene, userId, clientId);

        // x分钟后断开
        SseEmitter sseEmitter = new SseEmitter(60 * 1000L * 20);
        // SseEmitter sseEmitter = new SseEmitter(1000L * 10);
        SseMessage sseMessage = new SseMessage();
        if (sseEmitterMap.containsKey(clientId)) {
            return SseExceptionHandle(sseMessage, sseEmitter, "该clientId已绑定指定的客户端！clientId:" + clientId);
        }

        if (sseEmitterMap.size() > 1000) {
            return SseExceptionHandle(sseMessage, sseEmitter, "客户端连接过多，请稍后重试！");
        }
        // 连接成功需要返回数据，否则会出现待处理状态
        try {
            sseMessage.setCode(HttpServletResponse.SC_OK);
            sseMessage.setData("连接成功！");
            sseEmitter.send(sseMessage, MediaType.APPLICATION_JSON);
        } catch (IOException e) {
            log.error("sse连接发送数据时出现异常...msg={}", e.getMessage());
            throw new BizRuntimeException("sse连接发送数据时出现异常！");
        }

        // 连接断开
        sseEmitter.onCompletion(() -> {
            log.info("sse连接断开，clientId为：{}", clientId);
            sseEmitterMap.remove(clientId);
            removeEmitterFromUserMap(userClientId, sseEmitter);
        });

        // 连接超时
        sseEmitter.onTimeout(() -> {
            log.info("sse连接已超时，clientId为：{}", clientId);
            sseEmitter.complete();
        });

        // 连接报错
        sseEmitter.onError((throwable) -> {
            log.info("sse连接异常...msg={}", throwable.getMessage());
            sseEmitterMap.remove(clientId);
            removeEmitterFromUserMap(userClientId, sseEmitter);
        });
        sseEmitterMap.put(clientId, sseEmitter);
        List<SseEmitter> sseEmitterList = userConnectionsMap.computeIfAbsent(userClientId, k -> new CopyOnWriteArrayList<>());
        if (sseEmitterList.size() >= MAX_CONNECTIONS_PER_USER) {
            // 移除最早建立的连接（LRU策略）
            SseEmitter oldest = sseEmitterList.remove(0);
            oldest.complete();
            sseEmitterMap.values().remove(oldest);
        }
        sseEmitterList.add(sseEmitter);
        return sseEmitter;
    }

    /**
     * 发送消息
     */
    @Override
    public DtoResult<Void> sendMessage(String userClientId, SseMessageDTO sseMessageDTO) {
        if (CollectionUtil.isEmpty(sseEmitterMap)) {
            log.error("sseEmitterMap为空，程序结束");
            return DtoResult.ok();
        }
        List<SseEmitter> sseEmitterList = userConnectionsMap.get(userClientId);
        if (CollectionUtil.isEmpty(sseEmitterList)) {
            log.error("sseEmitterList为空，程序结束...userClientId={}", userClientId);
            return DtoResult.ok();
        }
        log.info("SSE推送数据...userClientId={}, sseEmitterList.size={}", userClientId, sseEmitterList.size());
        for (SseEmitter sseEmitter : sseEmitterList) {
            try {
                SseMessage message = new SseMessage();
                message.setCode(HttpServletResponse.SC_OK);
                message.setPushTime(DateUtil.formatDateTime(new Date()));
                message.setData(JSON.toJSON(sseMessageDTO));
                sseEmitter.send(message, MediaType.APPLICATION_JSON);
            } catch (Exception e) {
                log.error("告警sse推送json转换异常...msg={}", e.getMessage());
            }
        }
        return DtoResult.ok();
    }

    // 从userConnectionsMap移除指定Emitter
    private void removeEmitterFromUserMap(String userClientId, SseEmitter emitter) {
        List<SseEmitter> sseEmitterList = userConnectionsMap.get(userClientId);
        if (CollectionUtil.isNotEmpty(sseEmitterList)) {
            sseEmitterList.remove(emitter);
            if (CollectionUtil.isEmpty(sseEmitterList)) {
                userConnectionsMap.remove(userClientId);
            }
        }
    }

    private SseEmitter SseExceptionHandle(SseMessage sseMessage, SseEmitter sseEmitter, String exceptionMessage) {
        log.error(exceptionMessage);
        sseMessage.setCode(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        sseMessage.setData(exceptionMessage);
        try {
            sseEmitter.send(sseMessage, MediaType.APPLICATION_JSON);
        } catch (IOException e) {
            log.error("sse连接发送数据时出现异常...msg={}", e.getMessage(), e);
            throw new BizRuntimeException("sse连接发送数据时出现异常！");
        }
        return sseEmitter;
    }

    public static String getUserClientId(Long orgId, Long appScene, Long userId) {
        return String.format("%s_%s_%s", orgId, appScene, userId);
    }
}