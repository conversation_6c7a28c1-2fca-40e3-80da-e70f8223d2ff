package com.saida.services.system.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

/**
 * 森林防火-系统管理-已绑订组织用户导出
 *
 * <AUTHOR>
 * @version SlfhFormUserExcel v1.0.0
 * @since 2023/09/19 09:30:03
 */
@Data
@HeadRowHeight(40)
@ColumnWidth(15)
public class SlfhFormUserExcel {

    @ExcelProperty(value = "用户名", index = 0)
    private String account;

    @ExcelProperty(value = "真实姓名", index = 1)
    private String nickname;

    @ExcelProperty(value = "性别", index = 2)
    private String genderName;

    @ExcelProperty(value = "手机号", index = 3)
    private String phone;
}
