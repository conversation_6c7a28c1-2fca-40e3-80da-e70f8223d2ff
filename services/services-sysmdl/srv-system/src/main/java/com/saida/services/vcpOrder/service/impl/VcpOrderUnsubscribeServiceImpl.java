package com.saida.services.vcpOrder.service.impl;

import com.saida.services.common.service.BaseServiceImpl;
import com.saida.services.vcpOrder.entity.VcpOrderUnsubscribeEntity;
import com.saida.services.vcpOrder.mapper.VcpOrderUnsubscribeMapper;
import com.saida.services.vcpOrder.service.VcpOrderUnsubscribeService;
import org.springframework.stereotype.Service;

@Service
public class VcpOrderUnsubscribeServiceImpl extends BaseServiceImpl<VcpOrderUnsubscribeMapper, VcpOrderUnsubscribeEntity> implements VcpOrderUnsubscribeService {
}
