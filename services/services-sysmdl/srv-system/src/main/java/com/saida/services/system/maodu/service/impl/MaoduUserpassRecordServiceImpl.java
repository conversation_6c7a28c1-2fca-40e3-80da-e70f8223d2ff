package com.saida.services.system.maodu.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.saida.services.common.base.Result;
import com.saida.services.common.entity.BasePageInfoEntity;
import com.saida.services.common.tools.JwtUtil;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.entities.base.BaseRequest;
import com.saida.services.system.maodu.mapper.MaoduUserpassRecordMapper;
import com.saida.services.system.maodu.pojo.entity.MaoduUserpassRecordEntity;
import com.saida.services.system.maodu.service.MaoduUserpassRecordService;
import com.saida.services.system.sys.entity.UserEntity;
import com.saida.services.system.sys.service.AttributeDetailService;
import com.saida.services.system.sys.service.UserService;
import com.saida.services.tools.attr.AttrUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;


@Service("maoduUserpassRecordService")
public class MaoduUserpassRecordServiceImpl extends ServiceImpl<MaoduUserpassRecordMapper, MaoduUserpassRecordEntity> implements MaoduUserpassRecordService {

    @Autowired
    private AttributeDetailService attributeDetailService;

    @Resource
    private UserService userService;

    @Override
    public Result listPage(MaoduUserpassRecordEntity entity, BaseRequest baseRequest) {
        // 开启分页功能，设置页码和每页大小
        PageHelper.startPage(baseRequest.getPageNum(), baseRequest.getPageSize());
        // 如果班级信息实体类的所属企业id为空
        if (StringUtil.isEmpty(entity.getOrgId())) {
            // 获取所属企业的id
            Long enterpriseOrgId = JwtUtil.getEnterpriseOrgId();
            // 如果所属企业id为空
            if (enterpriseOrgId == null) {
                // 返回错误信息，所属企业id为空
                return Result.error("所属企业id为空");
            }
            // 设置班级信息实体类的所属企业id
            entity.setOrgId(enterpriseOrgId);
        }
        // 调用父类的baseMapper进行分页查询
        List<MaoduUserpassRecordEntity> resultList = super.baseMapper.listPage(entity);

        Map<Object, Object> dicMap = new HashMap<>(attributeDetailService.getAllIdNameMap());
        List<Long> uids = resultList.stream()
                .flatMap(workOrder -> Stream.of(workOrder.getCreateBy()))
                .distinct()
                .collect(Collectors.toList());
        List<UserEntity> userList = userService.getUserList(uids);
        if (CollectionUtil.isNotEmpty(userList)) {
            dicMap.putAll(userList.stream().collect(Collectors.toMap(UserEntity::getId, UserEntity::getName)));
        }
        resultList.replaceAll(o -> AttrUtil.putAttr(o, dicMap));

        // 返回分页查询结果，将查询结果包装为BasePageInfoEntity对象
        return Result.ok(new BasePageInfoEntity<>(new PageInfo<>(resultList)));
    }

    @Override
    public Result saveOrUpdateBean(MaoduUserpassRecordEntity entity) {
        if (entity.getId() == null) {
            if (entity.getOrgId() == null) {
                entity.setOrgId(JwtUtil.getEnterpriseOrgId());
            }
            this.save(entity);
        }else {

            this.updateById(entity);
        }

        return Result.ok();
    }

    @Override
    public Result delete(String ids) {

        this.removeByIds(Arrays.asList(ids.split(",")));
        return Result.ok();
    }


    @Override
    public MaoduUserpassRecordEntity getInfo(Long id) {
        // 调用父类中的baseMapper的getInfoById方法，传入entity作为参数，获取班级信息实体类对象
        MaoduUserpassRecordEntity entity = super.baseMapper.getInfo(id);

        Map<Object, Object> dicMap = new HashMap<>(attributeDetailService.getAllIdNameMap());
        List<Long> uids = new ArrayList<>();
        uids.add(entity.getCreateBy());
        List<UserEntity> userList = userService.getUserList(uids);
        if (CollectionUtil.isNotEmpty(userList)) {
            dicMap.putAll(userList.stream().collect(Collectors.toMap(UserEntity::getId, UserEntity::getName)));
        }
        return AttrUtil.putAttr(entity, dicMap);
    }
}