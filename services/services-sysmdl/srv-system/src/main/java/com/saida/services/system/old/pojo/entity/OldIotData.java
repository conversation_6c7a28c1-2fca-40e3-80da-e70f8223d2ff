package com.saida.services.system.old.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.saida.services.common.entity.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <p>
 * 养老-物联设备数据
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-02
 */
@Getter
@Setter
@ToString
@TableName("old_iot_data")
public class OldIotData extends BaseEntity<OldIotData> {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 机构id(企业id)
     */
    private Long orgId;

    /**
     * 设备ID
     */
    private Long deviceId;

    /**
     * 数据类型：19仁科水位监测 20仁科位移滑坡监测
     */
    private Integer regular;

    /**
     * 雷达水位计空高
     */
    private String radarNilometerAirHigh;

    /**
     * 雷达水位计液位高
     */
    private String radarNilometerLiquidHigh;

    /**
     * 累积雨量
     */
    private String accumulateRainfall;

    /**
     * 日雨量（昨日雨量）
     */
    private String yesterdayRainfall;

    /**
     * 瞬时雨量
     */
    private String instantRainfall;

    /**
     * 当前雨量（今日雨量）
     */
    private String todayRainfall;

    /**
     * 测量站解算海拔
     */
    private String altitude;

    /**
     * 当前水平位移
     */
    private String horizontalShift;

    /**
     * 当前垂直位移
     */
    private String verticalShift;

    /**
     * 剩余电量
     */
    private String remainBattery;

    /**
     * 是否删除：0：未删除；1已删除
     */
    @TableLogic
    private Byte isDelete;

    /**
     * 场景ID
     */
    private Long appScene;



    @TableField(exist = false)
    private String originJson;

}
