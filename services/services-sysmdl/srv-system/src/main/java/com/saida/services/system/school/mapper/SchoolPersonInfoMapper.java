package com.saida.services.system.school.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.saida.services.system.school.entity.SchoolPersonInfoEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 学校人员信息表
 *
 * <AUTHOR>
 * email ${email}
 * date 2024-7-8 09:54:50
 */
@Mapper
public interface SchoolPersonInfoMapper extends BaseMapper<SchoolPersonInfoEntity> {

    /**
     * 分页查询学校人员信息列表
     *
     * @param entity 学校人员信息实体类对象，用于查询条件
     * @return 返回一个包含学校信息列表的List集合
     */
    List<SchoolPersonInfoEntity> listPage(@Param("entity") SchoolPersonInfoEntity entity);

    /**
     * 根据ID获取学校人员信息
     *
     * @param entity 包含学校人员ID的SchoolPersonInfoEntity对象，用于查询条件
     * @return 返回对应的SchoolPersonInfoEntity对象，如果未找到则返回null
     */
    SchoolPersonInfoEntity getInfoById(@Param("entity") SchoolPersonInfoEntity entity);

    /**
     * 根据传入的id列表更新记录的isDelete字段为已删除状态
     *
     * @param ids 包含多个id的字符串，id之间用逗号分隔
     */
    void updateIsDeleteByIds(@Param("ids") String ids);

    void updateClassIdByIds(@Param("ids") String ids, @Param("classId") Long classId);

    Integer getCountByClassIds(@Param("classIds") String classIds);

    List<SchoolPersonInfoEntity> getListByIds(@Param("ids") List<String> ids);


    List<SchoolPersonInfoEntity> getListByDeviceTreeId(@Param("deviceTreeId") Long deviceTreeId);

    Integer getCountBySchoolIds(@Param("schoolIds") String schoolIds);


    List<SchoolPersonInfoEntity> getListByStartTimeOrEndTime(@Param("flag") Integer flag);

    Integer getCountByStartTimeOrEndTime(@Param("entity") SchoolPersonInfoEntity entity);
}