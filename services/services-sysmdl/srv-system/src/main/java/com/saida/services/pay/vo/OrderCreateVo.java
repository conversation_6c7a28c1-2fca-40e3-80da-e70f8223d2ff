package com.saida.services.pay.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

/**
 * 服务套餐创建dto
 *
 * <AUTHOR>
 * @version PackageOrderCreateDto v1.0.0
 * @since 2025/1/16 11:42
 */
@Data
@AllArgsConstructor
@Builder
public class OrderCreateVo {
    /**
     * 订单，业务关联关系表主键
     */
    private Long id;
    /**
     * 签名字符串
     */
    private String orderStr;
    /**
     * 支付类型
     * 1：支付宝
     * 2：微信
     */
    private Integer payType;
}
