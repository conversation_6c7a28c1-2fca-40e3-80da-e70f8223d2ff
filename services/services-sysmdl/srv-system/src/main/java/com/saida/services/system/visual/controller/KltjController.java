package com.saida.services.system.visual.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.saida.services.common.base.DtoResult;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.srv.dto.AiBoxPersonFlowDto;
import com.saida.services.system.visual.service.VisualAlarmService;
import com.saida.services.system.visual.entity.VisualAlarmEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


@Slf4j
@RestController
@RequestMapping("/kltj")
public class KltjController {

    @Resource
    private VisualAlarmService visualAlarmService;

    /**
     * 客流统计刷数据
     */
    @GetMapping("/brushData")
    public DtoResult<Void> listPage(
            @RequestParam("deviceId") Long deviceId,
            @RequestParam("alarmType") String alarmType) {
        // 查询上一次的客流人数
        List<VisualAlarmEntity> visualAlarmEntityList = visualAlarmService.list(new LambdaQueryWrapper<VisualAlarmEntity>()
                .eq(VisualAlarmEntity::getDeviceId, deviceId)
                .eq(VisualAlarmEntity::getAlarmType, alarmType)
                .orderByAsc(VisualAlarmEntity::getId)
        );
        if (CollectionUtil.isEmpty(visualAlarmEntityList)) {
            return DtoResult.ok();
        }

        int size = visualAlarmEntityList.size();

        List<VisualAlarmEntity> updateBatchVisualAlarmEntityList = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            VisualAlarmEntity updateVisualAlarmEntity = new VisualAlarmEntity();

            VisualAlarmEntity visualAlarmEntity = visualAlarmEntityList.get(i);
            VisualAlarmEntity lastVisualAlarmEntity = null;

            if (i > 0) {
                lastVisualAlarmEntity = visualAlarmEntityList.get(i - 1);
            }
            try {
                int personFlow = 0;
                String metadata = visualAlarmEntity.getExt();

                AiBoxPersonFlowDto aiBoxPersonFlowDto = JSON.parseObject(metadata, AiBoxPersonFlowDto.class);
                List<AiBoxPersonFlowDto.Rois> roisList = aiBoxPersonFlowDto.getRois();
                if (CollectionUtil.isNotEmpty(roisList)) {
                    for (AiBoxPersonFlowDto.Rois rois : roisList) {
                        List<AiBoxPersonFlowDto.Flow> flowList = rois.getFlow();
                        if (CollectionUtil.isNotEmpty(flowList)) {
                            for (AiBoxPersonFlowDto.Flow flow : flowList) {
                                if (Objects.equals(flow.getType(), 1)) {
                                    personFlow += flow.getIn();
                                }
                            }
                        }
                    }

                    int lastPersonFlow = 0;
                    if (lastVisualAlarmEntity != null) {
                        String ext = lastVisualAlarmEntity.getExt();
                        if (StringUtil.isNotEmpty(ext)) {
                            AiBoxPersonFlowDto lastAiBoxPersonFlowDto = JSON.parseObject(ext, AiBoxPersonFlowDto.class);

                            List<AiBoxPersonFlowDto.Rois> lastRoisList = lastAiBoxPersonFlowDto.getRois();
                            if (CollectionUtil.isNotEmpty(lastRoisList)) {
                                for (AiBoxPersonFlowDto.Rois rois : lastRoisList) {
                                    List<AiBoxPersonFlowDto.Flow> flowList = rois.getFlow();
                                    if (CollectionUtil.isNotEmpty(flowList)) {
                                        for (AiBoxPersonFlowDto.Flow flow : flowList) {
                                            if (Objects.equals(flow.getType(), 1)) {
                                                lastPersonFlow += flow.getIn();
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    if (personFlow >= lastPersonFlow) {
                        personFlow = personFlow - lastPersonFlow;
                    } else {
                        personFlow = 0;
                    }
                }
                updateVisualAlarmEntity.setId(visualAlarmEntity.getId());
                updateVisualAlarmEntity.setPersonFlow(personFlow);
                updateBatchVisualAlarmEntityList.add(updateVisualAlarmEntity);
            } catch (Exception e) {
                log.error("客流统计刷数据异常...msg={}", e.getMessage(), e);
            }
        }
        if (CollectionUtil.isNotEmpty(updateBatchVisualAlarmEntityList)) {
            log.info("刷客流数据...updateBatchVisualAlarmEntityList={}", JSON.toJSON(updateBatchVisualAlarmEntityList));
            visualAlarmService.updateBatchById(updateBatchVisualAlarmEntityList);
        }
        return DtoResult.ok();
    }
}