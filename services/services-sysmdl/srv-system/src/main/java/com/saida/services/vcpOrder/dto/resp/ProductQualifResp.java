package com.saida.services.vcpOrder.dto.resp;

import lombok.Data;

import java.util.List;

@Data
public class ProductQualifResp {

    /**
     * 原子能力平台内买家的用户id
     */
    private String memberId;
    /**
     * 原子能力平台内买家的账号名称
     */
    private String memberName;

    /**
     * 原子能力平台内买家的电话号码
     */
    private String phoneNo;

    /**
     * 原子能力平台内买家的邮箱地址
     */
    private String email;

    /**
     * result_info的对象数组，描述了能力网关在开通服务时需要查询客户的信息必要信息，格式上需要满足json标准。
     */
    private List<ResultInfo> resultInfoList;


    @Data
    public static class ResultInfo {
        /**
         * 客户资质信息的名称
         */
        private String qualifName;
        /**
         * 客户资质信息的类型
         */
        private String qualifType;
        /**
         * 客户真实资质信息或者文件地址
         */
        private String qualifValue;
    }
}
