package com.saida.services.system.maodu.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.saida.services.system.maodu.pojo.dto.ColValue;
import com.saida.services.system.maodu.pojo.entity.MaoduCarpassRecordEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;


/**
 * 通行时间
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-03-17 13:58:44
 */
@Mapper
public interface MaoduCarpassRecordMapper extends BaseMapper<MaoduCarpassRecordEntity> {

    MaoduCarpassRecordEntity getInfo(Long id);

    List<MaoduCarpassRecordEntity> listPage(@Param("entity") MaoduCarpassRecordEntity entity);

    List<MaoduCarpassRecordEntity> list(@Param("entity") MaoduCarpassRecordEntity entity);
}
