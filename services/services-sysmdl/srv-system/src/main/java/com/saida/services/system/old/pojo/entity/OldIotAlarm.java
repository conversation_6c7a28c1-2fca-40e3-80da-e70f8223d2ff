package com.saida.services.system.old.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;

import java.time.LocalDateTime;

import com.saida.services.common.entity.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <p>
 * 养老-物联告警消息
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-26
 */
@Getter
@Setter
@ToString
@TableName("old_iot_alarm")
public class OldIotAlarm extends BaseEntity<OldIotAlarm>  {

    private static final long serialVersionUID = 1L;


    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 企业id
     */
    private Long enterpriseId;

    /**
     * 机构id(企业id)
     */
    private Long orgId;

    /**
     * 设备ID
     */
    private Long deviceId;

    /**
     * 设备类型 1滑坡监测 2水位监测
     */
    private Integer deviceType;

    /**
     * 告警时间
     */
    private LocalDateTime alarmTime;

    /**
     * 告警内容
     */
    private String alarmContent;

    /**
     * 原始报文信息
     */
    private String originJson;

    /**
     * 是否删除：0：未删除；1已删除
     */
    @TableLogic
    private Integer isDelete;

    /**
     * 场景ID
     */
    private Long appScene;


}
