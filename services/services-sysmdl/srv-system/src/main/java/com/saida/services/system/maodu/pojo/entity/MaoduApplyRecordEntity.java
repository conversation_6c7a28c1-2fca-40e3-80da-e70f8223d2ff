package com.saida.services.system.maodu.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.saida.services.common.entity.BaseEntity;
import com.saida.services.tools.attr.DisplayField;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 入园管理
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-03-17 13:58:44
 */
@Data
@TableName("maodu_apply_record")
public class MaoduApplyRecordEntity extends BaseEntity<MaoduApplyRecordEntity>{
	private static final long serialVersionUID = 1L;



	/**
	 * 组织ID（企业ID）
	 */
	private Long orgId;


	/**
	 * 应用场景
	 */
	private Long appScene;



	/**
	 * 车辆类型
	 */
	//1危化品车,2普通车
	private String carType;


	/**
	 * 车牌号码
	 */
	private String carNum;


	/**
	 * 车主姓名
	 */
	private String ownerName;


	/**
	 * 联系电话
	 */
	private String phone;


	/**
	 * 入园状态,待审核,已通过,已入园
	 */
	@DisplayField(field = "applyStatusName")
	private Long applyStatus;


	/**
	 * 申请入园日期
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private String applyTime;


	/**
	 * 入园说明
	 */
	private String applyMemo;


	/**
	 * 审核时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private String checkTime;


	/**
	 * 审核操作,1通过/2驳回
	 */
	private String checkStatus;


	/**
	 * 审核说明
	 */
	private String checkMemo;


	/**
	 * 审核人
	 */
	private String checkUserName;


	private String position;

	/**
	 * 经度
	 */
	private String lng;


	/**
	 * 纬度
	 */
	private String lat;


	@TableField(exist = false)
	private String startDate;

	@TableField(exist = false)
	private String endDate;

	@TableField(exist = false)
	private Long[] applyStatuss;


	@DisplayField(field = "createByName")
	@TableField(value = "create_by", fill = FieldFill.INSERT)
	private Long createBy;

	@TableField(exist = false)
	private Long carId;





}
