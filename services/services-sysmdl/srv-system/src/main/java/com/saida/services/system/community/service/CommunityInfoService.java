package com.saida.services.system.community.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.saida.services.common.base.Result;
import com.saida.services.system.community.entity.CommunityInfoEntity;
import com.saida.services.entities.base.BaseRequest;

import java.util.List;

/**
 * 小区管理
 *
 * <AUTHOR>
 * email ${email}
 * date 2023-10-17 09:54:50
 */
public interface CommunityInfoService extends IService<CommunityInfoEntity> {

    Result listPage(CommunityInfoEntity entity, BaseRequest request);

    List<CommunityInfoEntity> getList(CommunityInfoEntity entity);

    CommunityInfoEntity getInfoById(CommunityInfoEntity entity);

    CommunityInfoEntity getInfoByVisualDeviceTreeId(CommunityInfoEntity entity);

    Result saveOrUpdateBean(CommunityInfoEntity entity);

    Result deleteByIds(BaseRequest entity);

}

