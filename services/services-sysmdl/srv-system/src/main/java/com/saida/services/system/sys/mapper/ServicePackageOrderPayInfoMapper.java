package com.saida.services.system.sys.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.saida.services.srv.entity.ServicePackageOrderPayInfoEntity;
import com.saida.services.system.sys.dto.PackageOrderQryDto;
import com.saida.services.system.sys.vo.PackageOrderQryListVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * 套餐支付订单记录(ServicePackageOrderPayInfoEntity)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-01-16 11:03:45
 */
public interface ServicePackageOrderPayInfoMapper extends BaseMapper<ServicePackageOrderPayInfoEntity> {


    List<PackageOrderQryListVo> listPage(PackageOrderQryDto dto);

    PackageOrderQryListVo findOneByOrderId(String orderId);

    List<ServicePackageOrderPayInfoEntity> getAvailableSmsPackage(@Param("userIdSet") Set<Long> userIdSet);
}