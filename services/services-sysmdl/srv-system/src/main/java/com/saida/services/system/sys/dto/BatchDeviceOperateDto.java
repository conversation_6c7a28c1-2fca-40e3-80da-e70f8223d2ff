package com.saida.services.system.sys.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchDeviceOperateDto implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long groupId;

    private List<Long> groupDeviceIdList;
}