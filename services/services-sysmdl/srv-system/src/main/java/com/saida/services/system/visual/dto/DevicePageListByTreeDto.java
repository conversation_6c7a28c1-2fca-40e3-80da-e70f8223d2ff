package com.saida.services.system.visual.dto;

import com.saida.services.srv.entity.VisualDeviceEntity;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Set;

/**
 * 设备树管理-设备列表请求实体类
 */
@Getter
@Setter
public class DevicePageListByTreeDto extends VisualDeviceEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /*
     * 接口传参-设备类型：固定字典camera_platform_type字段，监控设备，广播设备，AI盒子设备
     */
    private Long deviceType;

    /*
     * 接口传参-设备唯一编码
     */
    private String deviceCode;

    private String deviceCodeCode;

    private String channelCode;

    private String channelName;

    /*
     * 接口传参-设备名称
     */
    private String deviceName;

    /*
     * 接口传参- 应用场景
     */
    private Long appScene;

    /*
     * 接口传参- 组织id
     */
    private Long orgId;

    /*
     * 接口传参- 设备树ID
     */
    private Long deviceTreeId;

    private String deviceTreeIdChain;

    /**
     * 接口传参- 是否包含下级：true-包含下级；false-不包含下级
     */
    private Boolean subLevel = false;

    /*
     * SQL传参- 设备树ID集合
     */
    private Set<Long> deviceTreeIdSet;

    /*
     * SQL传参- 用户ID
     */
    private Long userId;

    /**
     * 1:人闸 2:车闸
     */
    private Integer gateType;

    /**
     * 固定类型
     * 1、北望霸凌终端
     * 2、北望7寸
     * 3、北望10寸
     *
     * @see com.saida.services.iot.enums.IotRegularEnum
     * 有些型号有固定类型  有些没处理就从枚举文件中读取
     */
    private Integer regular;


    private String productId;

    private String deviceTypes;

    private String gardenEnterpriseName;

    private String gardenName;
    
    /**
     * 江南中街-移动端搜索-噪音设备列表
     * 设备名称或编码
     */
    private String deviceNameOrCode;


    /*
     * 在线状态：0-离线；1-在线
     */
    private Integer isOnline;

    /**
     * visual_device_org表设备名称
     */
    private String showDeviceName;

    /**
     * 物联产品sn
     */
    private String productSn;
}