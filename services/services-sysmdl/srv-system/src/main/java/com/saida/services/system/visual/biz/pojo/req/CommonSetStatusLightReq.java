package com.saida.services.system.visual.biz.pojo.req;

import com.saida.services.system.visual.req.CommonBaseReq;
import lombok.Data;

import java.io.Serializable;

/**
 * ClassName: TsingseeLoginReq <br/>
 * Description: <br/>
 * date: 2023/07/20 17:36<br/>
 *
 * <AUTHOR> />
 */
@Data
public class CommonSetStatusLightReq extends CommonBaseReq implements Serializable {
    private static final long serialVersionUID = 1L;

    private String deviceCode;

    private String channelCode;

    //0:自动切换（自动切换时不允许关闭补光）, 1:彩色模式（彩色模式时只支持白光补光）, 2:黑白模式（黑白模式时只支持红外补光）
    public Integer mode;
    //0:关闭, 1:白光补光, 2:红外补光
    private Integer value;
    //0:状态指示灯关闭, 1:状态指示灯开启
    private Integer statusLight;

}