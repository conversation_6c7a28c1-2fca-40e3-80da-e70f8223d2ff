package com.saida.services.system.visual.biz.vlink.pojo.req;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * ClassName: TsingseeLoginReq <br/>
 * Description: <br/>
 * date: 2023/07/20 17:36<br/>
 *
 * <AUTHOR> />
 */
@Data
public class VlinkGotoPresetReq extends VlinkBaseTokenReq implements Serializable {
    private static final long serialVersionUID = 1L;

    /*
     * 预置点位
     */
    @JSONField(name = "index")
    private Integer index;

    /** 预置点名称 */
    private String name;

    @JSONField(name = "deviceId")
    private String deviceId;

}