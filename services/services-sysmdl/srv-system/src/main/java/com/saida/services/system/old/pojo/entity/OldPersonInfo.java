package com.saida.services.system.old.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;

import java.time.LocalDate;
import java.time.LocalDateTime;

import com.saida.services.common.entity.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <p>
 * 养老-老人表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-26
 */
@Getter
@Setter
@ToString
@TableName("old_person_info")
public class OldPersonInfo  extends BaseEntity<OldPersonInfo> {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 企业id
     */
    private Long enterpriseId;

    /**
     * 机构id(企业id)
     */
    private Long orgId;

    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空")
    private String name;

    /**
     * 是否卧床  1是 2否
     */
    @NotNull(message = "是否卧床不能为空")
    private Integer isBed = 2;

    /**
     * 性别（字典）
     */
    @NotNull(message = "性别不能为空")
    private Long sex;

    /**
     * 证件类型（字典）
     */
    @NotNull(message = "证件类型不能为空")
    private Long cardType;

    /**
     * 证件号码
     */
    @NotBlank(message = "证件号码不能为空")
    private String idCard;

    /**
     * 人员图片
     */
    @NotBlank(message = "人员图片不能为空")
    private String imgUrl;

    /**
     * 家属姓名
     */
    @NotBlank(message = "家属姓名不能为空")
    private String kinName;

    /**
     * 出生日期
     */
    @NotNull(message = "出生日期不能为空")
    private LocalDate birthday;

    /**
     * 家属联系方式
     */
    @NotBlank(message = "家属联系方式不能为空")
    private String kinPhone;

    /**
     * AI布控类型  1：已布控 2：未布控  3云能力布控
     */
    private Integer aiType;

    /**
     * 腾讯人脸ID
     */
    private String faceId;

    /**
     * AI盒子ID
     */
    private Long deviceId;

    /**
     * 是否删除：0：未删除；1已删除
     */
    @TableLogic
    private Integer isDelete;

    /**
     * 场景ID
     */
    private Long appScene;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人ID
     */
    private Long createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人ID
     */
    private Long updateBy;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;
}
