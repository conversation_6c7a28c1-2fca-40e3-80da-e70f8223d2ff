package com.saida.services.system.visual.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class StopPlaybackDto implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long deviceId;

    private String uuid;
}