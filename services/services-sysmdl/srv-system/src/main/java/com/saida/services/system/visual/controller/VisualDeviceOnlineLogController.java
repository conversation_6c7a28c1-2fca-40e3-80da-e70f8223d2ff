package com.saida.services.system.visual.controller;

import com.saida.services.common.base.Result;
import com.saida.services.entities.base.BaseRequest;
import com.saida.services.system.visual.service.VisualDeviceOnlineLogService;
import com.saida.services.system.visual.dto.VisualDeviceOnlineLogDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;



/**
 * 设备上下线记录表
 *
 * <AUTHOR>
 * email ${email}
 * date 2023-10-23 15:13:25
 */
@RestController
@RequestMapping("visualDeviceOnlineLog")
public class VisualDeviceOnlineLogController {
    @Autowired
    private VisualDeviceOnlineLogService visualDeviceOnlineLogService;

    /**
     * 分页列表查询
     */
    @GetMapping("listPage")
    public Result listPage(VisualDeviceOnlineLogDto entity, BaseRequest request) {
        return visualDeviceOnlineLogService.listPage(entity,request);
    }
}