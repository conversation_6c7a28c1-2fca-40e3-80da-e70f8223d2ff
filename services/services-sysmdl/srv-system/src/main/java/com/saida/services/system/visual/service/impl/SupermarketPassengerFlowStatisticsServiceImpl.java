package com.saida.services.system.visual.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.saida.services.common.base.DtoResult;
import com.saida.services.common.tools.DateUtil;
import com.saida.services.common.tools.EasyExcelStyleUtil;
import com.saida.services.common.tools.JwtUtil;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.enumeration.UserTypeEnum;
import com.saida.services.exception.BizRuntimeException;
import com.saida.services.open.entity.AlgorithmManageEntity;
import com.saida.services.srv.dto.*;
import com.saida.services.srv.entity.SupermarketEntity;
import com.saida.services.srv.enums.SrvCameraPlatformTypeEnum;
import com.saida.services.srv.excel.GetStatisticsExportDayExcel;
import com.saida.services.srv.vo.SupermarketPassengerFlowStatisticsStatisticsVo;
import com.saida.services.srv.vo.SupermarketPassengerFlowStatisticsTrendVo;
import com.saida.services.system.sys.service.UserService;
import com.saida.services.system.visual.mapper.VisualAlarmMapper;
import com.saida.services.system.visual.mapper.VisualDeviceTreeMapper;
import com.saida.services.system.visual.service.SceneInfoService;
import com.saida.services.system.visual.service.SupermarketPassengerFlowStatisticsService;
import com.saida.services.system.visual.service.SupermarketService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.*;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SupermarketPassengerFlowStatisticsServiceImpl implements SupermarketPassengerFlowStatisticsService {

    @Resource
    private UserService userService;
    @Resource
    private SupermarketService supermarketService;
    @Resource
    private VisualDeviceTreeMapper visualDeviceTreeMapper;
    @Resource
    private VisualAlarmMapper visualAlarmMapper;
    @Resource
    private SceneInfoService sceneInfoService;

    @Override
    public DtoResult<List<SupermarketPassengerFlowStatisticsStatisticsVo>> getStatistics(SupermarketPassengerFlowStatisticsStatisticsDto dto, boolean limit) {
        if (null == dto.getType()) {
            return DtoResult.error("请选择统计类型！");
        }
        UserTypeEnum loginUserTypeEnum = userService.getUserType(JwtUtil.getUserId());
        if (JwtUtil.isSupper() || UserTypeEnum.GUAN_LI.equals(loginUserTypeEnum)) {
            if (null == dto.getOrgId()) {
                return DtoResult.error("请选择一个企业！");
            }
        } else {
            dto.setOrgId(JwtUtil.getEnterpriseOrgId());
        }
        List<SupermarketEntity> supermarketEntityList = supermarketService.list(new LambdaQueryWrapper<SupermarketEntity>()
                .eq(SupermarketEntity::getOrgId, dto.getOrgId())
                .eq(SupermarketEntity::getAppScene, dto.getAppScene())
        );
        if (CollectionUtil.isEmpty(supermarketEntityList)) {
            return DtoResult.ok();
        }
        List<GetDeviceAndDeviceTreeListByUserResultDto> deviceAndDeviceTreeListByUserResultDtoList;
        // 如果是企业子账户
        // if (UserTypeEnum.ZI_ZHANG_HU.equals(loginUserTypeEnum)) {
        //     GetDeviceAndDeviceTreeListByUserParamDto deviceAndDeviceTreeListByUserParamDto = new GetDeviceAndDeviceTreeListByUserParamDto();
        //     deviceAndDeviceTreeListByUserParamDto.setUserId(JwtUtil.getUserId());
        //     deviceAndDeviceTreeListByUserParamDto.setOrgId(dto.getOrgId());
        //     deviceAndDeviceTreeListByUserParamDto.setAppScene(dto.getAppScene());
        //     deviceAndDeviceTreeListByUserParamDto.setDeviceType(SrvCameraPlatformTypeEnum.TYPE_1.getDicId());
        //     deviceAndDeviceTreeListByUserResultDtoList = visualDeviceTreeMapper.getDeviceAndDeviceTreeListByUser(deviceAndDeviceTreeListByUserParamDto);
        // } else {
        GetDeviceAndDeviceTreeListByOrgParamDto deviceAndDeviceTreeListByOrgParamDto = new GetDeviceAndDeviceTreeListByOrgParamDto();
        deviceAndDeviceTreeListByOrgParamDto.setOrgId(dto.getOrgId());
        deviceAndDeviceTreeListByOrgParamDto.setAppScene(dto.getAppScene());
        deviceAndDeviceTreeListByOrgParamDto.setDeviceType(SrvCameraPlatformTypeEnum.TYPE_1.getDicId());
        deviceAndDeviceTreeListByUserResultDtoList = visualDeviceTreeMapper.getDeviceAndDeviceTreeListByOrg(deviceAndDeviceTreeListByOrgParamDto);
        // }
        // 获取客流统计算法ID
        List<AlgorithmManageEntity> algorithmManageEntityList = new ArrayList<>();
        // 1.获取算法列表
        DtoResult<List<AlgorithmManageEntity>> resultT1 = sceneInfoService.getAlgorithmList();
        if (resultT1.success()) {
            algorithmManageEntityList = resultT1.getData();
        }
        Optional<AlgorithmManageEntity> optional = algorithmManageEntityList.stream().filter(algorithmManageEntity -> algorithmManageEntity.getCode().equals("KLTJ")).findFirst();

        Map<Long, Integer> map = new LinkedHashMap<>();
        List<GetStatisticsResultDto> statisticsResultDtoList = new ArrayList<>();

        Calendar calendar = Calendar.getInstance();
        Date currentDate = new Date();

        String begAndEndTime = "";
        switch (dto.getType()) {
            // 统计每个超市的当天客流总量
            case 1:
                String currentDay = DateUtil.format(currentDate, "yyyy-MM-dd");
                begAndEndTime = currentDay;

                if (optional.isPresent()) {
                    GetStatisticsDayParamDto statisticsDayParamDto = new GetStatisticsDayParamDto();
                    statisticsDayParamDto.setCurrentDay(currentDay);
                    statisticsDayParamDto.setAlarmType(optional.get().getId());
                    statisticsResultDtoList = visualAlarmMapper.getStatisticsByDay(statisticsDayParamDto);
                }

                for (SupermarketEntity supermarketEntity : supermarketEntityList) {
                    int sumCount = 0;
                    for (GetDeviceAndDeviceTreeListByUserResultDto deviceAndDeviceTreeListByUserResultDto : deviceAndDeviceTreeListByUserResultDtoList) {
                        // 判断该设备是否在该超市
                        String deviceTreeIdChain = deviceAndDeviceTreeListByUserResultDto.getDeviceTreeIdChain();
                        if (StringUtil.isEmpty(deviceTreeIdChain)) {
                            continue;
                        }
                        // 使用Set避免重复ID
                        Set<Long> deviceTreeIdSet = Arrays.stream(deviceTreeIdChain.split(","))
                                .map(s -> Long.parseLong(s.trim()))
                                .collect(Collectors.toSet());
                        if (!deviceTreeIdSet.contains(supermarketEntity.getVisualDeviceTreeId())) {
                            continue;
                        }
                        List<GetStatisticsResultDto> filterStatisticsResultDtoList = statisticsResultDtoList.stream()
                                .filter(trendByHourResultDto -> trendByHourResultDto.getDeviceId().equals(deviceAndDeviceTreeListByUserResultDto.getDeviceId())).collect(Collectors.toList());
                        if (CollectionUtil.isEmpty(filterStatisticsResultDtoList)) {
                            continue;
                        }
                        for (GetStatisticsResultDto statisticsResultDto : filterStatisticsResultDtoList) {
                            sumCount += statisticsResultDto.getCount();
                        }
                    }
                    map.put(supermarketEntity.getId(), sumCount);
                }
                break;
            // 统计每个超市的当周客流总量
            case 2:
                int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
                calendar.add(Calendar.DATE, -dayOfWeek + 2);
                Date firstDay = calendar.getTime();

                calendar.add(Calendar.DATE, 6);
                Date lastDay = calendar.getTime();

                String firstDayStr = DateUtil.format(firstDay, "yyyy-MM-dd");
                String lastDayStr = DateUtil.format(lastDay, "yyyy-MM-dd");
                begAndEndTime = firstDayStr + "至" + lastDayStr;

                if (optional.isPresent()) {
                    GetStatisticsDayParamDto statisticsDayParamDto = new GetStatisticsDayParamDto();
                    statisticsDayParamDto.setBegDay(firstDayStr);
                    statisticsDayParamDto.setEndDay(lastDayStr);
                    statisticsDayParamDto.setAlarmType(optional.get().getId());
                    statisticsResultDtoList = visualAlarmMapper.getStatisticsByDay(statisticsDayParamDto);
                }

                for (SupermarketEntity supermarketEntity : supermarketEntityList) {
                    int sumCount = 0;
                    for (GetDeviceAndDeviceTreeListByUserResultDto deviceAndDeviceTreeListByUserResultDto : deviceAndDeviceTreeListByUserResultDtoList) {
                        // 判断该设备是否在该超市
                        String deviceTreeIdChain = deviceAndDeviceTreeListByUserResultDto.getDeviceTreeIdChain();
                        if (StringUtil.isEmpty(deviceTreeIdChain)) {
                            continue;
                        }
                        // 使用Set避免重复ID
                        Set<Long> deviceTreeIdSet = Arrays.stream(deviceTreeIdChain.split(","))
                                .map(s -> Long.parseLong(s.trim()))
                                .collect(Collectors.toSet());
                        if (!deviceTreeIdSet.contains(supermarketEntity.getVisualDeviceTreeId())) {
                            continue;
                        }
                        List<GetStatisticsResultDto> filterStatisticsResultDtoList = statisticsResultDtoList.stream()
                                .filter(trendByHourResultDto -> trendByHourResultDto.getDeviceId().equals(deviceAndDeviceTreeListByUserResultDto.getDeviceId())).collect(Collectors.toList());
                        if (CollectionUtil.isEmpty(filterStatisticsResultDtoList)) {
                            continue;
                        }
                        for (GetStatisticsResultDto statisticsResultDto : filterStatisticsResultDtoList) {
                            sumCount += statisticsResultDto.getCount();
                        }
                    }
                    map.put(supermarketEntity.getId(), sumCount);
                }
                break;
            // 统计每个超市的当月客流总量
            case 3:
                String currentMonth = DateUtil.format(currentDate, "yyyy-MM");
                begAndEndTime = currentMonth;

                if (optional.isPresent()) {
                    GetStatisticsByMonthParamDto statisticsByMonthParamDto = new GetStatisticsByMonthParamDto();
                    statisticsByMonthParamDto.setCurrentMonth(currentMonth);
                    statisticsByMonthParamDto.setAlarmType(optional.get().getId());
                    statisticsResultDtoList = visualAlarmMapper.getStatisticsByMonth(statisticsByMonthParamDto);
                }

                for (SupermarketEntity supermarketEntity : supermarketEntityList) {
                    Map<Long, Integer> innerMap = new LinkedHashMap<>();
                    int sumCount = 0;
                    for (GetDeviceAndDeviceTreeListByUserResultDto deviceAndDeviceTreeListByUserResultDto : deviceAndDeviceTreeListByUserResultDtoList) {
                        // 判断该设备是否在该超市
                        String deviceTreeIdChain = deviceAndDeviceTreeListByUserResultDto.getDeviceTreeIdChain();
                        if (StringUtil.isEmpty(deviceTreeIdChain)) {
                            continue;
                        }
                        // 使用Set避免重复ID
                        Set<Long> deviceTreeIdSet = Arrays.stream(deviceTreeIdChain.split(","))
                                .map(s -> Long.parseLong(s.trim()))
                                .collect(Collectors.toSet());
                        if (!deviceTreeIdSet.contains(supermarketEntity.getVisualDeviceTreeId())) {
                            continue;
                        }
                        List<GetStatisticsResultDto> filterStatisticsResultDtoList = statisticsResultDtoList.stream()
                                .filter(trendByHourResultDto -> trendByHourResultDto.getDeviceId().equals(deviceAndDeviceTreeListByUserResultDto.getDeviceId())).collect(Collectors.toList());
                        if (CollectionUtil.isEmpty(filterStatisticsResultDtoList)) {
                            continue;
                        }
                        for (GetStatisticsResultDto statisticsResultDto : filterStatisticsResultDtoList) {
                            sumCount += statisticsResultDto.getCount();
                        }
                    }
                    map.put(supermarketEntity.getId(), sumCount);
                }
                break;
            // 自定义时间查询
            case 4:
                String begTime = dto.getBegTime();
                String endTime = dto.getEndTime();
                if (StringUtil.isEmpty(begTime) || StringUtil.isEmpty(endTime)) {
                    return DtoResult.error("请选择查询时间");
                }
                begAndEndTime = begTime + "至" + endTime;

                if (optional.isPresent()) {
                    GetStatisticsDayParamDto statisticsDayParamDto = new GetStatisticsDayParamDto();
                    statisticsDayParamDto.setBegDay(begTime);
                    statisticsDayParamDto.setEndDay(endTime);
                    statisticsDayParamDto.setAlarmType(optional.get().getId());
                    statisticsResultDtoList = visualAlarmMapper.getStatisticsByDay(statisticsDayParamDto);
                }

                for (SupermarketEntity supermarketEntity : supermarketEntityList) {
                    int sumCount = 0;
                    for (GetDeviceAndDeviceTreeListByUserResultDto deviceAndDeviceTreeListByUserResultDto : deviceAndDeviceTreeListByUserResultDtoList) {
                        // 判断该设备是否在该超市
                        String deviceTreeIdChain = deviceAndDeviceTreeListByUserResultDto.getDeviceTreeIdChain();
                        if (StringUtil.isEmpty(deviceTreeIdChain)) {
                            continue;
                        }
                        // 使用Set避免重复ID
                        Set<Long> deviceTreeIdSet = Arrays.stream(deviceTreeIdChain.split(","))
                                .map(s -> Long.parseLong(s.trim()))
                                .collect(Collectors.toSet());
                        if (!deviceTreeIdSet.contains(supermarketEntity.getVisualDeviceTreeId())) {
                            continue;
                        }
                        List<GetStatisticsResultDto> filterStatisticsResultDtoList = statisticsResultDtoList.stream()
                                .filter(trendByHourResultDto -> trendByHourResultDto.getDeviceId().equals(deviceAndDeviceTreeListByUserResultDto.getDeviceId())).collect(Collectors.toList());
                        if (CollectionUtil.isEmpty(filterStatisticsResultDtoList)) {
                            continue;
                        }
                        for (GetStatisticsResultDto statisticsResultDto : filterStatisticsResultDtoList) {
                            sumCount += statisticsResultDto.getCount();
                        }
                    }
                    map.put(supermarketEntity.getId(), sumCount);
                }
                break;
            default:
                break;
        }

        Map<Long, Integer> sortedMap;
        if (Objects.equals(dto.getSort(), 1)) {
            // 根据值从大到小排序
            sortedMap = map.entrySet().stream()
                    .sorted(Map.Entry.comparingByValue(Comparator.reverseOrder()))
                    .limit(limit ? 10 : map.size())
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            Map.Entry::getValue,
                            (oldValue, newValue) -> oldValue, // 如果键冲突，保留旧值
                            LinkedHashMap::new // 使用LinkedHashMap来保持插入顺序（这里是排序后的顺序）
                    ));
        } else {
            // 根据值从大到小排序
            sortedMap = map.entrySet().stream()
                    .sorted(Map.Entry.comparingByValue())
                    .limit(limit ? 10 : map.size())
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            Map.Entry::getValue,
                            (oldValue, newValue) -> oldValue, // 如果键冲突，保留旧值
                            LinkedHashMap::new // 使用LinkedHashMap来保持插入顺序（这里是排序后的顺序）
                    ));
        }
        // 将键的集合转换为列表，并保持顺序
        List<Long> keys = new ArrayList<>(sortedMap.keySet());

        // 对键的列表进行倒序排序（自然顺序）
        Collections.reverse(keys);

        Map<Long, Integer> resultSortedMap = new LinkedHashMap<>();
        // 遍历倒序的键列表，并输出对应的值
        for (Long key : keys) {
            resultSortedMap.put(key, sortedMap.get(key));
        }

        List<SupermarketPassengerFlowStatisticsStatisticsVo> resultList = new ArrayList<>();
        // 输出排序后的Map（这里只是键和对应的和）
        for (Map.Entry<Long, Integer> entrySortedMap : resultSortedMap.entrySet()) {
            SupermarketPassengerFlowStatisticsStatisticsVo vo = new SupermarketPassengerFlowStatisticsStatisticsVo();
            vo.setId(entrySortedMap.getKey());

            Optional<SupermarketEntity> optionalSupermarket = supermarketEntityList.stream().filter(supermarketEntity -> supermarketEntity.getId().equals(entrySortedMap.getKey())).findFirst();
            if (optionalSupermarket.isPresent()) {
                SupermarketEntity supermarketEntity = optionalSupermarket.get();
                vo.setX(supermarketEntity.getName());
            } else {
                vo.setX(String.valueOf(entrySortedMap.getKey()));
            }
            vo.setY(String.valueOf(entrySortedMap.getValue()));
            vo.setBegAndEndTime(begAndEndTime);
            resultList.add(vo);
        }
        return DtoResult.ok(resultList);
    }

    @Override
    public void getStatisticsExcelExport(HttpServletResponse response, SupermarketPassengerFlowStatisticsStatisticsDto dto) {
        try {
            DtoResult<List<SupermarketPassengerFlowStatisticsStatisticsVo>> dtoResult = this.getStatistics(dto, false);
            List<SupermarketPassengerFlowStatisticsStatisticsVo> supermarketPassengerFlowStatisticsStatisticsVoList = dtoResult.getData();

            List<GetStatisticsExportDayExcel> dataList = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(supermarketPassengerFlowStatisticsStatisticsVoList)) {
                for (SupermarketPassengerFlowStatisticsStatisticsVo supermarketPassengerFlowStatisticsStatisticsVo : supermarketPassengerFlowStatisticsStatisticsVoList) {
                    GetStatisticsExportDayExcel excel = new GetStatisticsExportDayExcel();
                    excel.setBegAndEndTime(supermarketPassengerFlowStatisticsStatisticsVo.getBegAndEndTime());
                    excel.setSupermarketName(supermarketPassengerFlowStatisticsStatisticsVo.getX());
                    excel.setPassengerFlow(String.valueOf(supermarketPassengerFlowStatisticsStatisticsVo.getY()));
                    dataList.add(excel);
                }
            }

            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("UTF-8");
            String yyyymmdd = DateUtil.format(new Date(), "yyyyMMdd");
            String fileName = URLEncoder.encode("获客统计" + yyyymmdd, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Access-Control-Expose-Headers", "fileName");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            response.setHeader("fileName", fileName + ".xlsx");
            HorizontalCellStyleStrategy horizontalCellStyleStrategy = new HorizontalCellStyleStrategy(EasyExcelStyleUtil.getHeadStyle(), EasyExcelStyleUtil.getContentStyle());
            EasyExcel.write(response.getOutputStream(), GetStatisticsExportDayExcel.class)
                    .autoCloseStream(Boolean.FALSE)
                    .sheet("获客统计")
                    .registerWriteHandler(horizontalCellStyleStrategy)
                    .doWrite(dataList);
        } catch (Exception e) {
            log.error("V-LINKER视频融合平台.连锁超市-客流统计-获客趋势..导出错误...msg={}", e.getMessage(), e);
        }
    }

    @Override
    public DtoResult<List<SupermarketPassengerFlowStatisticsTrendVo>> getTrend(SupermarketPassengerFlowStatisticsTrendDto dto, boolean limit) {
        if (null == dto.getType()) {
            return DtoResult.error("请选择统计类型！");
        }
        UserTypeEnum loginUserTypeEnum = userService.getUserType(JwtUtil.getUserId());
        if (JwtUtil.isSupper() || UserTypeEnum.GUAN_LI.equals(loginUserTypeEnum)) {
            if (null == dto.getOrgId()) {
                return DtoResult.error("请选择一个企业！");
            }
        } else {
            dto.setOrgId(JwtUtil.getEnterpriseOrgId());
        }
        List<SupermarketEntity> supermarketEntityList;
        if (null == dto.getSupermarketId()) {
            supermarketEntityList = supermarketService.list(new LambdaQueryWrapper<SupermarketEntity>()
                    .eq(SupermarketEntity::getOrgId, dto.getOrgId())
                    .eq(SupermarketEntity::getAppScene, dto.getAppScene())
            );
        } else {
            SupermarketEntity supermarketEntity = Optional.ofNullable(supermarketService.getById(dto.getSupermarketId())).orElseThrow(() -> new BizRuntimeException("该超市不存在！"));
            supermarketEntityList = Lists.newArrayList(supermarketEntity);
        }
        if (CollectionUtil.isEmpty(supermarketEntityList)) {
            return DtoResult.ok();
        }
        List<GetDeviceAndDeviceTreeListByUserResultDto> deviceAndDeviceTreeListByUserResultDtoList;
        // 如果是企业子账户
        // if (UserTypeEnum.ZI_ZHANG_HU.equals(loginUserTypeEnum)) {
        //     GetDeviceAndDeviceTreeListByUserParamDto deviceAndDeviceTreeListByUserParamDto = new GetDeviceAndDeviceTreeListByUserParamDto();
        //     deviceAndDeviceTreeListByUserParamDto.setUserId(JwtUtil.getUserId());
        //     deviceAndDeviceTreeListByUserParamDto.setOrgId(dto.getOrgId());
        //     deviceAndDeviceTreeListByUserParamDto.setAppScene(dto.getAppScene());
        //     deviceAndDeviceTreeListByUserParamDto.setDeviceType(SrvCameraPlatformTypeEnum.TYPE_1.getDicId());
        //     deviceAndDeviceTreeListByUserResultDtoList = visualDeviceTreeMapper.getDeviceAndDeviceTreeListByUser(deviceAndDeviceTreeListByUserParamDto);
        // } else {
        GetDeviceAndDeviceTreeListByOrgParamDto deviceAndDeviceTreeListByOrgParamDto = new GetDeviceAndDeviceTreeListByOrgParamDto();
        deviceAndDeviceTreeListByOrgParamDto.setOrgId(dto.getOrgId());
        deviceAndDeviceTreeListByOrgParamDto.setAppScene(dto.getAppScene());
        deviceAndDeviceTreeListByOrgParamDto.setDeviceType(SrvCameraPlatformTypeEnum.TYPE_1.getDicId());
        deviceAndDeviceTreeListByUserResultDtoList = visualDeviceTreeMapper.getDeviceAndDeviceTreeListByOrg(deviceAndDeviceTreeListByOrgParamDto);
        // }
        // 获取客流统计算法ID
        List<AlgorithmManageEntity> algorithmManageEntityList = new ArrayList<>();
        // 1.获取算法列表
        DtoResult<List<AlgorithmManageEntity>> resultT1 = sceneInfoService.getAlgorithmList();
        if (resultT1.success()) {
            algorithmManageEntityList = resultT1.getData();
        }
        Optional<AlgorithmManageEntity> optional = algorithmManageEntityList.stream().filter(algorithmManageEntity -> algorithmManageEntity.getCode().equals("KLTJ")).findFirst();

        Map<Long, Map<String, Integer>> map = new HashMap<>();
        List<GetTrendResultDto> trendByResultDtoList = new ArrayList<>();

        Calendar calendar = Calendar.getInstance();
        Date currentDate = new Date();
        switch (dto.getType()) {
            // 天
            case 1:
                if (optional.isPresent()) {
                    GetTrendByHourParamDto hourParamDto = new GetTrendByHourParamDto();
                    hourParamDto.setDay(DateUtil.format(currentDate, "yyyy-MM-dd"));
                    hourParamDto.setAlarmType(optional.get().getId());
                    trendByResultDtoList = visualAlarmMapper.getTrendByHour(hourParamDto);
                }

                // 24小时制
                int currentHour = calendar.get(Calendar.HOUR_OF_DAY) + 1;
                for (SupermarketEntity supermarketEntity : supermarketEntityList) {
                    Map<String, Integer> innerMap = new LinkedHashMap<>();
                    for (int i = 1; i <= currentHour; i++) {
                        int sumCount = 0;
                        for (GetDeviceAndDeviceTreeListByUserResultDto deviceAndDeviceTreeListByUserResultDto : deviceAndDeviceTreeListByUserResultDtoList) {
                            // 判断该设备是否在该超市
                            String deviceTreeIdChain = deviceAndDeviceTreeListByUserResultDto.getDeviceTreeIdChain();
                            if (StringUtil.isEmpty(deviceTreeIdChain)) {
                                continue;
                            }
                            // 使用Set避免重复ID
                            Set<Long> deviceTreeIdSet = Arrays.stream(deviceTreeIdChain.split(","))
                                    .map(s -> Long.parseLong(s.trim()))
                                    .collect(Collectors.toSet());
                            if (!deviceTreeIdSet.contains(supermarketEntity.getVisualDeviceTreeId())) {
                                continue;
                            }
                            List<GetTrendResultDto> getTrendResultDtoList = trendByResultDtoList.stream()
                                    .filter(trendByHourResultDto -> trendByHourResultDto.getDeviceId().equals(deviceAndDeviceTreeListByUserResultDto.getDeviceId())).collect(Collectors.toList());
                            if (CollectionUtil.isEmpty(getTrendResultDtoList)) {
                                continue;
                            }
                            for (GetTrendResultDto getTrendResultDto : getTrendResultDtoList) {
                                if (new BigDecimal(i).compareTo(new BigDecimal(Integer.parseInt(getTrendResultDto.getDateTime()) + 1)) == 0) {
                                    sumCount += getTrendResultDto.getCount();
                                }
                            }
                        }
                        innerMap.put(String.valueOf(i), sumCount);
                    }
                    map.put(supermarketEntity.getId(), innerMap);
                }
                break;
            // 周
            case 2:
                int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
                calendar.add(Calendar.DATE, -dayOfWeek + 2);
                Date firstDay = calendar.getTime();

                calendar.add(Calendar.DATE, 6);
                Date lastDay = calendar.getTime();

                if (optional.isPresent()) {
                    GetTrendByDayParamDto dayParamDto = new GetTrendByDayParamDto();
                    dayParamDto.setBegDay(DateUtil.format(firstDay, "yyyy-MM-dd"));
                    dayParamDto.setEndDay(DateUtil.format(lastDay, "yyyy-MM-dd"));
                    dayParamDto.setAlarmType(optional.get().getId());
                    trendByResultDtoList = visualAlarmMapper.getTrendByDay(dayParamDto);
                }

                for (SupermarketEntity supermarketEntity : supermarketEntityList) {
                    Map<String, Integer> innerMap = new LinkedHashMap<>();

                    calendar.setTime(firstDay);
                    while ((calendar.getTime().before(lastDay) || calendar.getTime().equals(lastDay)) && calendar.getTime().compareTo(currentDate) <= 0) {
                        String day = DateUtil.format(calendar.getTime(), "yyyy-MM-dd");
                        calendar.add(Calendar.DATE, 1);

                        int sumCount = 0;
                        for (GetDeviceAndDeviceTreeListByUserResultDto deviceAndDeviceTreeListByUserResultDto : deviceAndDeviceTreeListByUserResultDtoList) {
                            // 判断该设备是否在该超市
                            String deviceTreeIdChain = deviceAndDeviceTreeListByUserResultDto.getDeviceTreeIdChain();
                            if (StringUtil.isEmpty(deviceTreeIdChain)) {
                                continue;
                            }
                            // 使用Set避免重复ID
                            Set<Long> deviceTreeIdSet = Arrays.stream(deviceTreeIdChain.split(","))
                                    .map(s -> Long.parseLong(s.trim()))
                                    .collect(Collectors.toSet());
                            if (!deviceTreeIdSet.contains(supermarketEntity.getVisualDeviceTreeId())) {
                                continue;
                            }
                            List<GetTrendResultDto> getTrendResultDtoList = trendByResultDtoList.stream()
                                    .filter(trendByHourResultDto -> trendByHourResultDto.getDeviceId().equals(deviceAndDeviceTreeListByUserResultDto.getDeviceId())).collect(Collectors.toList());
                            if (CollectionUtil.isEmpty(getTrendResultDtoList)) {
                                continue;
                            }
                            for (GetTrendResultDto getTrendResultDto : getTrendResultDtoList) {
                                if (Objects.equals(getTrendResultDto.getDateTime(), day)) {
                                    sumCount += getTrendResultDto.getCount();
                                }
                            }
                        }
                        innerMap.put(day, sumCount);
                    }
                    map.put(supermarketEntity.getId(), innerMap);
                }
                break;
            // 月
            case 3:
                DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

                // 获取当前时间
                LocalDate nowDate = LocalDate.now();
                // 获取当月第一天的日期
                LocalDate firstDayOfMonth = nowDate.withDayOfMonth(1);
                // 获取当月最后一天的日期
                LocalDate lastDayOfMonth = nowDate.with(TemporalAdjusters.lastDayOfMonth());

                String firstMonthDay = firstDayOfMonth.format(dateTimeFormatter);
                String lastMonthDay = lastDayOfMonth.format(dateTimeFormatter);

                if (optional.isPresent()) {
                    GetTrendByDayParamDto dayMonthParamDto = new GetTrendByDayParamDto();
                    dayMonthParamDto.setBegDay(firstMonthDay);
                    dayMonthParamDto.setEndDay(lastMonthDay);
                    dayMonthParamDto.setAlarmType(optional.get().getId());
                    trendByResultDtoList = visualAlarmMapper.getTrendByDay(dayMonthParamDto);
                }

                for (SupermarketEntity supermarketEntity : supermarketEntityList) {
                    Map<String, Integer> innerMap = new LinkedHashMap<>();

                    for (LocalDate date = firstDayOfMonth; date.isBefore(lastDayOfMonth.plusDays(1)) && !date.isAfter(nowDate); date = date.plusDays(1)) {
                        String day = date.format(dateTimeFormatter);

                        int sumCount = 0;
                        for (GetDeviceAndDeviceTreeListByUserResultDto deviceAndDeviceTreeListByUserResultDto : deviceAndDeviceTreeListByUserResultDtoList) {
                            // 判断该设备是否在该超市
                            String deviceTreeIdChain = deviceAndDeviceTreeListByUserResultDto.getDeviceTreeIdChain();
                            if (StringUtil.isEmpty(deviceTreeIdChain)) {
                                continue;
                            }
                            // 使用Set避免重复ID
                            Set<Long> deviceTreeIdSet = Arrays.stream(deviceTreeIdChain.split(","))
                                    .map(s -> Long.parseLong(s.trim()))
                                    .collect(Collectors.toSet());
                            if (!deviceTreeIdSet.contains(supermarketEntity.getVisualDeviceTreeId())) {
                                continue;
                            }
                            List<GetTrendResultDto> getTrendResultDtoList = trendByResultDtoList.stream()
                                    .filter(trendByHourResultDto -> trendByHourResultDto.getDeviceId().equals(deviceAndDeviceTreeListByUserResultDto.getDeviceId())).collect(Collectors.toList());
                            if (CollectionUtil.isEmpty(getTrendResultDtoList)) {
                                continue;
                            }
                            for (GetTrendResultDto getTrendResultDto : getTrendResultDtoList) {
                                if (Objects.equals(getTrendResultDto.getDateTime(), day)) {
                                    sumCount += getTrendResultDto.getCount();
                                }
                            }
                        }
                        innerMap.put(day, sumCount);
                    }
                    map.put(supermarketEntity.getId(), innerMap);
                }
                break;
            default:
                break;
        }

        // 遍历外部Map，计算每个内部Map的value之和，并排序
        List<Map.Entry<Long, Integer>> entriesWithSumList = map.entrySet().stream()
                .map(entry -> new AbstractMap.SimpleEntry<>(entry.getKey(), entry.getValue().values().stream().mapToInt(Integer::intValue).sum()))
                .sorted(Map.Entry.comparingByValue(Comparator.reverseOrder())) // 默认从大到小排序
                .collect(Collectors.toList());

        // 如果需要从小到大排序，可以在这里添加逻辑（但您可能不需要这个if-else）
        if (!Objects.equals(dto.getSort(), 1)) {
            entriesWithSumList.sort(Map.Entry.comparingByValue());
        }

        // 只需要前5个元素
        Map<Long, Integer> sortedMap;
        if (limit) {
            sortedMap = entriesWithSumList.stream()
                    .limit(5)
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (oldValue, newValue) -> oldValue, LinkedHashMap::new));
        } else {
            sortedMap = entriesWithSumList.stream()
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (oldValue, newValue) -> oldValue, LinkedHashMap::new));
        }

        // 将键的集合转换为列表，并保持顺序
        List<Long> keys = new ArrayList<>(sortedMap.keySet());

        // 对键的列表进行倒序排序（自然顺序）
        Collections.reverse(keys);

        Map<Long, Integer> resultSortedMap = new LinkedHashMap<>();
        // 遍历倒序的键列表，并输出对应的值
        for (Long key : keys) {
            resultSortedMap.put(key, sortedMap.get(key));
        }

        List<SupermarketPassengerFlowStatisticsTrendVo> resultList = new ArrayList<>();
        // 输出排序后的Map（这里只是键和对应的和）
        for (Map.Entry<Long, Integer> entrySortedMap : resultSortedMap.entrySet()) {
            int total = 0;
            SupermarketPassengerFlowStatisticsTrendVo vo = new SupermarketPassengerFlowStatisticsTrendVo();
            List<SupermarketPassengerFlowStatisticsTrendVo.SupermarketPassengerFlowStatisticsTrendByHourInfo> points = new ArrayList<>();

            Map<String, Integer> innerMap = map.get(entrySortedMap.getKey());
            for (Map.Entry<String, Integer> entryInnerMap : innerMap.entrySet()) {
                SupermarketPassengerFlowStatisticsTrendVo.SupermarketPassengerFlowStatisticsTrendByHourInfo info = new SupermarketPassengerFlowStatisticsTrendVo.SupermarketPassengerFlowStatisticsTrendByHourInfo();
                info.setX(entryInnerMap.getKey().length() <= 2 ? entryInnerMap.getKey() + ":00" : entryInnerMap.getKey());
                info.setY(String.valueOf(entryInnerMap.getValue()));
                points.add(info);

                total += entryInnerMap.getValue();
            }

            vo.setSupermarketId(entrySortedMap.getKey());
            Optional<SupermarketEntity> optionalSupermarket = supermarketEntityList.stream().filter(supermarketEntity -> supermarketEntity.getId().equals(entrySortedMap.getKey())).findFirst();
            if (optionalSupermarket.isPresent()) {
                SupermarketEntity supermarketEntity = optionalSupermarket.get();
                vo.setSupermarketName(supermarketEntity.getName());
            }
            vo.setPoints(points);
            vo.setTotal(total);
            resultList.add(vo);
        }
        return DtoResult.ok(resultList);
    }

    @Override
    public void getTrendExcelExport(HttpServletResponse response, SupermarketPassengerFlowStatisticsTrendDto dto) {
        OutputStream outputStream = null;
        // 创建一个excel
        try (HSSFWorkbook workbook = new HSSFWorkbook()) {
            String yyyymmdd = DateUtil.format(new Date(), "yyyyMMdd");
            String fileName = URLEncoder.encode("获客趋势" + yyyymmdd, "UTF-8").replaceAll("\\+", "%20");

            DtoResult<List<SupermarketPassengerFlowStatisticsTrendVo>> dtoResult = this.getTrend(dto, false);
            List<SupermarketPassengerFlowStatisticsTrendVo> supermarketPassengerFlowStatisticsTrendVoList = dtoResult.getData();

            List<String> xList = new ArrayList<>();
            List<List<String>> yList = new ArrayList<>();

            if (CollectionUtil.isNotEmpty(supermarketPassengerFlowStatisticsTrendVoList)) {
                for (int i = 0; i < supermarketPassengerFlowStatisticsTrendVoList.size(); i++) {
                    SupermarketPassengerFlowStatisticsTrendVo supermarketPassengerFlowStatisticsTrendVo = supermarketPassengerFlowStatisticsTrendVoList.get(i);
                    List<String> dataList = new ArrayList<>();
                    dataList.add(supermarketPassengerFlowStatisticsTrendVo.getSupermarketName());

                    List<SupermarketPassengerFlowStatisticsTrendVo.SupermarketPassengerFlowStatisticsTrendByHourInfo> points = supermarketPassengerFlowStatisticsTrendVo.getPoints();
                    for (SupermarketPassengerFlowStatisticsTrendVo.SupermarketPassengerFlowStatisticsTrendByHourInfo point : points) {
                        if (i == 0) {
                            xList.add(point.getX());
                        }
                        dataList.add(point.getY());
                    }
                    dataList.add(String.valueOf(supermarketPassengerFlowStatisticsTrendVo.getTotal()));
                    yList.add(dataList);
                }
                xList.add("总计");
            }

            HSSFSheet sheet = workbook.createSheet();// 创建一个excel的sheet
            workbook.setSheetName(0, "获客趋势");
            // 设置居中样式
            HSSFCellStyle cellStyle = workbook.createCellStyle();
            cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);// 设置垂直居中
            cellStyle.setAlignment(HorizontalAlignment.CENTER);// 设置水平居中
            cellStyle.setBorderBottom(BorderStyle.THIN); // 下边框
            cellStyle.setBorderLeft(BorderStyle.THIN);// 左边框
            cellStyle.setBorderTop(BorderStyle.THIN);// 上边框
            cellStyle.setBorderRight(BorderStyle.THIN);// 右边框

            // 设置居中样式
            HSSFCellStyle cellStyle2 = workbook.createCellStyle();
            cellStyle2.setVerticalAlignment(VerticalAlignment.CENTER);// 设置垂直居中
            cellStyle2.setAlignment(HorizontalAlignment.CENTER);// 设置水平居中
            cellStyle2.setBorderBottom(BorderStyle.THIN); // 下边框
            cellStyle2.setBorderLeft(BorderStyle.THIN);// 左边框
            cellStyle2.setBorderTop(BorderStyle.THIN);// 上边框
            cellStyle2.setBorderRight(BorderStyle.THIN);// 右边框
            // 设置自动换行
            cellStyle2.setWrapText(true);

            // 设置1,2列列宽
            sheet.setColumnWidth(0, 30 * 128);

            // 设置背景色样式
            HSSFCellStyle backgroundStyle = workbook.createCellStyle();
            // 设置填充样式
            backgroundStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            // 设置背景色
            backgroundStyle.setFillForegroundColor(HSSFColor.HSSFColorPredefined.GREY_25_PERCENT.getIndex());
            backgroundStyle.setBorderBottom(BorderStyle.THIN); // 下边框
            backgroundStyle.setBorderLeft(BorderStyle.THIN);// 左边框
            backgroundStyle.setBorderTop(BorderStyle.THIN);// 上边框
            backgroundStyle.setBorderRight(BorderStyle.THIN);// 右边框
            // 设置字体
            Font font = workbook.createFont();
            // 字体样式
            font.setFontName("黑体");
            // 字体大小
            font.setFontHeightInPoints((short) 12);
            // 是否加粗
            font.setBold(false);
            cellStyle.setFont(font);
            // 设置自动换行
            cellStyle.setWrapText(true);

            // 创建第一行
            HSSFRow row1 = sheet.createRow(0);
            row1.setHeightInPoints((short) 30);
            for (int i = 0; i < xList.size(); i++) {
                sheet.setColumnWidth(i + 1, 30 * 100);

                HSSFCell hssfCell = row1.createCell(i + 1);
                hssfCell.setCellStyle(cellStyle);
                hssfCell.setCellValue(xList.get(i));
            }

            for (int i = 0; i < yList.size(); i++) {
                // 创建第二行
                HSSFRow row2 = sheet.createRow(i + 1);
                row2.setHeightInPoints((short) 30);

                List<String> dataList = yList.get(i);
                for (int j = 0; j < dataList.size(); j++) {
                    HSSFCell cell_2_1 = row2.createCell(j);// 创建第一行第一列
                    // 设置合并后的style
                    cell_2_1.setCellStyle(j == 0 ? cellStyle : cellStyle2);
                    // 填充数据
                    cell_2_1.setCellValue(dataList.get(j));
                }
            }

            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Access-Control-Expose-Headers", "fileName");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            response.setHeader("fileName", fileName + ".xlsx");
            outputStream = response.getOutputStream();
            workbook.write(outputStream);
        } catch (
                Exception e) {
            log.error("V-LINKER视频融合平台.连锁超市-客流统计-获客趋势..导出错误...msg={}", e.getMessage(), e);
        } finally {
            if (outputStream != null) {
                try {
                    outputStream.flush();
                    outputStream.close();
                } catch (IOException e) {
                    log.error("V-LINKER视频融合平台.连锁超市-客流统计-获客趋势..导出错误...msg={}", e.getMessage(), e);
                }
            }
        }
    }
}