package com.saida.services.system.visual.biz.comtom.pojo.req;

import com.saida.services.system.visual.req.CommonBaseReq;
import lombok.Data;

import java.util.List;

@Data
public class SendBroadcastRes extends CommonBaseReq {
    private List<String> deviceId;

    private Long id;

    /**
     * 内容
     */
    private String content;
    /**
     * 音频文件
     */
    private String contentFile;

    private String contentFileName;


    private Integer loopTimes = 1;

    // 1男 2女

    private Integer sex;

    //持续时间 单位s
    private Integer duration;

    //文件ids
    private String fileId;
    //康通文件id
    private String broadcastFileId;

    private Integer volume = 10;

}
