package com.saida.services.system.sys.controller;

import com.saida.services.common.base.DtoResult;
import com.saida.services.jpush.JiGuangPushBean;
import com.saida.services.jpush.JiGuangPushService;
import com.saida.services.srv.entity.SrvSysUserJgEntity;
import com.saida.services.system.sys.service.SysUserJgService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


@RestController
@RequestMapping("/user/jg")
public class SysUserJgController {

    @Resource
    private SysUserJgService sysUserJgService;
    @Resource
    private JiGuangPushService jiGuangPushService;

    @PostMapping("/add")
    public DtoResult<Void> add(@RequestBody SrvSysUserJgEntity entity) {
        return sysUserJgService.add(entity);
    }

    @PostMapping("/delete")
    public DtoResult<Void> delete(@RequestBody SrvSysUserJgEntity entity) {
        return sysUserJgService.delete(entity);
    }

    @PostMapping("/push")
    public DtoResult<Void> push(@RequestBody SrvSysUserJgEntity entity) {
        JiGuangPushBean jiGuangPushBean = new JiGuangPushBean();
        jiGuangPushBean.setTitle("测试推送标题");
        jiGuangPushBean.setContent("测试推送内容");
        boolean flag = jiGuangPushService.sendPushByAlias(jiGuangPushBean, "1705477296068825090");
        return flag ? DtoResult.ok() : DtoResult.error("推送失败");
    }
}