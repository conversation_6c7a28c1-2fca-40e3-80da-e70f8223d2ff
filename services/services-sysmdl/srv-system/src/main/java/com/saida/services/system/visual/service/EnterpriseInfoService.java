package com.saida.services.system.visual.service;

import com.saida.services.common.base.Result;
import com.saida.services.common.service.IBaseService;
import com.saida.services.entities.base.BaseRequest;
import com.saida.services.system.visual.entity.EnterpriseInfoEntity;

/**
 * 企业信息
 *
 * <AUTHOR>
 * email ${email}
 * date 2023-09-28 16:39:07
 */
public interface EnterpriseInfoService extends IBaseService<EnterpriseInfoEntity> {

    Result listPage(EnterpriseInfoEntity entity, BaseRequest request);

    EnterpriseInfoEntity getInfoById(EnterpriseInfoEntity entity);

    EnterpriseInfoEntity getInfoByOrgId(EnterpriseInfoEntity entity);

    Result saveOrUpdateBean(EnterpriseInfoEntity entity);

    void deleteByIds(String ids);

    Result delEnterpriseInfo(Long orgId);
}

