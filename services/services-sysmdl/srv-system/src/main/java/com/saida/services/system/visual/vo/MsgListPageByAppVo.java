package com.saida.services.system.visual.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;

/**
 * app-消息分页列表
 *
 * @author: yj
 * @date: 2023/10/22 09:01
 */
@Data
public class MsgListPageByAppVo implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;

    private Long deviceId;

    private Integer msgType;

    private String msgTypeName;

    private String content;

    private String deviceName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String createTime;

    private String week;
    private String day;
    private String time;
}