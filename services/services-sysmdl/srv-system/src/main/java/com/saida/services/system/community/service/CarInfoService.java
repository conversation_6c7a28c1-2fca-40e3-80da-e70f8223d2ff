package com.saida.services.system.community.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.saida.services.common.base.Result;
import com.saida.services.system.community.entity.CarInfoEntity;
import com.saida.services.entities.base.BaseRequest;

import java.util.List;
import java.util.Set;

/**
 * 房屋管理
 *
 * <AUTHOR>
 * email ${email}
 * date 2023-10-17 09:54:50
 */
public interface CarInfoService extends IService<CarInfoEntity> {

    Result listPage(CarInfoEntity entity, BaseRequest request);

    List<CarInfoEntity> getList(CarInfoEntity entity);

    CarInfoEntity getInfoById(CarInfoEntity entity);

    Result saveOrUpdateBean(CarInfoEntity entity);

    void deleteByIds(String ids);

    boolean updateDeviceInfo(Set<Long> ids, Long deviceTreeId);

}

