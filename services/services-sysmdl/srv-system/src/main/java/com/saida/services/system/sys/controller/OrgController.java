package com.saida.services.system.sys.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.tree.Tree;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.saida.services.common.base.DtoResult;
import com.saida.services.common.base.Result;
import com.saida.services.common.tools.JwtUtil;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.log.LogOperation;
import com.saida.services.log.LogOperationEnum;
import com.saida.services.log.ModuleEnum;
import com.saida.services.system.sys.entity.OrgEntity;
import com.saida.services.system.sys.pojo.JwtUser;
import com.saida.services.system.sys.service.OrgService;
import com.saida.services.system.visual.entity.EnterpriseInfoEntity;
import com.saida.services.system.visual.service.EnterpriseInfoService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;

@RestController
@RequestMapping("/sys/org")
public class OrgController {

    @Resource
    private OrgService orgService;
    @Resource
    private EnterpriseInfoService enterpriseInfoService;

    @PostMapping("/save")
    @LogOperation(type = LogOperationEnum.ADDOREDIT, func = "组织信息", module = ModuleEnum.ORG)
    public Result save(OrgEntity entity) {
        if (StringUtil.isEmpty(entity.getName())) {
            return Result.error("组织名称必填");
        }
        orgService.addOrUpdate(entity);
        return Result.ok();
    }

    @GetMapping("/getTree")
    public Result getTree(OrgEntity entity) {
        List<Tree<Long>> list = orgService.getTree(entity);
        return Result.ok(list);
    }

    @GetMapping("/getTreeHasEnterpriseNum")
    public Result getTreeHasEnterpriseNum(OrgEntity entity) {
        List<Tree<Long>> list = orgService.getTreeHasEnterpriseNum(entity);
        return Result.ok(list);
    }

    @GetMapping("/getTreeHasRoleNum")
    public Result getTreeHasRoleNum(OrgEntity entity) {
        List<Tree<Long>> list = orgService.getTreeHasRoleNum(entity);
        return Result.ok(list);
    }

    @GetMapping("/getTreeHasUserNum")
    public Result getTreeHasUserNum(OrgEntity entity) {
        List<Tree<Long>> list = orgService.getTreeHasUserNum(entity);
        return Result.ok(list);
    }

    @GetMapping("/getTreeHasParentDeviceNum")
    public Result getTreeHasParentDeviceNum(OrgEntity entity) {
        List<Tree<Long>> list = orgService.getTreeHasParentDeviceNum(entity);
        return Result.ok(list);
    }

    @GetMapping("/listPage")
    public Result listPage(OrgEntity entity) {
        if (entity.getParentId() == null) {
            entity.setParentId(JwtUtil.getOrgId());
        }
        if (entity.getSubLevel()) {
            OrgEntity org = orgService.getById(entity.getParentId());
            if (org != null) {
                entity.setIdChain(org.getIdChain());
                entity.setParentId(null);
            }
        }
        IPage<OrgEntity> page = orgService.listPage(entity);
        return Result.ok(page);
    }

    /**
     * 查询当前以及下级组织列表
     */
    @GetMapping("/getChildList")
    public Result getChildList(OrgEntity entity) {
        JwtUser jwtUser = JwtUtil.getUserInfo();
        Set<Long> orgIdSet = jwtUser.getOrgIdSet();

        List<OrgEntity> list = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(orgIdSet)) {
            for (Long orgId : orgIdSet) {
                List<OrgEntity> childList = orgService.getChildList(orgId, entity.getType(), entity.getName());
                if (CollectionUtil.isNotEmpty(childList)) {
                    list.addAll(childList);
                }
            }
        } else {
            entity.setId(JwtUtil.getOrgId());
            list = orgService.getChildList(entity.getId(), entity.getType(), entity.getName());
        }
        if (CollectionUtil.isNotEmpty(list)) {
            List<EnterpriseInfoEntity> enterpriseInfoEntityList = enterpriseInfoService.list();
            for (OrgEntity org : list) {
                Optional<EnterpriseInfoEntity> enterpriseInfoEntityOptional = enterpriseInfoEntityList.stream().filter(e -> org.getId().equals(e.getOrgId())).findFirst();
                enterpriseInfoEntityOptional.ifPresent(enterpriseInfoEntity -> org.setSceneRenewStatus(enterpriseInfoEntity.getSceneRenewStatus()));
            }
        }
        return Result.ok(list);
    }

    @PostMapping("/delete")
    @LogOperation(type = LogOperationEnum.DELETE, func = "删除组织信息", module = ModuleEnum.ORG)
    public DtoResult<Integer> delete(OrgEntity entity) {
        return orgService.delete(entity);
    }

    @PostMapping("/confirmDelete")
    @LogOperation(type = LogOperationEnum.DELETE, func = "确认删除组织信息", module = ModuleEnum.ORG)
    public DtoResult<Void> confirmDelete(OrgEntity entity) {
        return orgService.confirmDelete(entity);
    }

    @GetMapping("/getInfo")
    public Result getInfo(OrgEntity entity) {
        if (entity.getId() == null) {
            return Result.error("ID必传");
        }
        IPage<OrgEntity> page = orgService.listPage(entity);
        if (page == null || page.getRecords() == null || page.getRecords().isEmpty()) {
            return Result.ok();
        }
        OrgEntity org = page.getRecords().get(0);
        if (org.getParentId() != null) {
            OrgEntity parentOrg = orgService.getById(org.getParentId());
            if (parentOrg != null) {
                org.setParentName(parentOrg.getName());
            }
        }
        return Result.ok(org);
    }
}