package com.saida.services.system.sys.service;

import com.saida.services.common.base.DtoResult;
import com.saida.services.common.service.IBaseService;
import com.saida.services.srv.entity.SrvAppDeviceMsgConfigEntity;

public interface AppDeviceMsgConfigService extends IBaseService<SrvAppDeviceMsgConfigEntity> {

    DtoResult<SrvAppDeviceMsgConfigEntity> info(SrvAppDeviceMsgConfigEntity entity);

    DtoResult<Void> edit(SrvAppDeviceMsgConfigEntity entity);

    DtoResult<Void> delete(SrvAppDeviceMsgConfigEntity entity);
}