package com.saida.services.system.sys.service;

import cn.hutool.core.lang.tree.Tree;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.saida.services.common.base.DtoResult;
import com.saida.services.enumeration.OrgTypeEnum;
import com.saida.services.system.sys.entity.OrgEntity;
import com.saida.services.system.sys.vo.OrgTypeByIdVo;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface OrgService extends IService<OrgEntity> {

    void addOrUpdate(OrgEntity entity);

    List<Tree<Long>> getTree(OrgEntity entity);

    List<Tree<Long>> getTreeHasEnterpriseNum(OrgEntity entity);

    List<Tree<Long>> getTreeHasRoleNum(OrgEntity entity);

    List<Tree<Long>> getTreeHasUserNum(OrgEntity entity);

    List<Tree<Long>> getTreeHasParentDeviceNum(OrgEntity entity);

    IPage<OrgEntity> listPage(OrgEntity entity);

    DtoResult<Integer> delete(OrgEntity entity);

    DtoResult<Void> confirmDelete(OrgEntity entity);

    /**
     * 获取本级以及下级或子级组织ID
     *
     * @param id       组织ID
     * @param subLevel true：包含本级 下级 迭代子级，false: 包含本级 默认false
     */
    List<Long> getChildIds(Long id, Boolean subLevel);

    List<OrgEntity> getChildIdsByOrgType(Long orgParentId, Set<OrgTypeEnum> orgTypeEnumSet);

    /**
     * 获取本级以及下级或子级企业ID
     *
     * @param id 组织ID
     */
    List<Long> getChildEnterpriseIds(Long id, String orgName);

    /**
     * 获取本级以及下级或子级组织ID
     *
     * @param id       组织ID
     * @param subLevel true：包含本级 下级 迭代子级，false: 包含本级 默认false
     * @param orgName  组织/企业名称
     */
    List<Long> getChildIds(Long id, Boolean subLevel, String orgName);

    List<OrgEntity> getOrgList(Collection<Long> ids);

    Map<Long, String> getIdNameMap(Collection<Long> ids);

    Long getEnterpriseOrg(Long orgId);

    /**
     * 查询当前以及下级组织列表
     *
     * @param id   组织ID
     * @param type 组织类型
     * @return
     */
    List<OrgEntity> getChildList(Long id, Long type);

    /**
     * 查询当前以及下级组织列表
     *
     * @param id   组织ID
     * @param type 组织类型
     * @param name 组织名称
     * @return
     */
    List<OrgEntity> getChildList(Long id, Long type, String name);


    /**
     * 比较组织之间的上下级关系
     *
     * @param orgId1
     * @param orgId2
     * @return 0 ：同级或同一组织
     * 1 ：org1 > org2, org1是org2的上级
     * -1 ：org1 < org2, org1是org2的下级
     * -2 ：org1与org2不存在直属的上下级关系
     * -3 ：org1或org2不存在
     */
    Integer compare(Long orgId1, Long orgId2);

    List<OrgEntity> getChildIdsByParentId(Long orgParentId);

    OrgTypeByIdVo getOrgTypeById(Long orgId);
}

