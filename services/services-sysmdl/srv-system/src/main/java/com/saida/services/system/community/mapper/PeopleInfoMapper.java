package com.saida.services.system.community.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.saida.services.system.community.entity.PeopleInfoEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 人口管理
 *
 * <AUTHOR>
 * email ${email}
 * date 2023-10-17 09:54:50
 */
@Mapper
public interface PeopleInfoMapper extends BaseMapper<PeopleInfoEntity> {


    List<PeopleInfoEntity> listPage(@Param("entity") PeopleInfoEntity entity);

    PeopleInfoEntity getInfoById(@Param("entity") PeopleInfoEntity entity);

    List<PeopleInfoEntity> getListByIds(@Param("ids") List<String> ids);

    void updateIsDeleteByIds(@Param("ids") String ids);


    List<PeopleInfoEntity> getListByDeviceTreeId(@Param("deviceTreeId") Long deviceTreeId);



}
