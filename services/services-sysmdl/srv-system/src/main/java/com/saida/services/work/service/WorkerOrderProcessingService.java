package com.saida.services.work.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.saida.services.work.pojo.WorkerOrderProcessingEntity;

import java.util.List;

/**
 * 
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2024-10-15 18:02:28
 */
public interface WorkerOrderProcessingService extends IService<WorkerOrderProcessingEntity> {

    List<WorkerOrderProcessingEntity> getInfoByWorkId(String workId);

    WorkerOrderProcessingEntity holdInfoByWorkId(String workId);
}

