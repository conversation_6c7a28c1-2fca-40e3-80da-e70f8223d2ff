package com.saida.services.system.visual.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * description your class purpose
 *
 * <AUTHOR>
 * @version IotDataVo v1.0.0
 * @since 2025/4/29 17:28
 */
@Data
public class IotDataVo {
    /**
     * 数据上报时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime reportTime;
    /**
     * 字段标识
     */
    private String fieldIdent;
    /**
     * 字段类型名字
     */
    private String fieldName;
    /**
     * 字段值
     */
    private String fieldValue;
    /**
     * 单位
     */
    private String unit;
}
