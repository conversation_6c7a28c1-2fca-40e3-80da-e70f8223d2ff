package com.saida.services.system.visual.biz.vlink.pojo.resp;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class VlinkDeviceListResp implements Serializable {
    private static final long serialVersionUID = 1L;

    @JSONField(name = "DeviceCount")
    private Integer deviceCount;

    @JSONField(name = "Devices")
    private List<Devices> devices;

    @JSONField(name = "TotalCount")
    private Integer totalCount;

    @Data
    public static class Devices implements Serializable {
        private static final long serialVersionUID = 1L;

        /*
         * 设备ID
         */
        @JSONField(name = "DeviceID")
        private Integer deviceID;

        /*
         * 设备名称
         */
        @JSONField(name = "DeviceName")
        private String deviceName;

        /*
         * 端口
         */
        @JSONField(name = "DevicePort")
        private String devicePort;

        /*
         * 设备ip地址
         */
        @JSONField(name = "DeviceIp")
        private String deviceIp;

        /*
         * 设备类型
         */
        @JSONField(name = "DeviceType")
        private String deviceType;

        /*
         * 设备厂商
         */
        @JSONField(name = "Manufacturer")
        private String manufacturer;

        /*
         * 是否启用
         */
        @JSONField(name = "Enable")
        private Integer enable;

        /*
         * 传输协议
         */
        @JSONField(name = "Transport")
        private String transport;

        /*
         * 在线状态（允许值: 0, 1）
         */
        @JSONField(name = "Online")
        private Integer online;

        /*
         * Ehome设备原始id E86162103
         */
        @JSONField(name = "OriginalID")
        private String originalID;
    }

}