package com.saida.services.system.sys.dto;

import com.saida.services.system.sys.entity.UserRoleEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserRoleDto extends UserRoleEntity {

    private Long orgId;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 用户手机号码
     */
    private String userPhone;

    /**
     * 用户姓名
     */
    private String userName;

}
