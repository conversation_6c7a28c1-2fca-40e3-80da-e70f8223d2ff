package com.saida.services.system.visual.biz.tsingsee.pojo.req;

import com.alibaba.fastjson.annotation.JSONField;
import com.saida.services.system.visual.req.CommonBaseReq;
import lombok.Data;

import java.io.Serializable;

/**
 * ClassName: TsingseeLoginReq <br/>
 * Description: <br/>
 * date: 2023/07/20 17:36<br/>
 *
 * <AUTHOR> />
 */
@Data
public class TsingseeBaseTokenReq extends CommonBaseReq implements Serializable {
    private static final long serialVersionUID = 1L;

    @JSONField(name = "token")
    private String token;

    public TsingseeBaseTokenReq() {
    }
}