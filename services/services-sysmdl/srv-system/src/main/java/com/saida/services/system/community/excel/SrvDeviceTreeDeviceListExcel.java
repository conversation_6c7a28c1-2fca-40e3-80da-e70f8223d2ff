package com.saida.services.system.community.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@HeadRowHeight(40)
@ColumnWidth(25)
public class SrvDeviceTreeDeviceListExcel implements Serializable {
    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "设备树编码", index = 0)
    private String deviceTreeId;

    @ExcelProperty(value = "设备名称", index = 1)
    private String deviceTreeName;

    @ExcelProperty(value = "设备编码", index = 2)
    private String deviceCodeCode;

    @ExcelProperty(value = "设备名称", index = 3)
    private String deviceName;

    @ExcelProperty(value = "通道编码", index = 4)
    private String channelCode;

    @ExcelProperty(value = "通道名称", index = 5)
    private String channelName;

    @ExcelProperty(value = "设备类型", index = 6)
    private String deviceTypeName;

    @ExcelProperty(value = "联动广播", index = 7)
    private String bindBroadcastName;

    @ExcelProperty(value = "是否在线", index = 8)
    private String onlineName;

    @ExcelProperty(value = "修改时间", index = 9)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;
}