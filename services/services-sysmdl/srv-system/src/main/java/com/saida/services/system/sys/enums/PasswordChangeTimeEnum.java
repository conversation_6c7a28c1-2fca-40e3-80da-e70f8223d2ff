package com.saida.services.system.sys.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 密码最长时间期限枚举
 *
 * <AUTHOR>
 * @version PasswordChangeTimeEnum v1.0.0
 * @since 2025/3/4 16:54
 */
@Getter
@AllArgsConstructor
public enum PasswordChangeTimeEnum {

    ONE_MONTH(1, "一个月"),
    THREE_MONTH(2, "三个月"),
    SIX_MONTH(3, "六个月"),
    ONE_YEAR(4, "一年")
        ;

    private final Integer code;
    private final String desc;


}
