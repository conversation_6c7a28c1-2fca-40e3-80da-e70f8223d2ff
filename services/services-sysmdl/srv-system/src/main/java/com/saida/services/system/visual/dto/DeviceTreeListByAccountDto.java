package com.saida.services.system.visual.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Set;

/**
 * ClassName: ChannelListDto <br/>
 * Description: <br/>
 * date: 2023/07/27 14:58<br/>
 *
 * <AUTHOR> />
 */
@Getter
@Setter
public class DeviceTreeListByAccountDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /*
     * 组织id
     */
    private Long orgId;

    private Long appScene;

    private Set<Long> appSceneIdSet;

    /*
     * 用户ID
     */
    private Long userId;

    private String deviceName;

    private String channelName;

    private String keyWords;

    /*
     * 设备类型：固定字典camera_platform_type字段，监控设备，广播设备，AI盒子设备
     */
    private Long deviceType;

    private Set<Long> deviceOrgIdSet;

    private Set<Long> deviceTreeIdSet;

    /*
     * 是否自由设备：0-汇聚设备；1-自有设备
     */
    private Integer isOwner;

    private String eventType;

    /*
     * 是否在线：0-离线；1-在线
     */
    private Integer isOnline;

    /**
     * 固定类型
     * 1、北望霸凌终端
     * 2、北望7寸
     * 3、北望10寸
     * @see com.saida.services.iot.enums.IotRegularEnum
     * 有些型号有固定类型  有些没处理就从枚举文件中读取
     */
    private Integer regular;
}