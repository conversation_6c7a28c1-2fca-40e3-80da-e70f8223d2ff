package com.saida.services.system.visual.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.saida.services.common.entity.BaseEntity;
import lombok.*;

import javax.validation.constraints.NotBlank;

/**
 * BC_设备预置点列表
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("device_pre_info_bc")
public class DevicePreInfoBcEntity extends BaseEntity<DevicePreInfoBcEntity> {
    private static final long serialVersionUID = 1L;


    /**
     */
    private Long deviceId;

    /**
     * 名称
     */
    @NotBlank(message = "名称不能为空")
    private String name;

    /**
     * 索引
     */
    private Integer index;

    /**
     * 图片
     */
    private String imgUrl;

}