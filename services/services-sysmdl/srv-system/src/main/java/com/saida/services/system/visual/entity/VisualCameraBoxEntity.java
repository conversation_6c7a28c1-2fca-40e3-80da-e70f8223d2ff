package com.saida.services.system.visual.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.saida.services.common.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * ClassName: BaseEntity <br/>
 * Description: <br/>
 * date: 2023/07/24 14:34<br/>
 *
 * <AUTHOR> />
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName("visual_camera_box")
public class VisualCameraBoxEntity extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /*
     * 监控设备ID
     */
    private Long cameraId;

    /*
     * AI盒子ID
     */
    private Long boxId;
}