package com.saida.services.system.sys.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.saida.services.common.base.DtoResult;
import com.saida.services.common.entity.BasePageInfoEntity;
import com.saida.services.common.tools.JwtUtil;
import com.saida.services.entities.base.BaseRequest;
import com.saida.services.system.sys.entity.OperatorLogEntity;
import com.saida.services.system.sys.entity.OrgEntity;
import com.saida.services.system.sys.mapper.OperatorLogMapper;
import com.saida.services.system.sys.service.OperatorLogService;
import com.saida.services.system.sys.service.OrgService;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;

@Service("operatorLogService")
public class OperatorLogServiceImpl extends ServiceImpl<OperatorLogMapper, OperatorLogEntity> implements OperatorLogService {

    @Resource
    private OrgService orgService;
    /**
     * 每天凌晨4点清理表里面过多的数据
     */
    @Scheduled(cron = "0 0 4 * * ?  ")
    public void clearData() {
        this.remove(new LambdaUpdateWrapper<OperatorLogEntity>()
                .le(OperatorLogEntity::getOperatorTime, LocalDateTime.now().minusDays(30 * 6)));
    }

    @Override
    public void saveOperatorLog(OperatorLogEntity entity) {
        save(entity);
    }

    @Override
    public DtoResult<BasePageInfoEntity<OperatorLogEntity>> listPage(OperatorLogEntity entity, BaseRequest baseRequest) {
        entity.setEnterpriseOrgId(JwtUtil.getEnterpriseOrgId());
        if (entity.getOrgId() == null) {
            entity.setOrgId(JwtUtil.getOrgId());
        }

        OrgEntity orgEntity = orgService.getById(entity.getOrgId());
        if (null == orgEntity) {
            return DtoResult.error("操作失败, 机构不存在");
        }
        entity.setOrgIdChain(orgEntity.getIdChain());

        try (Page<OperatorLogEntity> page = PageHelper.startPage(baseRequest.getPageNum(), baseRequest.getPageSize())) {
            super.baseMapper.getList(entity);
            return DtoResult.ok(new BasePageInfoEntity<>(page));
        }
    }
}
