package com.saida.services.system.websocket.appbc;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.saida.services.common.base.DtoResult;
import com.saida.services.common.config.redis.RedisMessagePublisher;
import com.saida.services.srv.dto.VlinkConvMsg;
import com.saida.services.srv.entity.SrvAppUserGroupDeviceEntity;
import com.saida.services.srv.entity.VisualDeviceEntity;
import com.saida.services.system.sys.service.AppUserGroupDeviceService;
import com.saida.services.system.visual.service.VisualDeviceService;
import com.saida.services.system.websocket.appbc.dto.AppBcWsMessageBase;
import com.saida.services.system.websocket.appbc.dto.AppBcWsMessageByCall;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 消息发送器
 */
@Slf4j
@Component
public class AppBcWebSocketServerSend {

    @Resource
    private RedisMessagePublisher redisMessagePublisher;
    @Resource
    private AppUserGroupDeviceService appUserGroupDeviceService;
    @Resource
    private VisualDeviceService visualDeviceService;


    public DtoResult<Void> sendMsgByVlinkConvMsg(VlinkConvMsg vlinkConvMsg) {
        VisualDeviceEntity visualDevice = visualDeviceService.getOne(new LambdaQueryWrapper<VisualDeviceEntity>()
                .eq(VisualDeviceEntity::getDeviceCodeCode, vlinkConvMsg.getDeviceCode())
                .last(" limit 1 "));
        if (ObjectUtil.isNull(visualDevice)) {
            log.error("sendMsgByVlinkConvMsg => 设备不存在 device:{}", vlinkConvMsg.getDeviceCode());
            return DtoResult.error("设备不存在");
        }
        List<SrvAppUserGroupDeviceEntity> userGroup = appUserGroupDeviceService.getUserGroupDeviceListByDeviceSn(vlinkConvMsg.getDeviceCode());
        if (CollectionUtil.isEmpty(userGroup)) {
            log.error("sendMsgByVlinkConvMsg => 用户不存在 device:{}", vlinkConvMsg.getDeviceCode());
            return DtoResult.error("用户不存在");
        }
        userGroup.forEach(userGroupDevice -> {
            AppBcWsMessageBase<AppBcWsMessageByCall> bcWsMessageDto = new AppBcWsMessageBase<>();
            bcWsMessageDto.setUserId(userGroupDevice.getUserId());
            bcWsMessageDto.setMsgId(vlinkConvMsg.getMsgId());
            if (vlinkConvMsg.getMsgType() == 1) {
                bcWsMessageDto.setType(3);
                bcWsMessageDto.setData(AppBcWsMessageByCall
                        .builder()
                        .deviceId(visualDevice.getId())
                        .build());
            } else {
                log.error("sendMsgByVlinkConvMsg => 消息类型错误 msgType:{}", vlinkConvMsg.getMsgType());
                return;
            }
            redisMessagePublisher.publish("SRV:ws_app_bc_" + userGroupDevice.getUserId(), JSON.toJSONString(bcWsMessageDto));
        });
        log.info("sendMsgByVlinkConvMsg => 发送消息成功 msg:{}", vlinkConvMsg);
        return DtoResult.ok();
    }

    @Scheduled(cron = "0 0/10 * * * ?")
    public void cleanExpiredConnections() {
        AppBcWebSocketServer.getWebSocketMap().forEach((k, v) -> {
            if (!v.getSession().isOpen()) {
                log.info("移除过期连接，用户ID: {}", k);
                AppBcWebSocketServer.getWebSocketMap().remove(k);
            }
        });
    }
}
