package com.saida.services.system.garden.util;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.saida.services.common.base.DtoResult;
import com.saida.services.common.config.ThreadPoolConfig;
import com.saida.services.common.entity.BaseEntity;
import com.saida.services.system.community.dto.PeopleInfoDto;
import com.saida.services.converge.qxNode.req.AddCarInfoGateReq;
import com.saida.services.converge.qxNode.req.DeleteCarInfoGateReq;
import com.saida.services.feign.open.system.IFeignOpenSystemApiController;
import com.saida.services.open.req.AddPeopleByHumanGateReq;
import com.saida.services.open.req.DeletePeopleByHumanGateReq;
import com.saida.services.system.community.dto.CarInfoDto;
import com.saida.services.system.constants.SrvThirdPartyPlatformsBizComponent;
import com.saida.services.system.garden.dto.GardenPersonDTO;
import com.saida.services.system.garden.entity.*;
import com.saida.services.system.garden.service.GardenEnterpriseService;
import com.saida.services.system.garden.service.GardenService;
import com.saida.services.system.visual.biz.vlink.component.SendToVlinkRequest;
import com.saida.services.system.visual.mapper.VisualDeviceAuthMapper;
import com.saida.services.system.visual.service.VisualDeviceTreeService;
import com.saida.services.system.visual.service.VisualSystemDeviceService;
import com.saida.services.system.visual.dto.DevicePageListByTreeDto;
import com.saida.services.system.visual.entity.VisualDeviceTreeEntity;
import com.saida.services.system.visual.vo.DeviceListPageVo;
import com.saida.services.srv.entity.VisualDeviceAuthEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 发送信息到设备工具类
 */
@Slf4j
@Component
public class GardenSendInfoToDeviceUtil {

    @Resource
    private SendToVlinkRequest sendToVlinkRequest;
    @Resource
    private VisualDeviceAuthMapper visualDeviceAuthMapper;
    @Resource
    private VisualSystemDeviceService visualSystemDeviceService;
    @Resource
    private VisualDeviceTreeService visualDeviceTreeService;
    @Resource
    private GardenService gardenService;
    @Resource
    private GardenEnterpriseService gardenEnterpriseService;
    @Resource
    private IFeignOpenSystemApiController iFeignOpenSystemApiController;

    /**
     * 上传人员信息到相关设备
     *
     * @param entity 人员信息实体对象
     * @return 如果所有设备都成功接收人员信息，则返回true；否则返回false
     */
    @Async(ThreadPoolConfig.TASK_EXECUTOR_BEAN_NAME)
    public void uploadPeopleInfo(BaseEntity entity) {
        GardenPersonDTO gardenPersonDTO = buildGardenPersonDTO(entity);
        if (gardenPersonDTO == null) {
            return;
        }
        // 查询设备授权信息列表
        List<VisualDeviceAuthEntity> authEntityList = visualDeviceAuthMapper.selectList(new LambdaQueryWrapper<VisualDeviceAuthEntity>()
                .eq(VisualDeviceAuthEntity::getComponentName, SrvThirdPartyPlatformsBizComponent.vlinkerOpenBizComponentName));
        // 如果授权信息列表不为空
        if (!authEntityList.isEmpty()) {
            // 获取第一个授权信息实体
            VisualDeviceAuthEntity visualDeviceAuthEntity = authEntityList.get(0);
            // 获取设备列表
            List<DeviceListPageVo> deviceList = getDeviceListByGardenEnterpriseId(gardenPersonDTO.getGardenEnterpriseId());
            // 遍历设备列表
            for (DeviceListPageVo deviceListPageVo : deviceList) {
                // 发送更新人员信息的请求，并获取结果
                boolean flag = sendUpdatePeopleInfo(gardenPersonDTO, deviceListPageVo.getDeviceCode(), visualDeviceAuthEntity);

            }
        }
    }


    /**
     * 上传人员信息到相关设备
     *
     * @param entity 人员信息实体对象
     * @return 如果所有设备都成功接收人员信息，则返回true；否则返回false
     */
    @Async(ThreadPoolConfig.TASK_EXECUTOR_BEAN_NAME)
    public void uploadPeopleInfo(List<String> deviceCodeList, BaseEntity entity) {
        GardenPersonDTO gardenPersonDTO = buildGardenPersonDTO(entity);
        if (gardenPersonDTO == null) {
            return;
        }
        // 查询设备授权信息列表
        List<VisualDeviceAuthEntity> authEntityList = visualDeviceAuthMapper.selectList(new LambdaQueryWrapper<VisualDeviceAuthEntity>()
                .eq(VisualDeviceAuthEntity::getComponentName, SrvThirdPartyPlatformsBizComponent.vlinkerOpenBizComponentName));
        // 如果授权信息列表不为空
        if (!authEntityList.isEmpty()) {
            // 获取第一个授权信息实体
            VisualDeviceAuthEntity visualDeviceAuthEntity = authEntityList.get(0);
            // 遍历设备列表
            for (String deviceCode : deviceCodeList) {
                // 发送更新人员信息的请求，并获取结果
                boolean flag = sendUpdatePeopleInfo(gardenPersonDTO, deviceCode, visualDeviceAuthEntity);

            }
        }
    }

    /**
     * 根据给定的BaseEntity对象构建GardenPersonDTO对象
     *
     * @param entity BaseEntity对象，可能是GardenPersonInfoEntity或GardenVisitorInfoEntity的实例
     * @return 构建的SchoolPersonDTO对象
     */
    private GardenPersonDTO buildGardenPersonDTO(BaseEntity entity) {
        GardenPersonDTO gardenPersonDTO = new GardenPersonDTO();
        if (entity instanceof GardenPersonInfoEntity) {
            GardenPersonInfoEntity gardenPersonInfoEntity = (GardenPersonInfoEntity) entity;
            gardenPersonDTO.setId(gardenPersonInfoEntity.getId());
            gardenPersonDTO.setGardenEnterpriseId(gardenPersonInfoEntity.getGardenEnterpriseId());
            gardenPersonDTO.setName(gardenPersonInfoEntity.getName());
            gardenPersonDTO.setImage(gardenPersonInfoEntity.getImage());
        } else if (entity instanceof GardenVisitorInfoEntity) {
            GardenVisitorInfoEntity gardenVisitorInfoEntity = (GardenVisitorInfoEntity) entity;
            gardenPersonDTO.setId(gardenVisitorInfoEntity.getId());
            gardenPersonDTO.setGardenEnterpriseId(gardenVisitorInfoEntity.getGardenEnterpriseId());
            gardenPersonDTO.setName(gardenVisitorInfoEntity.getName());
            gardenPersonDTO.setImage(gardenVisitorInfoEntity.getImage());
        } else {
            return null;
        }
        return gardenPersonDTO;
    }

    /**
     * 发送更新人员信息到Vlink的请求
     *
     * @param gardenPersonDTO        人员信息实体
     * @param deviceCode             设备编码
     * @param visualDeviceAuthEntity 视觉设备授权实体
     * @return 如果更新成功则返回true，否则返回false
     */
    private boolean sendUpdatePeopleInfo(GardenPersonDTO gardenPersonDTO, String deviceCode, VisualDeviceAuthEntity visualDeviceAuthEntity) {
        String resp;
        // 创建人员信息DTO对象
        PeopleInfoDto peopleInfoDto = new PeopleInfoDto();
        // 设置人员信息DTO对象的属性
        peopleInfoDto.setFaceUrl(gardenPersonDTO.getImage());
        peopleInfoDto.setName(gardenPersonDTO.getName());
        peopleInfoDto.setUserId("SD" + gardenPersonDTO.getId());
        peopleInfoDto.setDeviceId(deviceCode);
        try {
            AddPeopleByHumanGateReq addPeopleByHumanGateReq = new AddPeopleByHumanGateReq();
            addPeopleByHumanGateReq.setDeviceId(peopleInfoDto.getDeviceId());
            addPeopleByHumanGateReq.setUserId(peopleInfoDto.getUserId());
            addPeopleByHumanGateReq.setName(peopleInfoDto.getName());
            addPeopleByHumanGateReq.setFaceUrl(peopleInfoDto.getFaceUrl());
            addPeopleByHumanGateReq.setStartTime(peopleInfoDto.getStartTime());
            addPeopleByHumanGateReq.setEndTime(peopleInfoDto.getEndTime());
            DtoResult<Void> dtoResult = iFeignOpenSystemApiController.addOrUpdatePeople(addPeopleByHumanGateReq);
            if (dtoResult.success()) {
                return true;
            }
        } catch (Exception e) {
            // 记录日志，输出异常信息
            log.error("同步人员信息到闸机失败：" + e.getMessage());
            return false;
        }
        return false;
    }


    /**
     * 删除闸机人员信息
     *
     * @param baseEntity 人员信息实体列表
     */
    @Async(ThreadPoolConfig.TASK_EXECUTOR_BEAN_NAME)
    public void deletePeopleInfo(BaseEntity baseEntity) {
        // 查询设备授权信息列表
        List<VisualDeviceAuthEntity> authEntityList = visualDeviceAuthMapper.selectList(new LambdaQueryWrapper<VisualDeviceAuthEntity>()
                .eq(VisualDeviceAuthEntity::getComponentName, SrvThirdPartyPlatformsBizComponent.vlinkerOpenBizComponentName));
        // 如果授权信息列表不为空
        if (!authEntityList.isEmpty()) {
            // 获取第一个授权信息实体
            VisualDeviceAuthEntity visualDeviceAuthEntity = authEntityList.get(0);
            GardenPersonDTO gardenPersonDTO = buildGardenPersonDTO(baseEntity);
            if (gardenPersonDTO == null) {
                return;
            }
            // 获取设备列表
            List<DeviceListPageVo> deviceList = getDeviceListByGardenEnterpriseId(gardenPersonDTO.getGardenEnterpriseId());
            // 遍历设备列表
            for (DeviceListPageVo deviceListPageVo : deviceList) {
                // 创建人员信息DTO对象
                PeopleInfoDto peopleInfoDto = new PeopleInfoDto();
                // 设置人员ID
                peopleInfoDto.setUserId("SD" + gardenPersonDTO.getId());

                // 设置设备ID
                peopleInfoDto.setDeviceId(deviceListPageVo.getDeviceCode());
                String resp;
                try {
                    DeletePeopleByHumanGateReq deletePeopleByHumanGateReq = new DeletePeopleByHumanGateReq();
                    deletePeopleByHumanGateReq.setDeviceId(peopleInfoDto.getDeviceId());
                    deletePeopleByHumanGateReq.setUserId(peopleInfoDto.getUserId());
                    DtoResult<Void> dtoResult = iFeignOpenSystemApiController.deletePeople(deletePeopleByHumanGateReq);
                    // 记录日志，输出删除结果
                    log.info("删除人员信息到闸机...result=【{}】", JSON.toJSON(dtoResult));
                } catch (Exception e) {
                    // 记录日志，输出删除失败信息
                    log.error("删除人员信息到闸机失败...msg={}", e.getMessage(), e);
                }

            }
        }

    }

    /**
     * 删除闸机人员信息
     *
     * @param baseEntity 人员信息实体列表
     */
    @Async(ThreadPoolConfig.TASK_EXECUTOR_BEAN_NAME)
    public void deletePeopleInfo(BaseEntity baseEntity, String deviceCode) {
        // 查询设备授权信息列表
        List<VisualDeviceAuthEntity> authEntityList = visualDeviceAuthMapper.selectList(new LambdaQueryWrapper<VisualDeviceAuthEntity>()
                .eq(VisualDeviceAuthEntity::getComponentName, SrvThirdPartyPlatformsBizComponent.vlinkerOpenBizComponentName));
        // 如果授权信息列表不为空
        if (!authEntityList.isEmpty()) {
            // 获取第一个授权信息实体
            VisualDeviceAuthEntity visualDeviceAuthEntity = authEntityList.get(0);
            GardenPersonDTO gardenPersonDTO = buildGardenPersonDTO(baseEntity);
            if (gardenPersonDTO == null) {
                return;
            }
            // 创建人员信息DTO对象
            PeopleInfoDto peopleInfoDto = new PeopleInfoDto();
            // 设置人员ID
            peopleInfoDto.setUserId("SD" + gardenPersonDTO.getId());

            // 设置设备ID
            peopleInfoDto.setDeviceId(deviceCode);
            String resp;
            try {
                DeletePeopleByHumanGateReq deletePeopleByHumanGateReq = new DeletePeopleByHumanGateReq();
                deletePeopleByHumanGateReq.setDeviceId(peopleInfoDto.getDeviceId());
                deletePeopleByHumanGateReq.setUserId(peopleInfoDto.getUserId());
                DtoResult<Void> dtoResult = iFeignOpenSystemApiController.deletePeople(deletePeopleByHumanGateReq);
                // 记录日志，输出删除结果
                log.info("删除人员信息到闸机...result=【{}】", JSON.toJSON(dtoResult));
            } catch (Exception e) {
                // 记录日志，输出删除失败信息
                log.error("删除人员信息到闸机失败...msg={}", e.getMessage(), e);
            }
        }

    }

    /**
     * 根据人员信息获取设备列表
     *
     * @param id
     * @return 设备列表的视图对象列表
     */
    private List<DeviceListPageVo> getDeviceList(Long id) {
        List<DeviceListPageVo> list = new ArrayList<>();

        // 获取社区信息实体
        GardenEntity garden = gardenService.getById(id);
        if (garden != null) {
            // 根据社区ID获取视觉设备树实体
            VisualDeviceTreeEntity visualDeviceTreeEntity = visualDeviceTreeService.getById(garden.getVisualDeviceTreeId());
            if (visualDeviceTreeEntity != null) {
                // 创建设备列表查询条件对象
                DevicePageListByTreeDto devicePageListByTreeDto = new DevicePageListByTreeDto();
                // 设置应用场景
                devicePageListByTreeDto.setAppScene(visualDeviceTreeEntity.getAppScene());
                // 设置设备树ID
                devicePageListByTreeDto.setDeviceTreeId(visualDeviceTreeEntity.getId());
                // 设置组织ID
                devicePageListByTreeDto.setOrgId(visualDeviceTreeEntity.getOrgId());
                devicePageListByTreeDto.setGateType(1);
                // 根据设备树查询设备列表
                List<DeviceListPageVo> deviceListPageVos = visualSystemDeviceService.listByDeviceTree(devicePageListByTreeDto);
                // 将查询到的设备列表添加到结果列表中
                list.addAll(deviceListPageVos);
            }
        }
        // 返回设备列表
        return list;
    }

    private List<DeviceListPageVo> getDeviceListByGardenEnterpriseId(Long enterpriseId) {
        List<DeviceListPageVo> list = new ArrayList<>();
        // 获取社区信息实体
        GardenEnterpriseEntity gardenEnterpriseEntity = gardenEnterpriseService.getById(enterpriseId);
        if (gardenEnterpriseEntity != null) {
            // 根据社区ID获取视觉设备树实体
            VisualDeviceTreeEntity visualDeviceTreeEntity = visualDeviceTreeService.getById(gardenEnterpriseEntity.getVisualDeviceTreeId());
            if (visualDeviceTreeEntity != null) {
                // 创建设备列表查询条件对象
                DevicePageListByTreeDto devicePageListByTreeDto = new DevicePageListByTreeDto();
                // 设置应用场景
                devicePageListByTreeDto.setAppScene(visualDeviceTreeEntity.getAppScene());
                // 设置设备树ID
                devicePageListByTreeDto.setDeviceTreeId(visualDeviceTreeEntity.getId());
                // 设置组织ID
                devicePageListByTreeDto.setOrgId(visualDeviceTreeEntity.getOrgId());
                devicePageListByTreeDto.setGateType(1);
                // 根据设备树查询设备列表
                List<DeviceListPageVo> deviceListPageVos = visualSystemDeviceService.listByDeviceTree(devicePageListByTreeDto);
                // 将查询到的设备列表添加到结果列表中
                list.addAll(deviceListPageVos);
            }
        }
        // 返回设备列表
        return list;
    }

    /**
     * 发送更新车辆信息到Vlink的请求
     *
     * @param carInfoEntity          车辆信息实体
     * @param deviceCode             设备编码
     * @param visualDeviceAuthEntity 视觉设备授权实体
     * @return 如果更新成功则返回true，否则返回false
     */
    private boolean sendUpdatePeopleInfo(GardenCarInfoEntity carInfoEntity, String deviceCode, VisualDeviceAuthEntity visualDeviceAuthEntity, String action) {
        // 构建请求并发送，尝试删除车辆信息
        String resp;
        try {
            // 创建车辆信息传输对象
            CarInfoDto carInfoDto = new CarInfoDto();
            // 初始化传输对象的属性
            carInfoDto.setAction(action);
            carInfoDto.setDeviceId(deviceCode);
            carInfoDto.setLicensePlate(carInfoEntity.getPlate());
            // 获取并设置车牌颜色代码
            carInfoDto.setPlateColor("");
            carInfoDto.setPlateType("unknown");
            carInfoDto.setListType("allowList");

            AddCarInfoGateReq addCarInfoGateReq = new AddCarInfoGateReq();
            addCarInfoGateReq.setAction(action);
            addCarInfoGateReq.setDeviceId(deviceCode);
            addCarInfoGateReq.setLicensePlate(carInfoEntity.getPlate());
            addCarInfoGateReq.setPlateColor(carInfoDto.getPlateColor());
            addCarInfoGateReq.setPlateType(carInfoDto.getPlateType());
            addCarInfoGateReq.setListType(carInfoDto.getListType());
            DtoResult<Void> dtoResult = iFeignOpenSystemApiController.addOrUpdateCarInfo(addCarInfoGateReq);
            log.info("同步车辆信息到闸机...result=【{}】", JSON.toJSON(dtoResult));
            // 检查响应是否为空，不为空则进一步处理
            if (dtoResult.success()) {
                return true;
            }
        } catch (Exception e) {
            log.error("同步车辆信息到闸机失败...msg={}", e.getMessage(), e);
            return false;
        }
        return false;
    }

    /**
     * 向闸机上传车辆信息。
     *
     * @param entity 车辆信息实体类，包含需要上传的车辆详细信息。
     * @return 如果上传成功，返回true；如果上传失败或出现异常，返回false。
     */
    @Async(ThreadPoolConfig.TASK_EXECUTOR_BEAN_NAME)
    public boolean uploadCarInfo(GardenCarInfoEntity entity, String action) {
        GardenEnterpriseEntity gardenEnterpriseEntity = gardenEnterpriseService.getById(entity.getGardenEnterpriseId());
        // 获取设备列表
        List<DeviceListPageVo> deviceList = getDeviceListByGardenEnterpriseId(gardenEnterpriseEntity.getId());
        // 查询设备授权信息列表
        List<VisualDeviceAuthEntity> authEntityList = visualDeviceAuthMapper.selectList(new LambdaQueryWrapper<VisualDeviceAuthEntity>()
                .eq(VisualDeviceAuthEntity::getComponentName, SrvThirdPartyPlatformsBizComponent.vlinkerOpenBizComponentName));
        // 如果授权信息列表不为空
        if (!authEntityList.isEmpty()) {
            // 获取第一个授权信息实体
            VisualDeviceAuthEntity visualDeviceAuthEntity = authEntityList.get(0);
            // 遍历设备列表
            for (DeviceListPageVo deviceListPageVo : deviceList) {
                boolean flag = sendUpdatePeopleInfo(entity, deviceListPageVo.getDeviceCode(), visualDeviceAuthEntity, action);
                if (!flag) {
                    return false;
                }
            }
        }
        // 如果上传过程中没有异常，且响应代码为200，则认为上传成功
        return true;
    }


    /**
     * 向闸机上传删除车辆信息。
     * 该方法通过向Vlink发送请求来删除指定车辆的信息。它遍历一个车辆信息列表，为每个车辆构建一个请求DTO，并发送删除请求。
     * 如果请求成功，它会记录日志；如果请求失败，它也会记录相应的错误日志。
     *
     * @param list 车辆信息列表，每个元素代表一个需要被删除的车辆。
     */
    @Async(ThreadPoolConfig.TASK_EXECUTOR_BEAN_NAME)
    public void deleteCarInfo(List<GardenCarInfoEntity> list) {
        // 遍历车辆信息列表
        for (GardenCarInfoEntity carInfo : list) {
            GardenEnterpriseEntity gardenEnterpriseEntity = gardenEnterpriseService.getById(carInfo.getGardenEnterpriseId());
            // 获取设备列表
            List<DeviceListPageVo> deviceList = getDeviceListByGardenEnterpriseId(gardenEnterpriseEntity.getId());
            // 遍历设备列表
            for (DeviceListPageVo deviceListPageVo : deviceList) {
                // 初始化DTO对象，用于构造请求数据
                CarInfoDto carInfoDto = new CarInfoDto();
                // 设置设备ID为空，因为此处的设备ID并未使用
                carInfoDto.setDeviceId(deviceListPageVo.getDeviceCode());
                // 设置车牌号，来源于CarInfoEntity
                carInfoDto.setLicensePlate(carInfo.getPlate());
                // 获取车牌颜色代码，需要通过服务从数据库中查询
                carInfoDto.setPlateColor("");
                // 查询设备认证信息，用于构建请求数据
                try {
                    DeleteCarInfoGateReq deleteCarInfoGateReq = new DeleteCarInfoGateReq();
                    deleteCarInfoGateReq.setDeviceId(deviceListPageVo.getDeviceCode());
                    deleteCarInfoGateReq.setLicensePlate(carInfo.getPlate());
                    deleteCarInfoGateReq.setPlateColor(carInfoDto.getPlateColor());
                    DtoResult<Void> dtoResult = iFeignOpenSystemApiController.deleteCarInfo(deleteCarInfoGateReq);
                    // 记录删除成功的日志
                    log.info("删除车辆信息到闸机...result=【{}】", JSON.toJSON(dtoResult));
                } catch (Exception e) {
                    // 记录删除失败的日志
                    log.error("删除车辆信息到闸机失败...msg={}", e.getMessage(), e);
                }
            }
        }

    }
}
