package com.saida.services.system.visual.controller;

import com.saida.services.common.base.DtoResult;
import com.saida.services.common.entity.BasePageInfoEntity;
import com.saida.services.entities.base.BaseRequest;
import com.saida.services.srv.entity.SupermarketEntity;
import com.saida.services.system.visual.service.SupermarketService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;


@RestController
@RequestMapping("/supermarket")
public class SupermarketController {

    @Resource
    private SupermarketService supermarketService;

    @GetMapping("/list")
    public DtoResult<List<SupermarketEntity>> list(SupermarketEntity entity) {
        return supermarketService.list(entity);
    }

    @GetMapping("/listPage")
    public DtoResult<BasePageInfoEntity<SupermarketEntity>> listPage(SupermarketEntity entity, BaseRequest baseRequest) {
        return supermarketService.listPage(entity, baseRequest);
    }

    @GetMapping("/getSort")
    public DtoResult<Integer> getSort(SupermarketEntity entity) {
        return supermarketService.getSort(entity);
    }

    @GetMapping("/excelExport")
    public void excelExport(HttpServletResponse response, SupermarketEntity entity) {
        supermarketService.excelExport(response, entity);
    }

    @GetMapping("/info")
    public DtoResult<SupermarketEntity> info(@RequestParam("id") Long id) {
        return supermarketService.info(id);
    }

    @PostMapping("/add")
    public DtoResult<Void> add(@RequestBody SupermarketEntity entity) {
        return supermarketService.add(entity);
    }

    @PostMapping("/edit")
    public DtoResult<Void> edit(@RequestBody SupermarketEntity entity) {
        return supermarketService.edit(entity);
    }

    @PostMapping("/delete")
    public DtoResult<Void> delete(@RequestBody SupermarketEntity entity) {
        return supermarketService.delete(entity);
    }
}