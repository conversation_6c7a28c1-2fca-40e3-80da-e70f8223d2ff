package com.saida.services.system.bully.controller;

import com.saida.services.common.base.DtoResult;
import com.saida.services.feign.open.system.IFeignOpenIotSystemApiController;
import com.saida.services.open.req.bully.IotAlarmMediaResp;
import com.saida.services.open.req.bully.IotSetKeyAlarmReq;
import com.saida.services.open.req.bully.IotSetVolumeReq;
import com.saida.services.srv.entity.VisualDeviceEntity;
import com.saida.services.system.visual.service.VisualDeviceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("bullyDevice")
public class BullyDeviceController {
    @Resource
    private IFeignOpenIotSystemApiController feignOpenIotSystemApiController;
    @Resource
    private VisualDeviceService visualDeviceService;

    @PostMapping("setKeyAlarm")
    public DtoResult<Void> setKeyAlarm(@RequestBody IotSetKeyAlarmReq req) {
        VisualDeviceEntity byId = visualDeviceService.getById(req.getDeviceId());
        if (byId == null) {
            return DtoResult.error("设备不存在");
        }
        req.setDeviceId(byId.getDeviceCode());
        return feignOpenIotSystemApiController.setKeyAlarm(req);
    }

    @GetMapping("getKeyAlarm")
    public DtoResult<IotSetKeyAlarmReq> getKeyAlarm(String deviceId) {
        VisualDeviceEntity byId = visualDeviceService.getById(deviceId);
        if (byId == null) {
            return DtoResult.error("设备不存在");
        }
        deviceId = (byId.getDeviceCode());
        return feignOpenIotSystemApiController.getKeyAlarm(deviceId);
    }

    @PostMapping("setVolume")
    public DtoResult<Void> setVolume(@RequestBody IotSetVolumeReq req) {
        VisualDeviceEntity byId = visualDeviceService.getById(req.getDeviceId());
        if (byId == null) {
            return DtoResult.error("设备不存在");
        }
        req.setDeviceId(byId.getDeviceCode());
        return feignOpenIotSystemApiController.setVolume(req);
    }

    @GetMapping("getVolume")
    public DtoResult<IotSetVolumeReq> getVolume(String deviceId) {
        VisualDeviceEntity byId = visualDeviceService.getById(deviceId);
        if (byId == null) {
            return DtoResult.error("设备不存在");
        }
        return feignOpenIotSystemApiController.getVolume(byId.getDeviceCode());
    }

    @GetMapping("getAlarmMedia")
    public DtoResult<IotAlarmMediaResp> getAlarmMedia(String deviceId, Integer rowId) {
        VisualDeviceEntity byId = visualDeviceService.getById(deviceId);
        if (byId == null) {
            return DtoResult.error("设备不存在");
        }
        return feignOpenIotSystemApiController.getAlarmMedia(byId.getDeviceCode(), rowId);
    }
}
