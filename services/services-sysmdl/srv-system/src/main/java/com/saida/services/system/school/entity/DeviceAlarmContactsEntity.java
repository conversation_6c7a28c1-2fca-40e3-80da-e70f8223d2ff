package com.saida.services.system.school.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.saida.services.common.entity.BaseEntity;
import com.saida.services.tools.attr.DisplayField;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 设备告警通知人
 *
 * <AUTHOR>
 * email ${email}
 * date 2024-7-8 09:54:50
 */
@Data
@TableName("device_alarm_contacts")
public class DeviceAlarmContactsEntity extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 设备id
     */
    private Long deviceId;

    private String phone;

    private String name;

    private Integer isSms;

    private Integer type;
}
