package com.saida.services.system.visual.service.impl;

import com.saida.services.common.service.BaseServiceImpl;
import com.saida.services.system.visual.mapper.VisualCameraBoxMapper;
import com.saida.services.system.visual.service.VisualCameraBoxService;
import com.saida.services.system.visual.entity.VisualCameraBoxEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


@Slf4j
@Service
public class VisualCameraBoxServiceImpl extends BaseServiceImpl<VisualCameraBoxMapper, VisualCameraBoxEntity> implements VisualCameraBoxService {
    
}