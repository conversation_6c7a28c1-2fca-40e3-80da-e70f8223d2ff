package com.saida.services.system.visual.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.saida.services.common.base.Result;
import com.saida.services.common.entity.BasePageInfoEntity;
import com.saida.services.common.service.BaseServiceImpl;
import com.saida.services.common.tools.SmsUtil;
import com.saida.services.entities.base.BaseRequest;
import com.saida.services.system.config.SmsSignatureConfig;
import com.saida.services.system.sys.entity.OrgEntity;
import com.saida.services.system.sys.entity.UserEntity;
import com.saida.services.system.sys.service.AttributeDetailService;
import com.saida.services.system.sys.service.OrgService;
import com.saida.services.system.sys.service.UserService;
import com.saida.services.system.visual.enums.SmsTemplateEnum;
import com.saida.services.system.visual.enums.SmsTemplateFieldEnum;
import com.saida.services.system.visual.mapper.SmsLogsMapper;
import com.saida.services.system.visual.service.SmsLogsService;
import com.saida.services.system.visual.service.SmsTemplateService;
import com.saida.services.tools.attr.AttrUtil;
import com.saida.services.system.visual.dto.SmsLogsDto;
import com.saida.services.system.visual.entity.SmsLogsEntity;
import com.saida.services.system.visual.entity.SmsTemplateEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Slf4j
@Service("smsLogsService")
public class SmsLogsServiceImpl extends BaseServiceImpl<SmsLogsMapper, SmsLogsEntity> implements SmsLogsService {

    @Autowired
    private OrgService orgService;

    @Override
    public BasePageInfoEntity<SmsLogsEntity> listPage(SmsLogsDto smsLogsDto, BaseRequest request) {
        OrgEntity orgEntity = orgService.getById(smsLogsDto.getOrgId());
        if (null == orgEntity) {
            throw new RuntimeException("组织机构不存在");
        }
        smsLogsDto.setOrgIdChain(orgEntity.getIdChain());

        try (Page<SmsLogsEntity> page = PageHelper.startPage(request.getPageNum(), request.getPageSize())) {
            List<SmsLogsEntity> list = super.baseMapper.getList(smsLogsDto);
            if (CollectionUtil.isNotEmpty(list)) {
                List<OrgEntity> orgList = orgService.getOrgList(list.stream().map(SmsLogsEntity::getOrgId).distinct().collect(Collectors.toList()));
                Map<Object, Object> dic = new HashMap<>(orgList.stream().collect(Collectors.toMap(OrgEntity::getId, OrgEntity::getName)));
                list.replaceAll(e -> AttrUtil.putAttr(e, dic));
            }
            return new BasePageInfoEntity<>(page);
        }
    }


    @Override
    public SmsLogsEntity getInfoById(SmsLogsEntity entity) {
//            SmsLogsEntity entity = super.getById(id);
        entity = super.baseMapper.getInfoById(entity);
        return entity;
    }

    @Override
    public Result saveOrUpdateBean(SmsLogsEntity entity) {
        super.saveOrUpdate(entity);
        return Result.ok();
    }

    @Override
    public void deleteByIds(String ids) {
        super.removeByIds(Arrays.asList(ids.split(",")));
    }

    @Autowired
    private AttributeDetailService attributeDetailService;
    @Autowired
    private SmsTemplateService smsTemplateService;
    @Resource
    private UserService userService;

    /**
     * 发送短信
     *
     * @param smsTemplateEnum 短信模板
     * @param params          短信参数
     * @param userId          用户id
     */
    public void sendSms(SmsTemplateEnum smsTemplateEnum, Map<SmsTemplateFieldEnum, String> params, Long userId) {
        UserEntity user = userService.getInfo(userId);
        if (user == null || user.getEnterpriseOrgId() == null || user.getPhone() == null) {
            return;
        }
        sendSms(smsTemplateEnum, params, user.getName(), user.getPhone(), user.getEnterpriseOrgId());
    }

    @Resource
    private SmsSignatureConfig smsSignatureConfig;

    @Override
    public String sendSms(SmsTemplateEnum smsTemplateEnum, Map<SmsTemplateFieldEnum, String> params, String name, String phone, Long orgId) {
        Long smsTemId = attributeDetailService.getDicIdByTypeAndTag(smsTemplateEnum.type, smsTemplateEnum.tag);
        SmsTemplateEntity templateEntity = smsTemplateService.getAny(new LambdaQueryWrapper<SmsTemplateEntity>()
                .eq(SmsTemplateEntity::getType, smsTemId)
                .eq(SmsTemplateEntity::getIsEnable, 1));
        log.info("发送短信模板：{}，smsTemId：{}", templateEntity, smsTemId);
        if (templateEntity != null) {
            final String[] content = {smsSignatureConfig.getSignature() + " " + templateEntity.getContent()};
            params.keySet().forEach(e -> content[0] = content[0].replace(e.getCode(), params.get(e)));
            SmsLogsEntity build = SmsLogsEntity.builder()
                    .content(content[0])
                    .phone(phone)
                    .orgId(orgId)
                    .userName(name)
                    .status(1)
                    .build();
            try {
                String string = SmsUtil.sendSms(phone, content[0]);
                build.setResContent(string);
                return string;
            } catch (Exception e) {
                build.setStatus(2);
                log.error("发送短信失败", e);
            }
            super.save(build);
        }
        return null;
    }
}