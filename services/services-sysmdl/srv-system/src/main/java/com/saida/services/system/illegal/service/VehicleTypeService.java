package com.saida.services.system.illegal.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.saida.services.system.illegal.entity.VehicleTypeEntity;
import com.saida.services.system.illegal.vo.VehicleSecondTypeVo;
import com.saida.services.system.illegal.vo.VehicleTypeVo;

import java.util.List;

/**
 * 电子劝导员_车辆类型
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-02-14 13:54:39
 */
public interface VehicleTypeService extends IService<VehicleTypeEntity> {

    List<VehicleTypeVo> getParentList();


    List<VehicleSecondTypeVo> getSecondTypeList(Long id);
}

