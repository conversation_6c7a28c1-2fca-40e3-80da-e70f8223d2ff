package com.saida.services.system.visual.service;

import com.saida.services.algorithm.dto.InnerDeviceListDto;
import com.saida.services.common.base.DtoResult;
import com.saida.services.common.base.Result;
import com.saida.services.common.entity.BasePageInfoEntity;
import com.saida.services.common.service.IBaseService;
import com.saida.services.entities.base.BaseRequest;
import com.saida.services.srv.entity.VisualDeviceAuthEntity;
import com.saida.services.system.visual.dto.DeviceAuthPageListDto;
import com.saida.services.system.visual.vo.SyncOpenDeviceListVo;

/**
 * 设备表-广播设备
 *
 * <AUTHOR>
 * email ${email}
 * date 2023-06-07 10:53:48
 */
public interface VisualDeviceAuthService extends IBaseService<VisualDeviceAuthEntity> {

    BasePageInfoEntity<VisualDeviceAuthEntity> listPage(DeviceAuthPageListDto dto, BaseRequest request);

    Result list(DeviceAuthPageListDto dto);

    Result info(String id);

    Result add(VisualDeviceAuthEntity visualDeviceAuthEntity);

    DtoResult<SyncOpenDeviceListVo> syncOpenDeviceList(VisualDeviceAuthEntity visualDeviceAuthEntity, Long userId);

    DtoResult<SyncOpenDeviceListVo> getListDtoResultByOpen(InnerDeviceListDto innerDeviceListDto, VisualDeviceAuthEntity visualDeviceAuthEntity, Long userId);

    boolean belongConverge(Long deviceAuthId);
}