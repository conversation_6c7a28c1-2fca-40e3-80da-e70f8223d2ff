<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.saida.open</groupId>
    <artifactId>vlinkeropen</artifactId>
    <version>${revision}</version>
    <modules>
        <module>services</module>
    </modules>

    <packaging>pom</packaging>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.18</version>
        <relativePath/>
    </parent>

    <!--依赖版本管理-->
    <properties>
        <revision>1.4-SNAPSHOT</revision>

        <java.version>11</java.version>
        <spring-cloud.version>2021.0.4</spring-cloud.version>
        <cloud-alibaba.version>2021.0.4.0</cloud-alibaba.version>
        <mysql-connector-j.version>8.2.0</mysql-connector-j.version>
        <mybatis-plus.version>3.5.3.1</mybatis-plus.version>
        <kaptcha.version>2.3.2</kaptcha.version>
        <fastjson.version>1.2.83</fastjson.version>
        <hutool.version>5.8.25</hutool.version>
        <tomcat-embed-core.version>9.0.90</tomcat-embed-core.version>
        <logback-core.version>1.2.13</logback-core.version>
        <logback-classic.version>1.2.13</logback-classic.version>
        <commons-lang.version>2.6</commons-lang.version>
        <commons-collections.version>3.2.2</commons-collections.version>
        <commons-io.version>2.14.0</commons-io.version>
        <httpclientutil.version>1.0.4</httpclientutil.version>
        <netty-codec-http.version>4.1.115.Final</netty-codec-http.version>
        <netty-common.version>4.1.115.Final</netty-common.version>
        <nimbus-jose-jwt.version>9.37.3</nimbus-jose-jwt.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <joda.version>2.3</joda.version>
        <minio.version>7.1.4</minio.version>
        <sharding.jdbc.version>4.1.1</sharding.jdbc.version>
        <fastdfs.client.version>1.26.7</fastdfs.client.version>
        <dom4j.version>2.1.3</dom4j.version>
        <validation.version>2.0.1.Final</validation.version>
        <easyexcel.version>3.1.1</easyexcel.version>
        <pagehelper.version>1.4.1</pagehelper.version>
        <spring.retry.version>1.2.4.RELEASE</spring.retry.version>
        <jpush.api.version>3.5.5</jpush.api.version>
        <okhttp3.version>4.9.1</okhttp3.version>
        <maven.flatten.version>1.7.0</maven.flatten.version>
    </properties>

    <!--依赖声明-->
    <dependencyManagement>
        <dependencies>
            <!-- spring cloud -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <!--maven不支持多继承，使用 import 来依赖管理配置-->
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-core</artifactId>
                <version>${logback-core.version}</version>
            </dependency>

            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-classic</artifactId>
                <version>${logback-classic.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>logback-core</artifactId>
                        <groupId>ch.qos.logback</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- alibaba 依赖-->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.mysql</groupId>
                <artifactId>mysql-connector-j</artifactId>
                <version>${mysql-connector-j.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-core</artifactId>
                <version>${tomcat-embed-core.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-el</artifactId>
                <version>${tomcat-embed-core.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.tomcat</groupId>
                <artifactId>tomcat-annotations-api</artifactId>
                <version>${tomcat-embed-core.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-websocket</artifactId>
                <version>${tomcat-embed-core.version}</version>
            </dependency>

            <!--mybatis-plus启动器-->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-core</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-cron</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-http</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-crypto</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <!-- aliyun oss -->
            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>3.18.2</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-codec-http</artifactId>
                <version>${netty-codec-http.version}</version>
            </dependency>

            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-common</artifactId>
                <version>${netty-common.version}</version>
            </dependency>

            <!-- 工具类依赖 -->
            <dependency>
                <groupId>commons-lang</groupId>
                <artifactId>commons-lang</artifactId>
                <version>${commons-lang.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-collections</groupId>
                <artifactId>commons-collections</artifactId>
                <version>${commons-collections.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-bom</artifactId>
                <version>${hutool.version}</version>
                <type>pom</type>
                <!-- 注意这里是import -->
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>joda-time</groupId>
                <artifactId>joda-time</artifactId>
                <version>${joda.version}</version>
            </dependency>
            <dependency>
                <groupId>io.minio</groupId>
                <artifactId>minio</artifactId>
                <version>${minio.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.shardingsphere</groupId>
                <artifactId>sharding-jdbc-spring-boot-starter</artifactId>
                <version>${sharding.jdbc.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.tobato</groupId>
                <artifactId>fastdfs-client</artifactId>
                <version>${fastdfs.client.version}</version>
            </dependency>

            <dependency>
                <groupId>org.dom4j</groupId>
                <artifactId>dom4j</artifactId>
                <version>${dom4j.version}</version>
            </dependency>

            <dependency>
                <groupId>javax.validation</groupId>
                <artifactId>validation-api</artifactId>
                <version>${validation.version}</version>
            </dependency>

            <dependency>
                <groupId>com.nimbusds</groupId>
                <artifactId>nimbus-jose-jwt</artifactId>
                <version>${nimbus-jose-jwt.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${pagehelper.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.retry</groupId>
                <artifactId>spring-retry</artifactId>
                <version>${spring.retry.version}</version>
            </dependency>

            <dependency>
                <groupId>com.saida</groupId>
                <artifactId>service-tyvisual-video</artifactId>
                <version>1.2.1</version>
            </dependency>

            <dependency>
                <groupId>cn.jpush.api</groupId>
                <artifactId>jpush-client</artifactId>
                <version>${jpush.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp3.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>


    <distributionManagement>
        <repository>
            <id>nexus-maven</id>
            <url>https://mvn.sdvideo.cn/repository/maven-releases/</url>
            <name>Release Repository</name>
        </repository>

        <snapshotRepository>
            <id>nexus-maven-snapshots</id>
            <url>https://mvn.sdvideo.cn/repository/maven-snapshots/</url>
            <name>Snapshot Repository</name>
        </snapshotRepository>
    </distributionManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.10.1</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
            <!--springboot 打包插件 ,不然报错 Unable to find main class
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            -->

            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>${maven.flatten.version}</version>
                <inherited>true</inherited>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                        <configuration>
                            <updatePomFile>true</updatePomFile>
                            <flattenMode>resolveCiFriendliesOnly</flattenMode>
                            <pomElements>
                                <parent>expand</parent>
                                <distributionManagement>remove</distributionManagement>
                                <repositories>remove</repositories>
                            </pomElements>
                        </configuration>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>

        <resources>
            <resource>
                <!--编译时，默认情况下不会将    mapper.xml文件编译进去，
                src/main/java 资源文件的路径，
                **/*.xml 需要编译打包的文件类型是xml文件，
                -->
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
        </resources>
    </build>
</project>